<persistence xmlns="http://java.sun.com/xml/ns/persistence" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xsi:schemaLocation="http://java.sun.com/xml/ns/persistence http://java.sun.com/xml/ns/persistence/persistence_2_0.xsd"
             version="2.0">
    <persistence-unit name="zstack.jpa" transaction-type="RESOURCE_LOCAL">
        <mapping-file>persistence-mapping.xml</mapping-file>

        <class>org.zstack.core.checkpoint.CheckPointVO</class>
        <class>org.zstack.core.checkpoint.CheckPointEntryVO</class>
        <class>org.zstack.core.workflow.WorkFlowChainVO</class>
        <class>org.zstack.core.workflow.WorkFlowVO</class>
        <class>org.zstack.core.keyvalue.KeyValueBinaryVO</class>
        <class>org.zstack.core.keyvalue.KeyValueVO</class>
        <class>org.zstack.core.job.JobQueueVO</class>
        <class>org.zstack.core.job.JobQueueEntryVO</class>
        <class>org.zstack.core.config.GlobalConfigVO</class>
        <class>org.zstack.core.eventlog.EventLogVO</class>
        <class>org.zstack.core.plugin.PluginDriverVO</class>
        <class>org.zstack.resourceconfig.ResourceConfigVO</class>
        <class>org.zstack.header.managementnode.ManagementNodeVO</class>
        <class>org.zstack.header.managementnode.ManagementNodeContextVO</class>
        <class>org.zstack.header.zone.ZoneVO</class>
        <class>org.zstack.header.zone.ZoneEO</class>
        <class>org.zstack.header.cluster.ClusterVO</class>
        <class>org.zstack.header.cluster.ClusterEO</class>
        <class>org.zstack.header.host.HostVO</class>
        <class>org.zstack.header.host.HostEO</class>
        <class>org.zstack.header.host.HostNetworkLabelVO</class>
        <class>org.zstack.header.host.CpuFeaturesHistoryVO</class>
        <class>org.zstack.header.storage.primary.PrimaryStorageVO</class>
        <class>org.zstack.header.storage.primary.PrimaryStorageEO</class>
        <class>org.zstack.header.storage.primary.PrimaryStorageClusterRefVO</class>
        <class>org.zstack.header.storage.primary.PrimaryStorageCapacityVO</class>
        <class>org.zstack.header.storage.primary.PrimaryStorageHostRefVO</class>
        <class>org.zstack.header.storage.backup.BackupStorageVO</class>
        <class>org.zstack.header.storage.backup.BackupStorageEO</class>
        <class>org.zstack.header.storage.backup.BackupStorageZoneRefVO</class>
        <class>org.zstack.header.image.ImageVO</class>
        <class>org.zstack.header.image.ImageEO</class>
        <class>org.zstack.header.image.GuestOsCategoryVO</class>
        <class>org.zstack.header.image.ImageBackupStorageRefVO</class>
        <class>org.zstack.header.allocator.HostCapacityVO</class>
        <class>org.zstack.header.configuration.InstanceOfferingVO</class>
        <class>org.zstack.header.configuration.InstanceOfferingEO</class>
        <class>org.zstack.header.configuration.DiskOfferingVO</class>
        <class>org.zstack.header.configuration.DiskOfferingEO</class>
        <class>org.zstack.header.volume.VolumeVO</class>
        <class>org.zstack.header.volume.VolumeEO</class>
        <class>org.zstack.header.network.l2.L2NetworkVO</class>
        <class>org.zstack.header.network.l2.L2NetworkEO</class>
        <class>org.zstack.header.network.l2.L2VlanNetworkVO</class>
        <class>org.zstack.network.l2.vxlan.vxlanNetworkPool.VxlanNetworkPoolVO</class>
        <class>org.zstack.network.l2.vxlan.vxlanNetwork.VxlanNetworkVO</class>
        <class>org.zstack.network.l2.vxlan.vtep.VtepVO</class>
        <class>org.zstack.network.l2.vxlan.vtep.RemoteVtepVO</class>
        <class>org.zstack.network.l2.vxlan.vxlanNetworkPool.VniRangeVO</class>
        <class>org.zstack.header.network.l2.L2NetworkClusterRefVO</class>
        <class>org.zstack.header.network.l2.L2NetworkHostRefVO</class>
        <class>org.zstack.header.network.l3.L3NetworkVO</class>
        <class>org.zstack.header.network.l3.L3NetworkEO</class>
        <class>org.zstack.header.network.l3.L3NetworkDnsVO</class>
        <class>org.zstack.header.network.l3.L3NetworkHostRouteVO</class>
        <class>org.zstack.header.network.l3.IpRangeVO</class>
        <class>org.zstack.header.network.l3.IpRangeEO</class>
        <class>org.zstack.header.network.l3.UsedIpVO</class>
        <class>org.zstack.header.network.l3.AddressPoolVO</class>
        <class>org.zstack.header.network.l3.NormalIpRangeVO</class>
        <class>org.zstack.header.network.l3.ReservedIpRangeVO</class>
        <class>org.zstack.network.hostNetworkInterface.HostNetworkBondingVO</class>
        <class>org.zstack.network.hostNetworkInterface.HostNetworkInterfaceVO</class>
        <class>org.zstack.network.service.vip.VipVO</class>
        <class>org.zstack.network.service.vip.VipNetworkServicesRefVO</class>
        <class>org.zstack.network.service.vip.VipPeerL3NetworkRefVO</class>
        <class>org.zstack.network.service.eip.EipVO</class>
        <class>org.zstack.network.service.virtualrouter.vip.VirtualRouterVipVO</class>
        <class>org.zstack.network.service.virtualrouter.eip.VirtualRouterEipRefVO</class>
        <class>org.zstack.header.network.service.NetworkServiceProviderVO</class>
        <class>org.zstack.header.network.service.NetworkServiceL3NetworkRefVO</class>
        <class>org.zstack.header.network.service.NetworkServiceProviderL2NetworkRefVO</class>
        <class>org.zstack.header.network.service.NetworkServiceTypeVO</class>
        <class>org.zstack.header.vm.VmInstanceVO</class>
        <class>org.zstack.header.vm.VmInstanceEO</class>
        <class>org.zstack.header.vm.VmInstanceSequenceNumberVO</class>
        <class>org.zstack.header.vm.VmCrashHistoryVO</class>
        <class>org.zstack.appliancevm.ApplianceVmVO</class>
        <class>org.zstack.appliancevm.ApplianceVmFirewallRuleVO</class>
        <class>org.zstack.header.vm.VmNicVO</class>
        <class>org.zstack.header.vm.VmSchedHistoryVO</class>
        <class>org.zstack.header.identity.SessionVO</class>
        <class>org.zstack.header.identity.AccountVO</class>
        <class>org.zstack.header.identity.AccountResourceRefVO</class>
        <class>org.zstack.header.identity.UserVO</class>
        <class>org.zstack.header.identity.PolicyVO</class>
        <class>org.zstack.header.identity.UserPolicyRefVO</class>
        <class>org.zstack.header.identity.UserGroupVO</class>
        <class>org.zstack.header.identity.UserGroupPolicyRefVO</class>
        <class>org.zstack.header.identity.UserGroupUserRefVO</class>
        <class>org.zstack.header.identity.SharedResourceVO</class>
        <class>org.zstack.header.identity.QuotaVO</class>
        <class>org.zstack.header.search.DeleteVO</class>
        <class>org.zstack.header.search.InsertVO</class>
        <class>org.zstack.header.search.UpdateVO</class>
        <class>org.zstack.kvm.KVMHostVO</class>
        <class>org.zstack.kvm.hypervisor.datatype.KvmHypervisorInfoVO</class>
        <class>org.zstack.kvm.hypervisor.datatype.HostOsCategoryVO</class>
        <class>org.zstack.kvm.hypervisor.datatype.KvmHostHypervisorMetadataVO</class>
        <class>org.zstack.storage.backup.sftp.SftpBackupStorageVO</class>
        <class>org.zstack.storage.backup.imagestore.ImageStoreBackupStorageVO</class>
        <class>org.zstack.header.simulator.SimulatorHostVO</class>
        <class>org.zstack.header.storage.primary.ImageCacheVO</class>
        <class>org.zstack.network.service.virtualrouter.VirtualRouterOfferingVO</class>
        <class>org.zstack.network.service.virtualrouter.VirtualRouterBootstrapIsoVO</class>
        <class>org.zstack.network.securitygroup.SecurityGroupRuleVO</class>
        <class>org.zstack.network.securitygroup.SecurityGroupL3NetworkRefVO</class>
        <class>org.zstack.network.securitygroup.SecurityGroupVO</class>
        <class>org.zstack.network.securitygroup.SecurityGroupFailureHostVO</class>
        <class>org.zstack.network.securitygroup.VmNicSecurityGroupRefVO</class>
        <class>org.zstack.network.securitygroup.VmNicSecurityPolicyVO</class>
        <class>org.zstack.network.securitygroup.SecurityGroupSequenceNumberVO</class>
        <class>org.zstack.network.service.portforwarding.PortForwardingRuleVO</class>
        <class>org.zstack.network.service.virtualrouter.portforwarding.VirtualRouterPortForwardingRuleRefVO</class>
        <class>org.zstack.header.console.ConsoleProxyVO</class>
        <class>org.zstack.header.storage.snapshot.VolumeSnapshotTreeVO</class>
        <class>org.zstack.header.storage.snapshot.VolumeSnapshotTreeEO</class>
        <class>org.zstack.header.storage.snapshot.VolumeSnapshotEO</class>
        <class>org.zstack.header.storage.snapshot.VolumeSnapshotVO</class>
        <class>org.zstack.header.storage.snapshot.reference.VolumeSnapshotReferenceVO</class>
        <class>org.zstack.header.storage.snapshot.reference.VolumeSnapshotReferenceTreeVO</class>
        <class>org.zstack.header.storage.snapshot.VolumeSnapshotBackupStorageRefVO</class>
        <class>org.zstack.header.storage.snapshot.group.VolumeSnapshotGroupVO</class>
        <class>org.zstack.header.storage.snapshot.group.VolumeSnapshotGroupRefVO</class>
        <class>org.zstack.header.tag.UserTagVO</class>
        <class>org.zstack.header.tag.SystemTagVO</class>
        <class>org.zstack.header.tag.TagPatternVO</class>
        <class>org.zstack.network.service.virtualrouter.VirtualRouterVmVO</class>
        <class>org.zstack.network.service.virtualrouter.VirtualRouterMetadataVO</class>
        <class>org.zstack.network.service.virtualrouter.VirtualRouterSoftwareVersionVO</class>
        <class>org.zstack.storage.primary.local.LocalStorageResourceRefVO</class>
        <class>org.zstack.storage.primary.local.LocalStorageHostRefVO</class>
        <class>org.zstack.header.storage.addon.primary.ExternalPrimaryStorageVO</class>
        <class>org.zstack.header.storage.addon.primary.PrimaryStorageOutputProtocolRefVO</class>
        <class>org.zstack.header.storage.addon.primary.ExternalPrimaryStorageHostRefVO</class>
        <class>org.zstack.storage.ceph.backup.CephBackupStorageVO</class>
        <class>org.zstack.storage.ceph.backup.CephBackupStorageMonVO</class>
        <class>org.zstack.storage.ceph.primary.CephPrimaryStorageMonVO</class>
        <class>org.zstack.storage.ceph.primary.CephPrimaryStorageVO</class>
        <class>org.zstack.storage.ceph.CephCapacityVO</class>
        <class>org.zstack.core.gc.GarbageCollectorVO</class>
        <class>org.zstack.header.storage.primary.ImageCacheVolumeRefVO</class>
        <class>org.zstack.header.acl.AccessControlListEntryVO</class>
        <class>org.zstack.header.acl.AccessControlListVO</class>
        <class>org.zstack.network.service.virtualrouter.lb.VirtualRouterLoadBalancerRefVO</class>
        <class>org.zstack.network.service.lb.LoadBalancerVO</class>
        <class>org.zstack.network.service.lb.LoadBalancerListenerACLRefVO</class>
        <class>org.zstack.network.service.lb.LoadBalancerListenerVmNicRefVO</class>
        <class>org.zstack.network.service.lb.LoadBalancerListenerVO</class>
        <class>org.zstack.network.service.lb.LoadBalancerServerGroupVO</class>
        <class>org.zstack.network.service.lb.LoadBalancerServerGroupVmNicRefVO</class>
        <class>org.zstack.network.service.lb.LoadBalancerServerGroupServerIpVO</class>
        <class>org.zstack.network.service.lb.LoadBalancerListenerServerGroupRefVO</class>
        <class>org.zstack.network.service.lb.CertificateVO</class>
        <class>org.zstack.network.service.lb.LoadBalancerListenerCertificateRefVO</class>
        <class>org.zstack.header.console.ConsoleProxyAgentVO</class>
        <class>org.zstack.header.storage.primary.ImageCacheShadowVO</class>
        <class>org.zstack.ldap.LdapServerVO</class>
        <class>org.zstack.ldap.LdapAccountRefVO</class>
        <class>org.zstack.core.jsonlabel.JsonLabelVO</class>
        <class>org.zstack.rest.AsyncRestVO</class>
        <class>org.zstack.storage.ceph.primary.CephPrimaryStoragePoolVO</class>
        <class>org.zstack.header.core.progress.TaskProgressVO</class>
        <class>org.zstack.header.vo.ResourceVO</class>
        <class>org.zstack.header.core.webhooks.WebhookVO</class>
        <class>org.zstack.header.longjob.LongJobVO</class>
        <class>org.zstack.header.identity.role.RoleAccountRefVO</class>
        <class>org.zstack.header.identity.role.RolePolicyRefVO</class>
        <class>org.zstack.header.identity.role.RoleUserGroupRefVO</class>
        <class>org.zstack.header.identity.role.RoleUserRefVO</class>
        <class>org.zstack.header.identity.role.RoleVO</class>
        <class>org.zstack.header.identity.role.SystemRoleVO</class>
        <class>org.zstack.header.identity.role.RolePolicyStatementVO</class>
        <class>org.zstack.header.core.captcha.CaptchaVO</class>
        <class>org.zstack.header.vm.cdrom.VmCdRomVO</class>
        <class>org.zstack.header.core.trash.InstallPathRecycleVO</class>
        <class>org.zstack.header.vm.VmPriorityConfigVO</class>
        <class>org.zstack.header.host.HostPortVO</class>
        <class>org.zstack.header.allocator.HostAllocatedCpuVO</class>
        <class>org.zstack.header.vm.VmInstanceNumaNodeVO</class>
        <class>org.zstack.header.host.HostNumaNodeVO</class>
        <class>org.zstack.header.core.encrypt.EncryptionIntegrityVO</class>
        <class>org.zstack.storage.primary.sharedblock.SharedBlockCapacityVO</class>
        <class>org.zstack.header.vm.devices.VmInstanceDeviceAddressVO</class>
        <class>org.zstack.header.vm.devices.VmInstanceDeviceAddressArchiveVO</class>
        <class>org.zstack.header.vm.devices.VmInstanceDeviceAddressGroupVO</class>
        <class>org.zstack.header.core.encrypt.EncryptEntityMetadataVO</class>
        <class>org.zstack.storage.ceph.primary.CephOsdGroupVO</class>
        <class>org.zstack.header.network.sdncontroller.SdnControllerVO</class>
        <class>org.zstack.sdnController.header.HardwareL2VxlanNetworkPoolVO</class>
        <class>org.zstack.sdnController.header.HardwareL2VxlanNetworkVO</class>
        <class>org.zstack.sdnController.header.VxlanHostMappingVO</class>
        <class>org.zstack.sdnController.header.VxlanClusterMappingVO</class>
        <class>org.zstack.header.network.sdncontroller.SdnControllerHostRefVO</class>
        <class>org.zstack.header.volume.VolumeHostRefVO</class>
        <class>org.zstack.directory.DirectoryVO</class>
        <class>org.zstack.directory.ResourceDirectoryRefVO</class>
        <class>org.zstack.core.upgrade.AgentVersionVO</class>
        <class>org.zstack.header.host.HostIpmiVO</class>
        <class>org.zstack.header.sshkeypair.SshKeyPairVO</class>
        <class>org.zstack.header.sshkeypair.SshKeyPairRefVO</class>
        <class>org.zstack.header.storage.primary.PrimaryStorageHistoricalUsageVO</class>
        <class>org.zstack.storage.ceph.primary.CephOsdGroupHistoricalUsageVO</class>
        <class>org.zstack.storage.primary.local.LocalStorageHostHistoricalUsageVO</class>
        <class>org.zstack.network.hostNetworkInterface.lldp.entity.HostNetworkInterfaceLldpVO </class>
        <class>org.zstack.network.hostNetworkInterface.lldp.entity.HostNetworkInterfaceLldpRefVO</class>
        <class>org.zstack.header.volume.block.BlockVolumeVO</class>
        <class>org.zstack.header.host.HostHwMonitorStatusVO</class>
        <class>org.zstack.kvm.xmlhook.XmlHookVO</class>
        <class>org.zstack.kvm.xmlhook.XmlHookVmInstanceRefVO</class>
        <class>org.zstack.log.server.LogServerVO</class>
        <class>org.zstack.network.hostNetworkInterface.PhysicalSwitchVO</class>
        <class>org.zstack.network.hostNetworkInterface.PhysicalSwitchPortVO</class>
    </persistence-unit>
</persistence>
