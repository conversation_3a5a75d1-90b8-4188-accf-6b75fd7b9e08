package org.zstack.sdk;

import org.zstack.sdk.HwMonitorStatus;
import org.zstack.sdk.HwMonitorStatus;
import org.zstack.sdk.HwMonitorStatus;
import org.zstack.sdk.HwMonitorStatus;
import org.zstack.sdk.HwMonitorStatus;
import org.zstack.sdk.HwMonitorStatus;
import org.zstack.sdk.HwMonitorStatus;
import org.zstack.sdk.HwMonitorStatus;
import org.zstack.sdk.HwMonitorStatus;

public class HostInventory  {

    public java.lang.String zoneUuid;
    public void setZoneUuid(java.lang.String zoneUuid) {
        this.zoneUuid = zoneUuid;
    }
    public java.lang.String getZoneUuid() {
        return this.zoneUuid;
    }

    public java.lang.String name;
    public void setName(java.lang.String name) {
        this.name = name;
    }
    public java.lang.String getName() {
        return this.name;
    }

    public java.lang.String uuid;
    public void setUuid(java.lang.String uuid) {
        this.uuid = uuid;
    }
    public java.lang.String getUuid() {
        return this.uuid;
    }

    public java.lang.String clusterUuid;
    public void setClusterUuid(java.lang.String clusterUuid) {
        this.clusterUuid = clusterUuid;
    }
    public java.lang.String getClusterUuid() {
        return this.clusterUuid;
    }

    public java.lang.String description;
    public void setDescription(java.lang.String description) {
        this.description = description;
    }
    public java.lang.String getDescription() {
        return this.description;
    }

    public java.lang.String managementIp;
    public void setManagementIp(java.lang.String managementIp) {
        this.managementIp = managementIp;
    }
    public java.lang.String getManagementIp() {
        return this.managementIp;
    }

    public java.lang.String hypervisorType;
    public void setHypervisorType(java.lang.String hypervisorType) {
        this.hypervisorType = hypervisorType;
    }
    public java.lang.String getHypervisorType() {
        return this.hypervisorType;
    }

    public java.lang.String state;
    public void setState(java.lang.String state) {
        this.state = state;
    }
    public java.lang.String getState() {
        return this.state;
    }

    public java.lang.String status;
    public void setStatus(java.lang.String status) {
        this.status = status;
    }
    public java.lang.String getStatus() {
        return this.status;
    }

    public java.lang.Long totalCpuCapacity;
    public void setTotalCpuCapacity(java.lang.Long totalCpuCapacity) {
        this.totalCpuCapacity = totalCpuCapacity;
    }
    public java.lang.Long getTotalCpuCapacity() {
        return this.totalCpuCapacity;
    }

    public java.lang.Long availableCpuCapacity;
    public void setAvailableCpuCapacity(java.lang.Long availableCpuCapacity) {
        this.availableCpuCapacity = availableCpuCapacity;
    }
    public java.lang.Long getAvailableCpuCapacity() {
        return this.availableCpuCapacity;
    }

    public java.lang.Integer cpuSockets;
    public void setCpuSockets(java.lang.Integer cpuSockets) {
        this.cpuSockets = cpuSockets;
    }
    public java.lang.Integer getCpuSockets() {
        return this.cpuSockets;
    }

    public java.lang.Long totalMemoryCapacity;
    public void setTotalMemoryCapacity(java.lang.Long totalMemoryCapacity) {
        this.totalMemoryCapacity = totalMemoryCapacity;
    }
    public java.lang.Long getTotalMemoryCapacity() {
        return this.totalMemoryCapacity;
    }

    public java.lang.Long availableMemoryCapacity;
    public void setAvailableMemoryCapacity(java.lang.Long availableMemoryCapacity) {
        this.availableMemoryCapacity = availableMemoryCapacity;
    }
    public java.lang.Long getAvailableMemoryCapacity() {
        return this.availableMemoryCapacity;
    }

    public java.lang.Integer cpuNum;
    public void setCpuNum(java.lang.Integer cpuNum) {
        this.cpuNum = cpuNum;
    }
    public java.lang.Integer getCpuNum() {
        return this.cpuNum;
    }

    public java.lang.String ipmiAddress;
    public void setIpmiAddress(java.lang.String ipmiAddress) {
        this.ipmiAddress = ipmiAddress;
    }
    public java.lang.String getIpmiAddress() {
        return this.ipmiAddress;
    }

    public java.lang.String ipmiUsername;
    public void setIpmiUsername(java.lang.String ipmiUsername) {
        this.ipmiUsername = ipmiUsername;
    }
    public java.lang.String getIpmiUsername() {
        return this.ipmiUsername;
    }

    public java.lang.Integer ipmiPort;
    public void setIpmiPort(java.lang.Integer ipmiPort) {
        this.ipmiPort = ipmiPort;
    }
    public java.lang.Integer getIpmiPort() {
        return this.ipmiPort;
    }

    public java.lang.String ipmiPowerStatus;
    public void setIpmiPowerStatus(java.lang.String ipmiPowerStatus) {
        this.ipmiPowerStatus = ipmiPowerStatus;
    }
    public java.lang.String getIpmiPowerStatus() {
        return this.ipmiPowerStatus;
    }

    public HwMonitorStatus cpuStatus;
    public void setCpuStatus(HwMonitorStatus cpuStatus) {
        this.cpuStatus = cpuStatus;
    }
    public HwMonitorStatus getCpuStatus() {
        return this.cpuStatus;
    }

    public HwMonitorStatus memoryStatus;
    public void setMemoryStatus(HwMonitorStatus memoryStatus) {
        this.memoryStatus = memoryStatus;
    }
    public HwMonitorStatus getMemoryStatus() {
        return this.memoryStatus;
    }

    public HwMonitorStatus diskStatus;
    public void setDiskStatus(HwMonitorStatus diskStatus) {
        this.diskStatus = diskStatus;
    }
    public HwMonitorStatus getDiskStatus() {
        return this.diskStatus;
    }

    public HwMonitorStatus nicStatus;
    public void setNicStatus(HwMonitorStatus nicStatus) {
        this.nicStatus = nicStatus;
    }
    public HwMonitorStatus getNicStatus() {
        return this.nicStatus;
    }

    public HwMonitorStatus gpuStatus;
    public void setGpuStatus(HwMonitorStatus gpuStatus) {
        this.gpuStatus = gpuStatus;
    }
    public HwMonitorStatus getGpuStatus() {
        return this.gpuStatus;
    }

    public HwMonitorStatus powerSupplyStatus;
    public void setPowerSupplyStatus(HwMonitorStatus powerSupplyStatus) {
        this.powerSupplyStatus = powerSupplyStatus;
    }
    public HwMonitorStatus getPowerSupplyStatus() {
        return this.powerSupplyStatus;
    }

    public HwMonitorStatus fanStatus;
    public void setFanStatus(HwMonitorStatus fanStatus) {
        this.fanStatus = fanStatus;
    }
    public HwMonitorStatus getFanStatus() {
        return this.fanStatus;
    }

    public HwMonitorStatus raidStatus;
    public void setRaidStatus(HwMonitorStatus raidStatus) {
        this.raidStatus = raidStatus;
    }
    public HwMonitorStatus getRaidStatus() {
        return this.raidStatus;
    }

    public HwMonitorStatus temperatureStatus;
    public void setTemperatureStatus(HwMonitorStatus temperatureStatus) {
        this.temperatureStatus = temperatureStatus;
    }
    public HwMonitorStatus getTemperatureStatus() {
        return this.temperatureStatus;
    }

    public java.lang.String architecture;
    public void setArchitecture(java.lang.String architecture) {
        this.architecture = architecture;
    }
    public java.lang.String getArchitecture() {
        return this.architecture;
    }

    public java.sql.Timestamp createDate;
    public void setCreateDate(java.sql.Timestamp createDate) {
        this.createDate = createDate;
    }
    public java.sql.Timestamp getCreateDate() {
        return this.createDate;
    }

    public java.sql.Timestamp lastOpDate;
    public void setLastOpDate(java.sql.Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }
    public java.sql.Timestamp getLastOpDate() {
        return this.lastOpDate;
    }

}
