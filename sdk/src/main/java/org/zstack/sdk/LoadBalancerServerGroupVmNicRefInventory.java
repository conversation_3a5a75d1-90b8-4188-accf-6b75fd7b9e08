package org.zstack.sdk;



public class LoadBalancerServerGroupVmNicRefInventory  {

    public java.lang.Long id;
    public void setId(java.lang.Long id) {
        this.id = id;
    }
    public java.lang.Long getId() {
        return this.id;
    }

    public java.lang.String serverGroupUuid;
    public void setServerGroupUuid(java.lang.String serverGroupUuid) {
        this.serverGroupUuid = serverGroupUuid;
    }
    public java.lang.String getServerGroupUuid() {
        return this.serverGroupUuid;
    }

    public java.lang.String vmNicUuid;
    public void setVmNicUuid(java.lang.String vmNicUuid) {
        this.vmNicUuid = vmNicUuid;
    }
    public java.lang.String getVmNicUuid() {
        return this.vmNicUuid;
    }

    public java.lang.Long weight;
    public void setWeight(java.lang.Long weight) {
        this.weight = weight;
    }
    public java.lang.Long getWeight() {
        return this.weight;
    }

    public java.lang.Integer ipVersion;
    public void setIpVersion(java.lang.Integer ipVersion) {
        this.ipVersion = ipVersion;
    }
    public java.lang.Integer getIpVersion() {
        return this.ipVersion;
    }

    public java.lang.String status;
    public void setStatus(java.lang.String status) {
        this.status = status;
    }
    public java.lang.String getStatus() {
        return this.status;
    }

    public java.sql.Timestamp createDate;
    public void setCreateDate(java.sql.Timestamp createDate) {
        this.createDate = createDate;
    }
    public java.sql.Timestamp getCreateDate() {
        return this.createDate;
    }

    public java.sql.Timestamp lastOpDate;
    public void setLastOpDate(java.sql.Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }
    public java.sql.Timestamp getLastOpDate() {
        return this.lastOpDate;
    }

}
