package org.zstack.sdk;



public class SharedBlockCandidateStruct  {

    public java.lang.String wwid;
    public void setWwid(java.lang.String wwid) {
        this.wwid = wwid;
    }
    public java.lang.String getWwid() {
        return this.wwid;
    }

    public java.lang.String vendor;
    public void setVendor(java.lang.String vendor) {
        this.vendor = vendor;
    }
    public java.lang.String getVendor() {
        return this.vendor;
    }

    public java.lang.String model;
    public void setModel(java.lang.String model) {
        this.model = model;
    }
    public java.lang.String getModel() {
        return this.model;
    }

    public java.lang.String wwn;
    public void setWwn(java.lang.String wwn) {
        this.wwn = wwn;
    }
    public java.lang.String getWwn() {
        return this.wwn;
    }

    public java.lang.String serial;
    public void setSerial(java.lang.String serial) {
        this.serial = serial;
    }
    public java.lang.String getSerial() {
        return this.serial;
    }

    public java.lang.String hctl;
    public void setHctl(java.lang.String hctl) {
        this.hctl = hctl;
    }
    public java.lang.String getHctl() {
        return this.hctl;
    }

    public java.lang.String type;
    public void setType(java.lang.String type) {
        this.type = type;
    }
    public java.lang.String getType() {
        return this.type;
    }

    public java.lang.String path;
    public void setPath(java.lang.String path) {
        this.path = path;
    }
    public java.lang.String getPath() {
        return this.path;
    }

    public java.lang.Long size;
    public void setSize(java.lang.Long size) {
        this.size = size;
    }
    public java.lang.Long getSize() {
        return this.size;
    }

    public java.lang.String source;
    public void setSource(java.lang.String source) {
        this.source = source;
    }
    public java.lang.String getSource() {
        return this.source;
    }

    public java.lang.String transport;
    public void setTransport(java.lang.String transport) {
        this.transport = transport;
    }
    public java.lang.String getTransport() {
        return this.transport;
    }

    public java.lang.String targetIdentifier;
    public void setTargetIdentifier(java.lang.String targetIdentifier) {
        this.targetIdentifier = targetIdentifier;
    }
    public java.lang.String getTargetIdentifier() {
        return this.targetIdentifier;
    }

}
