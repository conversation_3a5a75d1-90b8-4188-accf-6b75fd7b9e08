package org.zstack.core.config

import java.lang.Long
import java.lang.Long

doc {

	title "全局配置可选参数的数据结构"

	field {
		name "validValue"
		desc ""
		type "List"
		since "4.4.0"
	}
	field {
		name "numberGreater<PERSON>han"
		desc ""
		type "Long"
		since "4.4.0"
	}
	field {
		name "number<PERSON>ess<PERSON>han"
		desc ""
		type "Long"
		since "4.4.0"
	}
	field {
		name "numberGreaterThanOrEqual"
		desc "大于等于"
		type "Long"
		since "5.2.0"
	}
	field {
		name "numberLessThanOrEqual"
		desc "小于等于"
		type "Long"
		since "5.2.0"
	}
}
