<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>zstack</artifactId>
        <groupId>org.zstack</groupId>
        <version>5.3.0</version>
        <relativePath>..</relativePath>
    </parent>
    <artifactId>search</artifactId>
    <dependencies>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>header</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>utils</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.objenesis</groupId>
            <artifactId>objenesis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.kohsuke</groupId>
            <artifactId>groovy-sandbox</artifactId>
            <version>1.19</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
        </dependency>
        <dependency>
            <groupId>org.antlr</groupId>
            <artifactId>antlr4</artifactId>
        </dependency>
        <dependency>
            <groupId>org.antlr</groupId>
            <artifactId>antlr4-runtime</artifactId>
        </dependency>
        <dependency>
            <groupId>org.antlr</groupId>
            <artifactId>antlr4-maven-plugin</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>zql</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.antlr</groupId>
                        <artifactId>antlr4-maven-plugin</artifactId>
                        <version>4.7</version>
                        <executions>
                            <execution>
                                <configuration>
                                    <visitor>true</visitor>
                                </configuration>
                                <id>run-antlr</id>
                                <phase>generate-sources</phase>
                            </execution>
                        </executions>
                    </plugin>

                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>build-helper-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>antlr-add-source</id>
                                <phase>generate-sources</phase>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.antlr</groupId>
                <artifactId>antlr4-maven-plugin</artifactId>
                <version>4.7</version>
                <executions>
                    <execution>
                        <id>run-antlr</id>
                        <phase>none</phase>
                        <goals>
                            <goal>antlr4</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>${aspectj.plugin.version}</version>
                <executions>
                    <execution>
                        <id>antlr-add-source</id>
                        <phase>none</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>${basedir}/target/generated-sources/antlr4</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${project.compiler.version}</version>
                <configuration>
                    <compilerId>groovy-eclipse-compiler</compilerId>
                    <source>${project.java.version}</source>
                    <target>${project.java.version}</target>
                    <debuglevel>lines,vars,source</debuglevel>
                    <debug>true</debug>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy-eclipse-compiler</artifactId>
                        <version>2.9.2-01</version>
                    </dependency>
                    <dependency>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy-eclipse-batch</artifactId>
                        <version>2.4.3-01</version>
                    </dependency>
                </dependencies>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>aspectj-maven-plugin</artifactId>
                <version>${aspectj.plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>test-compile</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <source>${project.java.version}</source>
                    <target>${project.java.version}</target>
                    <complianceLevel>${project.java.version}</complianceLevel>
                    <XnoInline>true</XnoInline>
                    <aspectLibraries>
                        <aspectLibrary>
                            <groupId>org.springframework</groupId>
                            <artifactId>spring-aspects</artifactId>
                        </aspectLibrary>
                        <aspectLibrary>
                            <groupId>org.zstack</groupId>
                            <artifactId>core</artifactId>
                        </aspectLibrary>
                        <aspectLibrary>
                            <groupId>org.zstack</groupId>
                            <artifactId>header</artifactId>
                        </aspectLibrary>
                    </aspectLibraries>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
