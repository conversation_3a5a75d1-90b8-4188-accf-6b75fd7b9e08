// Generated from ZQL.g4 by ANTLR 4.7

package org.zstack.zql.antlr4;

import org.antlr.v4.runtime.Lexer;
import org.antlr.v4.runtime.CharStream;
import org.antlr.v4.runtime.Token;
import org.antlr.v4.runtime.TokenStream;
import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.atn.*;
import org.antlr.v4.runtime.dfa.DFA;
import org.antlr.v4.runtime.misc.*;

@SuppressWarnings({"all", "warnings", "unchecked", "unused", "cast"})
public class ZQLLexer extends Lexer {
	static { RuntimeMetaData.checkVersion("4.7", RuntimeMetaData.VERSION); }

	protected static final DFA[] _decisionToDFA;
	protected static final PredictionContextCache _sharedContextCache =
		new PredictionContextCache();
	public static final int
		T__0=1, T__1=2, T__2=3, T__3=4, T__4=5, T__5=6, T__6=7, T__7=8, T__8=9, 
		T__9=10, T__10=11, T__11=12, T__12=13, T__13=14, T__14=15, T__15=16, T__16=17, 
		T__17=18, T__18=19, T__19=20, T__20=21, T__21=22, T__22=23, T__23=24, 
		T__24=25, T__25=26, T__26=27, T__27=28, T__28=29, INNER=30, LEFT=31, RIGHT=32, 
		JOIN=33, ON=34, FILTER_BY=35, OFFSET=36, LIMIT=37, QUERY=38, GET=39, COUNT=40, 
		SUM=41, SEARCH=42, ORDER_BY=43, GROUP_BY=44, NAMED_AS=45, ORDER_BY_VALUE=46, 
		RESTRICT_BY=47, RETURN_WITH=48, WHERE=49, FROM=50, AND=51, OR=52, ASC=53, 
		DESC=54, INPUT=55, OUTPUT=56, BOOLEAN=57, INT=58, FLOAT=59, ID=60, WS=61, 
		STRING=62;
	public static String[] channelNames = {
		"DEFAULT_TOKEN_CHANNEL", "HIDDEN"
	};

	public static String[] modeNames = {
		"DEFAULT_MODE"
	};

	public static final String[] ruleNames = {
		"T__0", "T__1", "T__2", "T__3", "T__4", "T__5", "T__6", "T__7", "T__8", 
		"T__9", "T__10", "T__11", "T__12", "T__13", "T__14", "T__15", "T__16", 
		"T__17", "T__18", "T__19", "T__20", "T__21", "T__22", "T__23", "T__24", 
		"T__25", "T__26", "T__27", "T__28", "INNER", "LEFT", "RIGHT", "JOIN", 
		"ON", "FILTER_BY", "OFFSET", "LIMIT", "QUERY", "GET", "COUNT", "SUM", 
		"SEARCH", "ORDER_BY", "GROUP_BY", "NAMED_AS", "ORDER_BY_VALUE", "RESTRICT_BY", 
		"RETURN_WITH", "WHERE", "FROM", "AND", "OR", "ASC", "DESC", "INPUT", "OUTPUT", 
		"BOOLEAN", "INT", "FLOAT", "ID", "WS", "STRING", "CHAR", "NUMBER"
	};

	private static final String[] _LITERAL_NAMES = {
		null, "';'", "'.'", "','", "'='", "'!='", "'>'", "'>='", "'<'", "'<='", 
		"'is null'", "'is not null'", "'in'", "'not in'", "'like'", "'not like'", 
		"'has'", "'not has'", "'('", "')'", "'list('", "'{'", "'}'", "'by'", "'*'", 
		"'/'", "'%'", "'+'", "'-'", "'--'", "'inner'", "'left'", "'right'", "'join'", 
		"'on'", "'filter by'", "'offset'", "'limit'", "'query'", "'getapi'", "'count'", 
		"'sum'", "'search'", "'order by'", "'group by'", "'named as'", null, "'restrict by'", 
		"'return with'", "'where'", "'from'", "'and'", "'or'", "'asc'", "'desc'", 
		"'api'", "'output'"
	};
	private static final String[] _SYMBOLIC_NAMES = {
		null, null, null, null, null, null, null, null, null, null, null, null, 
		null, null, null, null, null, null, null, null, null, null, null, null, 
		null, null, null, null, null, null, "INNER", "LEFT", "RIGHT", "JOIN", 
		"ON", "FILTER_BY", "OFFSET", "LIMIT", "QUERY", "GET", "COUNT", "SUM", 
		"SEARCH", "ORDER_BY", "GROUP_BY", "NAMED_AS", "ORDER_BY_VALUE", "RESTRICT_BY", 
		"RETURN_WITH", "WHERE", "FROM", "AND", "OR", "ASC", "DESC", "INPUT", "OUTPUT", 
		"BOOLEAN", "INT", "FLOAT", "ID", "WS", "STRING"
	};
	public static final Vocabulary VOCABULARY = new VocabularyImpl(_LITERAL_NAMES, _SYMBOLIC_NAMES);

	/**
	 * @deprecated Use {@link #VOCABULARY} instead.
	 */
	@Deprecated
	public static final String[] tokenNames;
	static {
		tokenNames = new String[_SYMBOLIC_NAMES.length];
		for (int i = 0; i < tokenNames.length; i++) {
			tokenNames[i] = VOCABULARY.getLiteralName(i);
			if (tokenNames[i] == null) {
				tokenNames[i] = VOCABULARY.getSymbolicName(i);
			}

			if (tokenNames[i] == null) {
				tokenNames[i] = "<INVALID>";
			}
		}
	}

	@Override
	@Deprecated
	public String[] getTokenNames() {
		return tokenNames;
	}

	@Override

	public Vocabulary getVocabulary() {
		return VOCABULARY;
	}


	public ZQLLexer(CharStream input) {
		super(input);
		_interp = new LexerATNSimulator(this,_ATN,_decisionToDFA,_sharedContextCache);
	}

	@Override
	public String getGrammarFileName() { return "ZQL.g4"; }

	@Override
	public String[] getRuleNames() { return ruleNames; }

	@Override
	public String getSerializedATN() { return _serializedATN; }

	@Override
	public String[] getChannelNames() { return channelNames; }

	@Override
	public String[] getModeNames() { return modeNames; }

	@Override
	public ATN getATN() { return _ATN; }

	public static final String _serializedATN =
		"\3\u608b\ua72a\u8133\ub9ed\u417c\u3be7\u7786\u5964\2@\u01e1\b\1\4\2\t"+
		"\2\4\3\t\3\4\4\t\4\4\5\t\5\4\6\t\6\4\7\t\7\4\b\t\b\4\t\t\t\4\n\t\n\4\13"+
		"\t\13\4\f\t\f\4\r\t\r\4\16\t\16\4\17\t\17\4\20\t\20\4\21\t\21\4\22\t\22"+
		"\4\23\t\23\4\24\t\24\4\25\t\25\4\26\t\26\4\27\t\27\4\30\t\30\4\31\t\31"+
		"\4\32\t\32\4\33\t\33\4\34\t\34\4\35\t\35\4\36\t\36\4\37\t\37\4 \t \4!"+
		"\t!\4\"\t\"\4#\t#\4$\t$\4%\t%\4&\t&\4\'\t\'\4(\t(\4)\t)\4*\t*\4+\t+\4"+
		",\t,\4-\t-\4.\t.\4/\t/\4\60\t\60\4\61\t\61\4\62\t\62\4\63\t\63\4\64\t"+
		"\64\4\65\t\65\4\66\t\66\4\67\t\67\48\t8\49\t9\4:\t:\4;\t;\4<\t<\4=\t="+
		"\4>\t>\4?\t?\4@\t@\4A\tA\3\2\3\2\3\3\3\3\3\4\3\4\3\5\3\5\3\6\3\6\3\6\3"+
		"\7\3\7\3\b\3\b\3\b\3\t\3\t\3\n\3\n\3\n\3\13\3\13\3\13\3\13\3\13\3\13\3"+
		"\13\3\13\3\f\3\f\3\f\3\f\3\f\3\f\3\f\3\f\3\f\3\f\3\f\3\f\3\r\3\r\3\r\3"+
		"\16\3\16\3\16\3\16\3\16\3\16\3\16\3\17\3\17\3\17\3\17\3\17\3\20\3\20\3"+
		"\20\3\20\3\20\3\20\3\20\3\20\3\20\3\21\3\21\3\21\3\21\3\22\3\22\3\22\3"+
		"\22\3\22\3\22\3\22\3\22\3\23\3\23\3\24\3\24\3\25\3\25\3\25\3\25\3\25\3"+
		"\25\3\26\3\26\3\27\3\27\3\30\3\30\3\30\3\31\3\31\3\32\3\32\3\33\3\33\3"+
		"\34\3\34\3\35\3\35\3\36\3\36\3\36\3\37\3\37\3\37\3\37\3\37\3\37\3 \3 "+
		"\3 \3 \3 \3!\3!\3!\3!\3!\3!\3\"\3\"\3\"\3\"\3\"\3#\3#\3#\3$\3$\3$\3$\3"+
		"$\3$\3$\3$\3$\3$\3%\3%\3%\3%\3%\3%\3%\3&\3&\3&\3&\3&\3&\3\'\3\'\3\'\3"+
		"\'\3\'\3\'\3(\3(\3(\3(\3(\3(\3(\3)\3)\3)\3)\3)\3)\3*\3*\3*\3*\3+\3+\3"+
		"+\3+\3+\3+\3+\3,\3,\3,\3,\3,\3,\3,\3,\3,\3-\3-\3-\3-\3-\3-\3-\3-\3-\3"+
		".\3.\3.\3.\3.\3.\3.\3.\3.\3/\3/\5/\u015a\n/\3\60\3\60\3\60\3\60\3\60\3"+
		"\60\3\60\3\60\3\60\3\60\3\60\3\60\3\61\3\61\3\61\3\61\3\61\3\61\3\61\3"+
		"\61\3\61\3\61\3\61\3\61\3\62\3\62\3\62\3\62\3\62\3\62\3\63\3\63\3\63\3"+
		"\63\3\63\3\64\3\64\3\64\3\64\3\65\3\65\3\65\3\66\3\66\3\66\3\66\3\67\3"+
		"\67\3\67\3\67\3\67\38\38\38\38\39\39\39\39\39\39\39\3:\3:\3:\3:\3:\3:"+
		"\3:\3:\3:\5:\u01a3\n:\3;\5;\u01a6\n;\3;\3;\3<\3<\3<\6<\u01ad\n<\r<\16"+
		"<\u01ae\3=\6=\u01b2\n=\r=\16=\u01b3\3>\6>\u01b7\n>\r>\16>\u01b8\3>\3>"+
		"\3?\3?\7?\u01bf\n?\f?\16?\u01c2\13?\3?\3?\3?\3?\3?\7?\u01c9\n?\f?\16?"+
		"\u01cc\13?\3?\5?\u01cf\n?\3@\6@\u01d2\n@\r@\16@\u01d3\3@\6@\u01d7\n@\r"+
		"@\16@\u01d8\5@\u01db\n@\3A\6A\u01de\nA\rA\16A\u01df\2\2B\3\3\5\4\7\5\t"+
		"\6\13\7\r\b\17\t\21\n\23\13\25\f\27\r\31\16\33\17\35\20\37\21!\22#\23"+
		"%\24\'\25)\26+\27-\30/\31\61\32\63\33\65\34\67\359\36;\37= ?!A\"C#E$G"+
		"%I&K\'M(O)Q*S+U,W-Y.[/]\60_\61a\62c\63e\64g\65i\66k\67m8o9q:s;u<w=y>{"+
		"?}@\177\2\u0081\2\3\2\6\6\2\62;C\\aac|\5\2\13\f\17\17\"\"\3\2$$\3\2))"+
		"\2\u01ec\2\3\3\2\2\2\2\5\3\2\2\2\2\7\3\2\2\2\2\t\3\2\2\2\2\13\3\2\2\2"+
		"\2\r\3\2\2\2\2\17\3\2\2\2\2\21\3\2\2\2\2\23\3\2\2\2\2\25\3\2\2\2\2\27"+
		"\3\2\2\2\2\31\3\2\2\2\2\33\3\2\2\2\2\35\3\2\2\2\2\37\3\2\2\2\2!\3\2\2"+
		"\2\2#\3\2\2\2\2%\3\2\2\2\2\'\3\2\2\2\2)\3\2\2\2\2+\3\2\2\2\2-\3\2\2\2"+
		"\2/\3\2\2\2\2\61\3\2\2\2\2\63\3\2\2\2\2\65\3\2\2\2\2\67\3\2\2\2\29\3\2"+
		"\2\2\2;\3\2\2\2\2=\3\2\2\2\2?\3\2\2\2\2A\3\2\2\2\2C\3\2\2\2\2E\3\2\2\2"+
		"\2G\3\2\2\2\2I\3\2\2\2\2K\3\2\2\2\2M\3\2\2\2\2O\3\2\2\2\2Q\3\2\2\2\2S"+
		"\3\2\2\2\2U\3\2\2\2\2W\3\2\2\2\2Y\3\2\2\2\2[\3\2\2\2\2]\3\2\2\2\2_\3\2"+
		"\2\2\2a\3\2\2\2\2c\3\2\2\2\2e\3\2\2\2\2g\3\2\2\2\2i\3\2\2\2\2k\3\2\2\2"+
		"\2m\3\2\2\2\2o\3\2\2\2\2q\3\2\2\2\2s\3\2\2\2\2u\3\2\2\2\2w\3\2\2\2\2y"+
		"\3\2\2\2\2{\3\2\2\2\2}\3\2\2\2\3\u0083\3\2\2\2\5\u0085\3\2\2\2\7\u0087"+
		"\3\2\2\2\t\u0089\3\2\2\2\13\u008b\3\2\2\2\r\u008e\3\2\2\2\17\u0090\3\2"+
		"\2\2\21\u0093\3\2\2\2\23\u0095\3\2\2\2\25\u0098\3\2\2\2\27\u00a0\3\2\2"+
		"\2\31\u00ac\3\2\2\2\33\u00af\3\2\2\2\35\u00b6\3\2\2\2\37\u00bb\3\2\2\2"+
		"!\u00c4\3\2\2\2#\u00c8\3\2\2\2%\u00d0\3\2\2\2\'\u00d2\3\2\2\2)\u00d4\3"+
		"\2\2\2+\u00da\3\2\2\2-\u00dc\3\2\2\2/\u00de\3\2\2\2\61\u00e1\3\2\2\2\63"+
		"\u00e3\3\2\2\2\65\u00e5\3\2\2\2\67\u00e7\3\2\2\29\u00e9\3\2\2\2;\u00eb"+
		"\3\2\2\2=\u00ee\3\2\2\2?\u00f4\3\2\2\2A\u00f9\3\2\2\2C\u00ff\3\2\2\2E"+
		"\u0104\3\2\2\2G\u0107\3\2\2\2I\u0111\3\2\2\2K\u0118\3\2\2\2M\u011e\3\2"+
		"\2\2O\u0124\3\2\2\2Q\u012b\3\2\2\2S\u0131\3\2\2\2U\u0135\3\2\2\2W\u013c"+
		"\3\2\2\2Y\u0145\3\2\2\2[\u014e\3\2\2\2]\u0159\3\2\2\2_\u015b\3\2\2\2a"+
		"\u0167\3\2\2\2c\u0173\3\2\2\2e\u0179\3\2\2\2g\u017e\3\2\2\2i\u0182\3\2"+
		"\2\2k\u0185\3\2\2\2m\u0189\3\2\2\2o\u018e\3\2\2\2q\u0192\3\2\2\2s\u01a2"+
		"\3\2\2\2u\u01a5\3\2\2\2w\u01a9\3\2\2\2y\u01b1\3\2\2\2{\u01b6\3\2\2\2}"+
		"\u01ce\3\2\2\2\177\u01da\3\2\2\2\u0081\u01dd\3\2\2\2\u0083\u0084\7=\2"+
		"\2\u0084\4\3\2\2\2\u0085\u0086\7\60\2\2\u0086\6\3\2\2\2\u0087\u0088\7"+
		".\2\2\u0088\b\3\2\2\2\u0089\u008a\7?\2\2\u008a\n\3\2\2\2\u008b\u008c\7"+
		"#\2\2\u008c\u008d\7?\2\2\u008d\f\3\2\2\2\u008e\u008f\7@\2\2\u008f\16\3"+
		"\2\2\2\u0090\u0091\7@\2\2\u0091\u0092\7?\2\2\u0092\20\3\2\2\2\u0093\u0094"+
		"\7>\2\2\u0094\22\3\2\2\2\u0095\u0096\7>\2\2\u0096\u0097\7?\2\2\u0097\24"+
		"\3\2\2\2\u0098\u0099\7k\2\2\u0099\u009a\7u\2\2\u009a\u009b\7\"\2\2\u009b"+
		"\u009c\7p\2\2\u009c\u009d\7w\2\2\u009d\u009e\7n\2\2\u009e\u009f\7n\2\2"+
		"\u009f\26\3\2\2\2\u00a0\u00a1\7k\2\2\u00a1\u00a2\7u\2\2\u00a2\u00a3\7"+
		"\"\2\2\u00a3\u00a4\7p\2\2\u00a4\u00a5\7q\2\2\u00a5\u00a6\7v\2\2\u00a6"+
		"\u00a7\7\"\2\2\u00a7\u00a8\7p\2\2\u00a8\u00a9\7w\2\2\u00a9\u00aa\7n\2"+
		"\2\u00aa\u00ab\7n\2\2\u00ab\30\3\2\2\2\u00ac\u00ad\7k\2\2\u00ad\u00ae"+
		"\7p\2\2\u00ae\32\3\2\2\2\u00af\u00b0\7p\2\2\u00b0\u00b1\7q\2\2\u00b1\u00b2"+
		"\7v\2\2\u00b2\u00b3\7\"\2\2\u00b3\u00b4\7k\2\2\u00b4\u00b5\7p\2\2\u00b5"+
		"\34\3\2\2\2\u00b6\u00b7\7n\2\2\u00b7\u00b8\7k\2\2\u00b8\u00b9\7m\2\2\u00b9"+
		"\u00ba\7g\2\2\u00ba\36\3\2\2\2\u00bb\u00bc\7p\2\2\u00bc\u00bd\7q\2\2\u00bd"+
		"\u00be\7v\2\2\u00be\u00bf\7\"\2\2\u00bf\u00c0\7n\2\2\u00c0\u00c1\7k\2"+
		"\2\u00c1\u00c2\7m\2\2\u00c2\u00c3\7g\2\2\u00c3 \3\2\2\2\u00c4\u00c5\7"+
		"j\2\2\u00c5\u00c6\7c\2\2\u00c6\u00c7\7u\2\2\u00c7\"\3\2\2\2\u00c8\u00c9"+
		"\7p\2\2\u00c9\u00ca\7q\2\2\u00ca\u00cb\7v\2\2\u00cb\u00cc\7\"\2\2\u00cc"+
		"\u00cd\7j\2\2\u00cd\u00ce\7c\2\2\u00ce\u00cf\7u\2\2\u00cf$\3\2\2\2\u00d0"+
		"\u00d1\7*\2\2\u00d1&\3\2\2\2\u00d2\u00d3\7+\2\2\u00d3(\3\2\2\2\u00d4\u00d5"+
		"\7n\2\2\u00d5\u00d6\7k\2\2\u00d6\u00d7\7u\2\2\u00d7\u00d8\7v\2\2\u00d8"+
		"\u00d9\7*\2\2\u00d9*\3\2\2\2\u00da\u00db\7}\2\2\u00db,\3\2\2\2\u00dc\u00dd"+
		"\7\177\2\2\u00dd.\3\2\2\2\u00de\u00df\7d\2\2\u00df\u00e0\7{\2\2\u00e0"+
		"\60\3\2\2\2\u00e1\u00e2\7,\2\2\u00e2\62\3\2\2\2\u00e3\u00e4\7\61\2\2\u00e4"+
		"\64\3\2\2\2\u00e5\u00e6\7\'\2\2\u00e6\66\3\2\2\2\u00e7\u00e8\7-\2\2\u00e8"+
		"8\3\2\2\2\u00e9\u00ea\7/\2\2\u00ea:\3\2\2\2\u00eb\u00ec\7/\2\2\u00ec\u00ed"+
		"\7/\2\2\u00ed<\3\2\2\2\u00ee\u00ef\7k\2\2\u00ef\u00f0\7p\2\2\u00f0\u00f1"+
		"\7p\2\2\u00f1\u00f2\7g\2\2\u00f2\u00f3\7t\2\2\u00f3>\3\2\2\2\u00f4\u00f5"+
		"\7n\2\2\u00f5\u00f6\7g\2\2\u00f6\u00f7\7h\2\2\u00f7\u00f8\7v\2\2\u00f8"+
		"@\3\2\2\2\u00f9\u00fa\7t\2\2\u00fa\u00fb\7k\2\2\u00fb\u00fc\7i\2\2\u00fc"+
		"\u00fd\7j\2\2\u00fd\u00fe\7v\2\2\u00feB\3\2\2\2\u00ff\u0100\7l\2\2\u0100"+
		"\u0101\7q\2\2\u0101\u0102\7k\2\2\u0102\u0103\7p\2\2\u0103D\3\2\2\2\u0104"+
		"\u0105\7q\2\2\u0105\u0106\7p\2\2\u0106F\3\2\2\2\u0107\u0108\7h\2\2\u0108"+
		"\u0109\7k\2\2\u0109\u010a\7n\2\2\u010a\u010b\7v\2\2\u010b\u010c\7g\2\2"+
		"\u010c\u010d\7t\2\2\u010d\u010e\7\"\2\2\u010e\u010f\7d\2\2\u010f\u0110"+
		"\7{\2\2\u0110H\3\2\2\2\u0111\u0112\7q\2\2\u0112\u0113\7h\2\2\u0113\u0114"+
		"\7h\2\2\u0114\u0115\7u\2\2\u0115\u0116\7g\2\2\u0116\u0117\7v\2\2\u0117"+
		"J\3\2\2\2\u0118\u0119\7n\2\2\u0119\u011a\7k\2\2\u011a\u011b\7o\2\2\u011b"+
		"\u011c\7k\2\2\u011c\u011d\7v\2\2\u011dL\3\2\2\2\u011e\u011f\7s\2\2\u011f"+
		"\u0120\7w\2\2\u0120\u0121\7g\2\2\u0121\u0122\7t\2\2\u0122\u0123\7{\2\2"+
		"\u0123N\3\2\2\2\u0124\u0125\7i\2\2\u0125\u0126\7g\2\2\u0126\u0127\7v\2"+
		"\2\u0127\u0128\7c\2\2\u0128\u0129\7r\2\2\u0129\u012a\7k\2\2\u012aP\3\2"+
		"\2\2\u012b\u012c\7e\2\2\u012c\u012d\7q\2\2\u012d\u012e\7w\2\2\u012e\u012f"+
		"\7p\2\2\u012f\u0130\7v\2\2\u0130R\3\2\2\2\u0131\u0132\7u\2\2\u0132\u0133"+
		"\7w\2\2\u0133\u0134\7o\2\2\u0134T\3\2\2\2\u0135\u0136\7u\2\2\u0136\u0137"+
		"\7g\2\2\u0137\u0138\7c\2\2\u0138\u0139\7t\2\2\u0139\u013a\7e\2\2\u013a"+
		"\u013b\7j\2\2\u013bV\3\2\2\2\u013c\u013d\7q\2\2\u013d\u013e\7t\2\2\u013e"+
		"\u013f\7f\2\2\u013f\u0140\7g\2\2\u0140\u0141\7t\2\2\u0141\u0142\7\"\2"+
		"\2\u0142\u0143\7d\2\2\u0143\u0144\7{\2\2\u0144X\3\2\2\2\u0145\u0146\7"+
		"i\2\2\u0146\u0147\7t\2\2\u0147\u0148\7q\2\2\u0148\u0149\7w\2\2\u0149\u014a"+
		"\7r\2\2\u014a\u014b\7\"\2\2\u014b\u014c\7d\2\2\u014c\u014d\7{\2\2\u014d"+
		"Z\3\2\2\2\u014e\u014f\7p\2\2\u014f\u0150\7c\2\2\u0150\u0151\7o\2\2\u0151"+
		"\u0152\7g\2\2\u0152\u0153\7f\2\2\u0153\u0154\7\"\2\2\u0154\u0155\7c\2"+
		"\2\u0155\u0156\7u\2\2\u0156\\\3\2\2\2\u0157\u015a\5k\66\2\u0158\u015a"+
		"\5m\67\2\u0159\u0157\3\2\2\2\u0159\u0158\3\2\2\2\u015a^\3\2\2\2\u015b"+
		"\u015c\7t\2\2\u015c\u015d\7g\2\2\u015d\u015e\7u\2\2\u015e\u015f\7v\2\2"+
		"\u015f\u0160\7t\2\2\u0160\u0161\7k\2\2\u0161\u0162\7e\2\2\u0162\u0163"+
		"\7v\2\2\u0163\u0164\7\"\2\2\u0164\u0165\7d\2\2\u0165\u0166\7{\2\2\u0166"+
		"`\3\2\2\2\u0167\u0168\7t\2\2\u0168\u0169\7g\2\2\u0169\u016a\7v\2\2\u016a"+
		"\u016b\7w\2\2\u016b\u016c\7t\2\2\u016c\u016d\7p\2\2\u016d\u016e\7\"\2"+
		"\2\u016e\u016f\7y\2\2\u016f\u0170\7k\2\2\u0170\u0171\7v\2\2\u0171\u0172"+
		"\7j\2\2\u0172b\3\2\2\2\u0173\u0174\7y\2\2\u0174\u0175\7j\2\2\u0175\u0176"+
		"\7g\2\2\u0176\u0177\7t\2\2\u0177\u0178\7g\2\2\u0178d\3\2\2\2\u0179\u017a"+
		"\7h\2\2\u017a\u017b\7t\2\2\u017b\u017c\7q\2\2\u017c\u017d\7o\2\2\u017d"+
		"f\3\2\2\2\u017e\u017f\7c\2\2\u017f\u0180\7p\2\2\u0180\u0181\7f\2\2\u0181"+
		"h\3\2\2\2\u0182\u0183\7q\2\2\u0183\u0184\7t\2\2\u0184j\3\2\2\2\u0185\u0186"+
		"\7c\2\2\u0186\u0187\7u\2\2\u0187\u0188\7e\2\2\u0188l\3\2\2\2\u0189\u018a"+
		"\7f\2\2\u018a\u018b\7g\2\2\u018b\u018c\7u\2\2\u018c\u018d\7e\2\2\u018d"+
		"n\3\2\2\2\u018e\u018f\7c\2\2\u018f\u0190\7r\2\2\u0190\u0191\7k\2\2\u0191"+
		"p\3\2\2\2\u0192\u0193\7q\2\2\u0193\u0194\7w\2\2\u0194\u0195\7v\2\2\u0195"+
		"\u0196\7r\2\2\u0196\u0197\7w\2\2\u0197\u0198\7v\2\2\u0198r\3\2\2\2\u0199"+
		"\u019a\7v\2\2\u019a\u019b\7t\2\2\u019b\u019c\7w\2\2\u019c\u01a3\7g\2\2"+
		"\u019d\u019e\7h\2\2\u019e\u019f\7c\2\2\u019f\u01a0\7n\2\2\u01a0\u01a1"+
		"\7u\2\2\u01a1\u01a3\7g\2\2\u01a2\u0199\3\2\2\2\u01a2\u019d\3\2\2\2\u01a3"+
		"t\3\2\2\2\u01a4\u01a6\7/\2\2\u01a5\u01a4\3\2\2\2\u01a5\u01a6\3\2\2\2\u01a6"+
		"\u01a7\3\2\2\2\u01a7\u01a8\5\u0081A\2\u01a8v\3\2\2\2\u01a9\u01aa\5u;\2"+
		"\u01aa\u01ac\7\60\2\2\u01ab\u01ad\5\u0081A\2\u01ac\u01ab\3\2\2\2\u01ad"+
		"\u01ae\3\2\2\2\u01ae\u01ac\3\2\2\2\u01ae\u01af\3\2\2\2\u01afx\3\2\2\2"+
		"\u01b0\u01b2\t\2\2\2\u01b1\u01b0\3\2\2\2\u01b2\u01b3\3\2\2\2\u01b3\u01b1"+
		"\3\2\2\2\u01b3\u01b4\3\2\2\2\u01b4z\3\2\2\2\u01b5\u01b7\t\3\2\2\u01b6"+
		"\u01b5\3\2\2\2\u01b7\u01b8\3\2\2\2\u01b8\u01b6\3\2\2\2\u01b8\u01b9\3\2"+
		"\2\2\u01b9\u01ba\3\2\2\2\u01ba\u01bb\b>\2\2\u01bb|\3\2\2\2\u01bc\u01c0"+
		"\7$\2\2\u01bd\u01bf\n\4\2\2\u01be\u01bd\3\2\2\2\u01bf\u01c2\3\2\2\2\u01c0"+
		"\u01be\3\2\2\2\u01c0\u01c1\3\2\2\2\u01c1\u01c3\3\2\2\2\u01c2\u01c0\3\2"+
		"\2\2\u01c3\u01cf\7$\2\2\u01c4\u01ca\7)\2\2\u01c5\u01c6\7)\2\2\u01c6\u01c9"+
		"\7)\2\2\u01c7\u01c9\n\5\2\2\u01c8\u01c5\3\2\2\2\u01c8\u01c7\3\2\2\2\u01c9"+
		"\u01cc\3\2\2\2\u01ca\u01c8\3\2\2\2\u01ca\u01cb\3\2\2\2\u01cb\u01cd\3\2"+
		"\2\2\u01cc\u01ca\3\2\2\2\u01cd\u01cf\7)\2\2\u01ce\u01bc\3\2\2\2\u01ce"+
		"\u01c4\3\2\2\2\u01cf~\3\2\2\2\u01d0\u01d2\4c|\2\u01d1\u01d0\3\2\2\2\u01d2"+
		"\u01d3\3\2\2\2\u01d3\u01d1\3\2\2\2\u01d3\u01d4\3\2\2\2\u01d4\u01db\3\2"+
		"\2\2\u01d5\u01d7\4C\\\2\u01d6\u01d5\3\2\2\2\u01d7\u01d8\3\2\2\2\u01d8"+
		"\u01d6\3\2\2\2\u01d8\u01d9\3\2\2\2\u01d9\u01db\3\2\2\2\u01da\u01d1\3\2"+
		"\2\2\u01da\u01d6\3\2\2\2\u01db\u0080\3\2\2\2\u01dc\u01de\4\62;\2\u01dd"+
		"\u01dc\3\2\2\2\u01de\u01df\3\2\2\2\u01df\u01dd\3\2\2\2\u01df\u01e0\3\2"+
		"\2\2\u01e0\u0082\3\2\2\2\21\2\u0159\u01a2\u01a5\u01ae\u01b3\u01b8\u01c0"+
		"\u01c8\u01ca\u01ce\u01d3\u01d8\u01da\u01df\3\b\2\2";
	public static final ATN _ATN =
		new ATNDeserializer().deserialize(_serializedATN.toCharArray());
	static {
		_decisionToDFA = new DFA[_ATN.getNumberOfDecisions()];
		for (int i = 0; i < _ATN.getNumberOfDecisions(); i++) {
			_decisionToDFA[i] = new DFA(_ATN.getDecisionState(i), i);
		}
	}
}