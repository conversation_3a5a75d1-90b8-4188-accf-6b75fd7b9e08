<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <properties>
        <maven.resources.overwrite>true</maven.resources.overwrite>
        <jacoco.skip>${env.skipJacoco}</jacoco.skip>
        <jacoco.skip>${skipJacoco}</jacoco.skip>
    </properties>
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>zstack</artifactId>
        <groupId>org.zstack</groupId>
        <version>5.3.0</version>
        <relativePath>..</relativePath>
    </parent>
    <artifactId>test</artifactId>

    <!--
    <repositories>
        <repository>
            <id>repository.jboss.org</id>
            <name>JBoss Repository</name>
            <url>http://repository.jboss.org/nexus/content/groups/public-jboss/</url>
        </repository>
    </repositories>
-->

    <dependencies>
        <dependency>
            <groupId>org.kohsuke</groupId>
            <artifactId>groovy-sandbox</artifactId>
            <version>1.19</version>
           <!-- <scope>system</scope>
            <systemPath>${project.basedir}/ext-libs/groovy-sandbox-1.27-SNAPSHOT.jar</systemPath>-->
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.1</version>
        </dependency>
        <dependency>
            <groupId>org.zapodot</groupId>
            <artifactId>embedded-ldap-junit</artifactId>
            <version>0.7</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>4.11.0</version>
        </dependency>

        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>compute</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>header</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>portal</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>utils</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>external-service</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>simulatorImpl</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>image</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>network</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>configuration</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>identity</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>search</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>console</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>applianceVm</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>localstorage</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>externalStorage</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>zbs</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>cbd</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>expon</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>xinfini</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>vhost</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>iscsi</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>ceph</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>sharedMountPointPrimaryStorage</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>ldap</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.jboss.shrinkwrap</groupId>
            <artifactId>shrinkwrap-api</artifactId>
            <version>1.0.0-alpha-12</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jboss.shrinkwrap</groupId>
            <version>1.0.0-alpha-12</version>
            <artifactId>shrinkwrap-impl-base</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-server</artifactId>
            <version>9.4.51.v20230217</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-webapp</artifactId>
            <version>9.4.49.v20220914</version>
        </dependency>

        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>kvm</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>nfsPrimaryStorage</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>sftpBackupStorage</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>virtualRouterProvider</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>securityGroup</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>vip</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>mediator</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>flatNetworkProvider</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>rest</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>vxlan</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>sdnController</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>longjob</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>sshKeyPair</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>sdk</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>sugonSdnController</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>hostNetworkInterface</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>testlib</artifactId>
            <version>${project.version}</version>
        </dependency>
	    <dependency>
	        <groupId>com.github.javaparser</groupId>
	        <artifactId>javaparser-core</artifactId>
	    </dependency>
        <dependency>
            <groupId>org.jasig.cas.client</groupId>
            <artifactId>cas-client-core</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>merge</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>

                        <executions>
                            <execution>
                                <goals>
                                    <goal>run</goal>
                                </goals>

                                <id>default-cli</id>

                                <configuration>
                                    <target>
                                        <taskdef name="merge" classname="org.jacoco.ant.MergeTask">
                                            <classpath path="${basedir}/target/jacoco-jars/*.jar"/>
                                        </taskdef>

                                        <merge destfile="target/reports/jacoco-merged.exec">
                                            <fileset dir="${basedir}/target/reports" includes="*.exec"/>
                                        </merge>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>

                        <dependencies>
                            <dependency>
                                <groupId>org.jacoco</groupId>
                                <artifactId>org.jacoco.ant</artifactId>
                                <version>0.8.6</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>coverage</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>run</goal>
                                </goals>

                                <id>default-cli</id>
                                <configuration>
                                    <target>
                                        <!-- Execute an ant task within maven -->
                                        <echo message="Generating JaCoCo Reports"/>
                                        <taskdef name="report" classname="org.jacoco.ant.ReportTask">
                                            <classpath path="${basedir}/target/jacoco-jars/*.jar"/>
                                        </taskdef>

                                        <mkdir dir="${basedir}/target/coverage-report"/>

                                        <report>
                                            <executiondata>
                                                <fileset dir="${basedir}">
                                                    <include name="target/jacoco.exec"/>
                                                </fileset>
                                            </executiondata>

                                            <structure name="Integration Tests Coverage Report">
                                                <classfiles>
                                                    <fileset dir="${basedir}/target/classes"/>
                                                </classfiles>

                                                <sourcefiles encoding="UTF-8">
                                                    <dirset dir="${project.basedir}/../">
                                                        <include name="**/java" />
                                                        <include name="**/groovy" />
                                                    </dirset>
                                                </sourcefiles>
                                            </structure>
                                            <html destdir="${basedir}/target/coverage-report/html"/>
                                            <xml destfile="${basedir}/target/coverage-report/coverage-report.xml"/>
                                            <csv destfile="${basedir}/target/coverage-report/coverage-report.csv"/>
                                        </report>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>

                        <dependencies>
                            <dependency>
                                <groupId>org.jacoco</groupId>
                                <artifactId>org.jacoco.ant</artifactId>
                                <version>0.8.6</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>coverage-xml</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>1.8</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>run</goal>
                                </goals>

                                <id>default-cli</id>
                                <configuration>
                                    <target>
                                        <!-- Execute an ant task within maven -->
                                        <echo message="Generating JaCoCo Reports"/>
                                        <taskdef name="report" classname="org.jacoco.ant.ReportTask">
                                            <classpath path="${basedir}/target/jacoco-jars/*.jar"/>
                                        </taskdef>

                                        <mkdir dir="${basedir}/target/coverage-report"/>

                                        <report>
                                            <executiondata>
                                                <fileset dir="${basedir}">
                                                    <include name="target/jacoco.exec"/>
                                                </fileset>
                                            </executiondata>

                                            <structure name="Integration Tests Coverage Report">
                                                <classfiles>
                                                    <fileset dir="${basedir}/target/classes"/>
                                                </classfiles>

                                                <sourcefiles encoding="UTF-8">
                                                    <dirset dir="${project.basedir}/../">
                                                        <include name="**/java" />
                                                        <include name="**/groovy" />
                                                    </dirset>
                                                </sourcefiles>
                                            </structure>
                                            <xml destfile="${basedir}/target/coverage-report/coverage-report.xml"/>
                                        </report>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>

                        <dependencies>
                            <dependency>
                                <groupId>org.jacoco</groupId>
                                <artifactId>org.jacoco.ant</artifactId>
                                <version>0.8.6</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>


    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.6</version>

                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <append>true</append>
                            <classDumpDir>target/classes</classDumpDir>
                            <includes>
                                <include>org.zstack.*</include>
                            </includes>
                            <destFile>target/jacoco.exec</destFile>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.18</version>
                <configuration>
                    <classpathDependencyExcludes>
                        <classpathDependencyExcludes>c3p0:c3p0:jar:0.9.1.1</classpathDependencyExcludes>
                    </classpathDependencyExcludes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${project.compiler.version}</version>
                <configuration>
                    <source>${project.java.version}</source>
                    <target>${project.java.version}</target>
                    <debug>true</debug>
                    <useIncrementalCompilation>false</useIncrementalCompilation>
                </configuration>
            </plugin>

	    <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.6.1</version>
                <configuration>
                    <compilerId>groovy-eclipse-compiler</compilerId>
                    <source>${project.java.version}</source>
                    <target>${project.java.version}</target>
		            <debuglevel>lines,vars,source</debuglevel>
		            <debug>true</debug>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy-eclipse-compiler</artifactId>
                        <version>2.9.2-01</version>
                    </dependency>
                    <dependency>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy-eclipse-batch</artifactId>
                        <version>2.4.3-01</version>
                    </dependency>
                </dependencies>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>aspectj-maven-plugin</artifactId>
                <version>${aspectj.plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>test-compile</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <source>${project.java.version}</source>
                    <target>${project.java.version}</target>
                    <complianceLevel>${project.java.version}</complianceLevel>
                    <XnoInline>true</XnoInline>
                    <aspectLibraries>
                        <aspectLibrary>
                            <groupId>org.springframework</groupId>
                            <artifactId>spring-aspects</artifactId>
                        </aspectLibrary>
                        <aspectLibrary>
                            <groupId>org.zstack</groupId>
                            <artifactId>core</artifactId>
                        </aspectLibrary>
                        <aspectLibrary>
                            <groupId>org.zstack</groupId>
                            <artifactId>header</artifactId>
                        </aspectLibrary>
                    </aspectLibraries>
                </configuration>
            </plugin>

            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.5</version>
                <executions>
                    <execution>
                        <id>copy-conf</id>
                        <!-- here the phase you need -->
                        <phase>validate</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>target/test-classes</outputDirectory>
                            <includeEmptyDirs>true</includeEmptyDirs>
                            <resources>
                                <resource>
                                    <directory>../conf</directory>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
