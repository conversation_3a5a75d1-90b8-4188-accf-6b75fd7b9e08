dn: dc=example,dc=com
objectClass: domain
objectClass: top
dc: example

dn: ou=Users,dc=example,dc=com
objectClass: organizationalUnit
objectClass: top
ou: Users

dn: ou=Groups,dc=example,dc=com
objectClass: organizationalUnit
objectClass: top
ou: Groups

dn: cn=<PERSON><PERSON>,ou=Users,dc=example,dc=com
objectClass: inetOrgPerson
objectClass: organizationalPerson
objectClass: person
objectClass: top
cn: <PERSON><PERSON>
sn: Kops
uid: mkops
userPassword: password
entryDN: cn=<PERSON><PERSON>,ou=Users,dc=example,dc=com

dn: cn=<PERSON>,ou=Users,dc=example,dc=com
objectClass: top
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
cn: Santa Claus
sn: Claus
uid: sclaus
userPassword: password

dn: cn=<PERSON>,ou=Users,dc=example,dc=com
objectClass: top
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
cn: <PERSON>
sn: <PERSON><PERSON>
uid: j<PERSON>beck
userPassword: password

dn: ou=referral test,dc=example,dc=com
objectClass: extensibleObject
objectClass: referral
objectClass: top
objectClass: person
ref: ldap://localhost:10389/dc=example,dc=com