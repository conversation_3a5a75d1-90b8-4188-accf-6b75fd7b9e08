<?xml version="1.0" encoding="UTF-8"?>
<UnitTestSuiteConfig xmlns="http://zstack.org/schema/zstack" timeout="600">
	<TestCase class="org.zstack.test.network.TestCreateL2NoVlanNetwork"/>
	<TestCase class="org.zstack.test.network.TestCreateL3BasicNetwork"/>
	<TestCase class="org.zstack.test.network.TestDeleteL2NoVlanNetwork"/>
	<TestCase class="org.zstack.test.network.TestDeleteL3BasicNetwork"/>
	<TestCase class="org.zstack.test.network.TestListL2NoVlanNetwork"/>
	<TestCase class="org.zstack.test.network.TestListL3Network"/>
	<TestCase class="org.zstack.test.network.TestL2NetworkDeleteExtension"/>
	<TestCase class="org.zstack.test.network.TestL3NetworkDeleteExtension"/>
	<TestCase class="org.zstack.test.network.TestAddIpRange"/>
    <TestCase class="org.zstack.test.network.TestAddIpRangeByCidr"/>
	<TestCase class="org.zstack.test.network.TestDeleteIpRange"/>
	<TestCase class="org.zstack.test.network.TestAcquireIp"/>
	<TestCase class="org.zstack.test.network.TestAcquireIp1"/>
	<TestCase class="org.zstack.test.network.TestReleaseIp"/>
	<TestCase class="org.zstack.test.network.TestListIpRange"/>
	<TestCase class="org.zstack.test.network.TestFirstAvailableIpAllocatorStrategy1"/>
	<TestCase class="org.zstack.test.network.TestFirstAvailableIpAllocatorStrategy2"/>
	<TestCase class="org.zstack.test.network.TestFirstAvailableIpAllocatorStrategyConcurrent"/>
	<TestCase class="org.zstack.test.network.TestFirstAvailableIpAllocatorStrategyFailure"/>
	<TestCase class="org.zstack.test.network.TestFirstAvailableIpAllocatorStrategyReturnIp"/>
	<TestCase class="org.zstack.test.network.TestFirstAvailableConcurrentAllocateAndReturnIp"/>
    <TestCase class="org.zstack.test.network.TestRandomIpAllocatorStrategy1"/>
    <TestCase class="org.zstack.test.network.TestRandomIpAllocatorStrategy2"/>
    <TestCase class="org.zstack.test.network.TestRandomIpAllocatorStrategy3"/>
    <TestCase class="org.zstack.test.network.TestRandomIpAllocatorStrategy4"/>
    <TestCase class="org.zstack.test.network.TestRandomIpAllocatorStrategy5"/>
	<TestCase class="org.zstack.test.network.TestAddDnsToL3Network"/>
	<TestCase class="org.zstack.test.network.TestRemoveDnsFromL3Network"/>
	<TestCase class="org.zstack.test.network.TestAttachL2NetworkToCluster"/>
    <TestCase class="org.zstack.test.network.TestAttachL2NetworkToCluster1"/>
    <TestCase class="org.zstack.test.network.TestAttachL2NetworkToCluster2"/>
	<TestCase class="org.zstack.test.network.TestDetachL2NetworkFromCluster"/>
	<TestCase class="org.zstack.test.network.TestQueryL2Network"/>
	<TestCase class="org.zstack.test.network.TestQueryL2VlanNetwork"/>
	<TestCase class="org.zstack.test.network.TestQueryL3Network"/>
	<TestCase class="org.zstack.test.network.TestQueryIpRange"/>
	<TestCase class="org.zstack.test.network.TestQueryNetworkServiceL3NetworkRef"/>
	<TestCase class="org.zstack.test.network.TestQueryNetworkServiceProvider"/>
	<TestCase class="org.zstack.test.network.TestCreateL2VlanNetwork"/>
	<TestCase class="org.zstack.test.network.TestListL2VlanNetwork"/>
    <TestCase class="org.zstack.test.network.TestGetNetworkServiceTypes"/>
    <TestCase class="org.zstack.test.network.TestGetL3NetworkTypes"/>
    <TestCase class="org.zstack.test.network.TestGetL2NetworkTypes"/>
    <TestCase class="org.zstack.test.network.TestChangeL3BasicNetworkState"/>
    <TestCase class="org.zstack.test.network.TestQueryVip"/>
    <TestCase class="org.zstack.test.network.TestGetIpCapacity"/>
    <TestCase class="org.zstack.test.network.TestChangeVipState"/>
    <TestCase class="org.zstack.test.network.TestAcquireRequiredVip"/>
	<TestCase class="org.zstack.test.network.TestAcquireRequiredVip1"/>
    <TestCase class="org.zstack.test.network.TestAttachNetworkServiceL3NetworkFailure"/>
	<TestCase class="org.zstack.test.network.TestUpdateL2Network"/>
	<TestCase class="org.zstack.test.network.TestUpdateL3Network"/>
	<TestCase class="org.zstack.test.network.TestUpdateIpRange"/>
	<TestCase class="org.zstack.test.network.TestCheckIp"/>
	<TestCase class="org.zstack.test.network.TestPolicyForL3Network"/>
	<TestCase class="org.zstack.test.network.TestPolicyForL3Network1"/>
</UnitTestSuiteConfig>
