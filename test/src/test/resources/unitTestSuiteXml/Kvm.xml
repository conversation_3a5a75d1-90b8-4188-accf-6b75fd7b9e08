<?xml version="1.0" encoding="UTF-8"?>
<UnitTestSuiteConfig xmlns="http://zstack.org/schema/zstack" timeout="600">
  <TestCase class="org.zstack.test.kvm.TestAddKvmHost"/>
  <TestCase class="org.zstack.test.kvm.TestKvmAgentRestart"/>
  <TestCase class="org.zstack.test.kvm.TestAddKvmHostConncectException"/>
  <TestCase class="org.zstack.test.kvm.TestAddKvmHostConncectFailure"/>
  <TestCase class="org.zstack.test.kvm.TestAddKvmHostFactException"/>
  <TestCase class="org.zstack.test.kvm.TestAddKvmHostFactFailure"/>
  <TestCase class="org.zstack.test.kvm.TestAttachDataVolumeToVmOnKvm"/>
  <TestCase class="org.zstack.test.kvm.TestAttachDataVolumeToVmOnKvm2"/>
  <TestCase class="org.zstack.test.kvm.TestAttachDataVolumeToVmOnKvmFailure"/>
  <TestCase class="org.zstack.test.kvm.TestKvmNfsPrimaryStorage"/>
  <TestCase class="org.zstack.test.kvm.TestKvmNfsPrimaryStorage2"/>
  <TestCase class="org.zstack.test.kvm.TestKvmNfsPrimaryStorageMountException"/>
  <TestCase class="org.zstack.test.kvm.TestKvmNfsPrimaryStorageMountFailure"/>
  <TestCase class="org.zstack.test.kvm.TestKvmNfsPrimaryStorageUnmount"/>
  <TestCase class="org.zstack.test.kvm.TestKvmNfsPrimaryStorageUnmountException"/>
  <TestCase class="org.zstack.test.kvm.TestKvmNfsPrimaryStorageUnmountFailure"/>
  <TestCase class="org.zstack.test.kvm.TestAddKvmCheckPhysicalNetworkInterface"/>
  <TestCase class="org.zstack.test.kvm.TestAddKvmCheckPhysicalNetworkInterface2"/>
  <TestCase class="org.zstack.test.kvm.TestAddKvmCheckPhysicalNetworkInterfaceException"/>
  <TestCase class="org.zstack.test.kvm.TestAddKvmCheckPhysicalNetworkInterfaceFailure"/>
  <TestCase class="org.zstack.test.kvm.TestKVMRealizeL2NoVlanNetworkBackend"/>
  <TestCase class="org.zstack.test.kvm.TestKVMRealizeL2NoVlanNetworkBackendFailure"/>
  <TestCase class="org.zstack.test.kvm.TestCreateVmOnKvm"/>
  <TestCase class="org.zstack.test.kvm.TestCreateVmOnKvm1"/>
  <TestCase class="org.zstack.test.kvm.TestCreateVmOnKvm2"/>
  <TestCase class="org.zstack.test.kvm.TestCreateVmOnKvmFailure"/>
  <TestCase class="org.zstack.test.kvm.TestStartVmOnKvmExtensionPoint"/>
  <TestCase class="org.zstack.test.kvm.TestStopVmOnKvm"/>
  <TestCase class="org.zstack.test.kvm.TestStopVmOnKvmFailure"/>
  <TestCase class="org.zstack.test.kvm.TestStopVmOnKvmExtensionPoint"/>
  <TestCase class="org.zstack.test.kvm.TestRebootVmOnKvm"/>
  <!--
  <TestCase class="org.zstack.test.kvm.TestRebootVmOnKvmFailure"/>
  <TestCase class="org.zstack.test.kvm.TestRebootVmOnKvmExtensionPoint"/>
  -->
  <TestCase class="org.zstack.test.kvm.TestDestroyVmOnKvm"/>
  <TestCase class="org.zstack.test.kvm.TestDestroyVmOnKvm1"/>
  <TestCase class="org.zstack.test.kvm.TestDestroyVmOnKvm3"/>
  <TestCase class="org.zstack.test.kvm.TestDestroyVmOnKvm4"/>
  <TestCase class="org.zstack.test.kvm.TestDestroyVmOnKvm5"/>
  <TestCase class="org.zstack.test.kvm.TestDestroyVmOnKvm6"/>
  <TestCase class="org.zstack.test.kvm.TestDestroyVmOnKvm7"/>
  <TestCase class="org.zstack.test.kvm.TestDestroyVmOnKvmFailure"/>
  <TestCase class="org.zstack.test.kvm.TestDestroyVmOnKvmExtensionPoint"/>
  <TestCase class="org.zstack.test.kvm.TestCreateVmOnKvmFromIso"/>
  <TestCase class="org.zstack.test.kvm.TestKVMRealizeL2VlanNetworkBackendFailure"/>
  <TestCase class="org.zstack.test.kvm.TestKVMRealizeL2VlanNetworkBackend"/>
  <TestCase class="org.zstack.test.kvm.TestDetachDataVolumeFromVmOnKvm"/>
  <TestCase class="org.zstack.test.kvm.TestDetachDataVolumeFromVmOnKvmFailure"/>
  <TestCase class="org.zstack.test.kvm.TestAttachNicOnKvm"/>
  <TestCase class="org.zstack.test.kvm.TestAttachNicOnKvm1"/>
  <TestCase class="org.zstack.test.kvm.TestAttachNicOnKvm2"/>
  <TestCase class="org.zstack.test.kvm.TestAttachNicOnKvm3"/>
  <TestCase class="org.zstack.test.kvm.TestAttachNicOnKvmFailure"/>
  <TestCase class="org.zstack.test.kvm.TestKvmAttachL2Network"/>
  <TestCase class="org.zstack.test.kvm.TestKvmAttachL2Network1"/>
  <TestCase class="org.zstack.test.kvm.TestKvmAttachL2Network2"/>
  <TestCase class="org.zstack.test.kvm.TestKvmAttachL2Network3"/>
  <TestCase class="org.zstack.test.kvm.TestKvmAttachL2Network4"/>
  <TestCase class="org.zstack.test.kvm.TestKvmAttachL2Network5"/>
  <TestCase class="org.zstack.test.kvm.TestKvmAttachL2Network6"/>
  <TestCase class="org.zstack.test.kvm.TestMigrateVmOnKvm"/>
  <TestCase class="org.zstack.test.kvm.TestMigrateVmOnKvm1"/>
  <TestCase class="org.zstack.test.kvm.TestMigrateVmOnKvm2"/>
  <TestCase class="org.zstack.test.kvm.TestMigrateVmOnKvm3"/>
  <TestCase class="org.zstack.test.kvm.TestMigrateVmOnKvm4"/>
  <TestCase class="org.zstack.test.kvm.TestMigrateVmOnKvm5"/>
  <TestCase class="org.zstack.test.kvm.TestMigrateVmOnKvm6"/>
  <TestCase class="org.zstack.test.kvm.TestMigrateVmOnKvm7"/>
  <TestCase class="org.zstack.test.kvm.TestKvmMaintenanceMode"/>
  <TestCase class="org.zstack.test.kvm.TestKvmMaintenanceMode1"/>
  <TestCase class="org.zstack.test.kvm.TestKvmMaintenanceMode2"/>
  <TestCase class="org.zstack.test.kvm.TestKvmMaintenanceMode3"/>
  <TestCase class="org.zstack.test.kvm.TestKvmMaintenanceMode4"/>
  <TestCase class="org.zstack.test.kvm.TestKvmMaintenanceMode5"/>
  <TestCase class="org.zstack.test.kvm.TestKvmMaintenanceMode6"/>
  <TestCase class="org.zstack.test.kvm.TestKvmMaintenanceMode7"/>
  <TestCase class="org.zstack.test.kvm.TestKvmMaintenanceMode8"/>
  <TestCase class="org.zstack.test.kvm.TestDeleteDataVolumeOnKvm"/>
  <TestCase class="org.zstack.test.kvm.TestDetachDataVolumeFromVmOnKvm1"/>
  <TestCase class="org.zstack.test.kvm.TestVmErrorOnKvm"/>
  <TestCase class="org.zstack.test.kvm.TestVmErrorOnKvm1"/>
  <TestCase class="org.zstack.test.kvm.TestVmErrorOnKvm3"/>
  <TestCase class="org.zstack.test.kvm.TestVmErrorOnKvm4"/>
  <TestCase class="org.zstack.test.kvm.TestVmErrorOnKvm5"/>
  <TestCase class="org.zstack.test.kvm.TestVmErrorOnKvm6"/>
  <TestCase class="org.zstack.test.kvm.TestVmErrorOnKvm7"/>
  <TestCase class="org.zstack.test.kvm.TestMaxDataVolumeNumberOnKvm"/>
  <TestCase class="org.zstack.test.kvm.TestCreateDataVolumeTemplate"/>
  <TestCase class="org.zstack.test.kvm.TestCreateDataVolumeTemplate1"/>
  <TestCase class="org.zstack.test.kvm.TestCreateDataVolumeTemplate2"/>
  <TestCase class="org.zstack.test.kvm.TestCreateDataVolumeTemplate3"/>
  <TestCase class="org.zstack.test.kvm.TestCreateDataVolumeTemplate4"/>
  <TestCase class="org.zstack.test.kvm.TestCreateDataVolumeTemplate5"/>
  <TestCase class="org.zstack.test.kvm.TestCreateDataVolumeTemplate6"/>
  <TestCase class="org.zstack.test.kvm.TestKvmMaintenanceModeAttachPrimaryStorage"/>
  <TestCase class="org.zstack.test.kvm.TestKvmMaintenanceModeAttachL2Network"/>
  <TestCase class="org.zstack.test.kvm.TestCreateVmOnKvmIso"/>
  <TestCase class="org.zstack.test.kvm.TestUpdateKvmHost"/>
  <TestCase class="org.zstack.test.kvm.TestSyncPrimaryStorageCapacityOnKvm"/>
  <TestCase class="org.zstack.test.kvm.TestDetachNicOnKvm"/>
  <TestCase class="org.zstack.test.kvm.TestDetachNicOnKvm1"/>
  <TestCase class="org.zstack.test.kvm.TestDetachNicOnKvm2"/>
  <TestCase class="org.zstack.test.kvm.TestDetachNicOnKvm3"/>
  <TestCase class="org.zstack.test.kvm.TestDetachNicOnKvm4"/>
  <TestCase class="org.zstack.test.kvm.TestDetachNicOnKvm5"/>
  <TestCase class="org.zstack.test.kvm.TestDetachNicOnKvm6"/>
  <TestCase class="org.zstack.test.kvm.TestDetachNicOnKvm7"/>
  <TestCase class="org.zstack.test.kvm.TestDetachNicOnKvm9"/>
  <TestCase class="org.zstack.test.kvm.TestDetachNicOnKvm10"/>
  <TestCase class="org.zstack.test.kvm.TestDetachNicOnKvm11"/>
  <TestCase class="org.zstack.test.kvm.TestVmSocketOnKvm"/>
  <TestCase class="org.zstack.test.kvm.TestAttachIsoOnKvm"/>
  <TestCase class="org.zstack.test.kvm.TestKvmHostCapacityOnFailure"/>
  <TestCase class="org.zstack.test.kvm.TestKvmVmTracer"/>
  <!--
  <TestCase class="org.zstack.test.kvm.TestKvmVmTracer1"/>
  -->
  <TestCase class="org.zstack.test.kvm.TestKvmFailureCheckState"/>
  <TestCase class="org.zstack.test.kvm.TestVmBootOrderOnKvm"/>
  <TestCase class="org.zstack.test.kvm.TestKvmPingHost"/>
  <TestCase class="org.zstack.test.kvm.TestKvmReconnectMe"/>
  <TestCase class="org.zstack.test.kvm.TestKvmReconnectHost"/>
  <TestCase class="org.zstack.test.kvm.TestKvmExtendCpuMemory"/>
  <TestCase class="org.zstack.test.kvm.TestDeleteL2NetworkOnKvm"/>
  <TestCase class="org.zstack.test.kvm.TestKvmVolumeCacheMode1"/>

  <TestCase class="org.zstack.test.kvm.TestDeleteIpRangeOnKvm"/>
  <TestCase class="org.zstack.test.kvm.TestDeleteIpRangeOnKvm1"/>
  <TestCase class="org.zstack.test.kvm.TestDeleteIpRangeOnKvm2"/>

  <TestCase class="org.zstack.test.kvm.TestStartVmOnTargetHost"/>
  <TestCase class="org.zstack.test.kvm.TestStartVmOnTargetHost1"/>
  <TestCase class="org.zstack.test.kvm.TestVmErrorRootVolumeRollback"/>
  <TestCase class="org.zstack.test.kvm.TestVmSshKey"/>
  <TestCase class="org.zstack.test.kvm.TestKvmPingAgentExtension"/>
  <TestCase class="org.zstack.test.kvm.TestGetCandidatesForCreatingVm"/>
  <TestCase class="org.zstack.test.kvm.TestGetCandidatesForCreatingVm1"/>
  <TestCase class="org.zstack.test.kvm.TestCreateVmNotStartOnKvm"/>
  <TestCase class="org.zstack.test.kvm.TestStartVmWithKvmIso"/>
</UnitTestSuiteConfig>
