<?xml version="1.0" encoding="UTF-8"?>
<UnitTestSuiteConfig xmlns="http://zstack.org/schema/zstack" timeout="600">
	<TestCase class="org.zstack.test.virtualrouter.TestAddVirtualRouterOffering"/>
	<TestCase class="org.zstack.test.virtualrouter.TestStartVirtualRouter"/>
	<TestCase class="org.zstack.test.virtualrouter.TestStartVirtualRouter2"/>
	<TestCase class="org.zstack.test.virtualrouter.TestStartVirtualRouter3"/>
	<TestCase class="org.zstack.test.virtualrouter.TestStartVirtualRouter4"/>
	<TestCase class="org.zstack.test.virtualrouter.TestStartVirtualRouter5"/>
	<TestCase class="org.zstack.test.virtualrouter.TestStartVirtualRouter6"/>
    <TestCase class="org.zstack.test.virtualrouter.TestStartVirtualRouter7"/>
    <TestCase class="org.zstack.test.virtualrouter.TestStartVirtualRouter8"/>
    <TestCase class="org.zstack.test.virtualrouter.TestStartVirtualRouter9"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterDhcpFailure"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterApplyExistingDhcp"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterDns"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterDns1"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterDns2"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterDnsOnStart"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterSNAT"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterSNATOnStart"/>
	<TestCase class="org.zstack.test.virtualrouter.TestRemoveDhcpFromVirtualRouter"/>
	<TestCase class="org.zstack.test.virtualrouter.TestRemoveDhcpFromVirtualRouter2"/>
	<TestCase class="org.zstack.test.virtualrouter.TestRemoveDhcpFromVirtualRouterFailure"/>
	<TestCase class="org.zstack.test.virtualrouter.TestQueryVirtualRouterOffering"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterFirewall"/>
    <TestCase class="org.zstack.test.virtualrouter.TestStartVirtualRouterFailure"/>
    <TestCase class="org.zstack.test.virtualrouter.TestCreateVirtualRouterFailure"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterServiceAttaching"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterOfferingDelete"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterOnIpRangeDelete"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterDMZ"/>
    <TestCase class="org.zstack.test.virtualrouter.TestQueryVirtualRouterVm"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPing"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPing1"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPing2"/>
	<TestCase class="org.zstack.test.virtualrouter.TestPolicyForVirtualRouterOffering"/>
	<TestCase class="org.zstack.test.virtualrouter.TestUpdateVirtualRouterOffering"/>
	<TestCase class="org.zstack.test.virtualrouter.TestAddVirtualRouterOffering1"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterAttachNetworkService"/>
</UnitTestSuiteConfig>
