<?xml version="1.0" encoding="UTF-8"?>
<UnitTestSuiteConfig xmlns="http://zstack.org/schema/zstack" timeout="600">
    <TestCase class="org.zstack.test.storage.ceph.TestCephBackupStorage"/>
    <TestCase class="org.zstack.test.storage.ceph.TestCeph1"/>
    <TestCase class="org.zstack.test.storage.ceph.TestCeph2"/>
    <TestCase class="org.zstack.test.storage.ceph.TestCeph4"/>
    <TestCase class="org.zstack.test.storage.ceph.TestCeph5"/>
    <TestCase class="org.zstack.test.storage.ceph.TestCeph6"/>
    <TestCase class="org.zstack.test.storage.ceph.TestCeph7"/>
    <TestCase class="org.zstack.test.storage.ceph.TestCeph8"/>
    <TestCase class="org.zstack.test.storage.ceph.TestCeph9"/>
    <TestCase class="org.zstack.test.storage.ceph.TestCeph10"/>
    <TestCase class="org.zstack.test.storage.ceph.TestCeph11"/>
    <TestCase class="org.zstack.test.storage.ceph.TestCeph12"/>
    <TestCase class="org.zstack.test.storage.ceph.TestCeph13"/>
    <TestCase class="org.zstack.test.storage.ceph.TestCeph14"/>
    <TestCase class="org.zstack.test.storage.ceph.TestCeph15"/>
    <TestCase class="org.zstack.test.storage.ceph.TestCeph16"/>
    <TestCase class="org.zstack.test.storage.ceph.TestCeph17"/>
    <TestCase class="org.zstack.test.storage.ceph.TestCeph18"/>
    <TestCase class="org.zstack.test.storage.ceph.TestCeph19"/>
    <TestCase class="org.zstack.test.storage.ceph.TestCeph20"/>
    <TestCase class="org.zstack.test.storage.ceph.TestPingCephPs1"/>
    <TestCase class="org.zstack.test.storage.ceph.TestPingCephBs1"/>
    <TestCase class="org.zstack.test.storage.ceph.TestUpdateCephPrimaryStorageMon"/>
    <TestCase class="org.zstack.test.storage.ceph.TestUpdateCephBackupStorageMon"/>
    <TestCase class="org.zstack.test.storage.ceph.TestCephCreateDataVolume"/>
</UnitTestSuiteConfig>