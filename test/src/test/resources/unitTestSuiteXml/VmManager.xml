<?xml version="1.0" encoding="UTF-8"?>
<UnitTestSuiteConfig xmlns="http://zstack.org/schema/zstack" timeout="600">
    <TestCase class="org.zstack.test.compute.vm.TestVmAllocateNicFlow"/>
    <TestCase class="org.zstack.test.compute.vm.TestVmCreateOnHypervisorFlow"/>
    <TestCase class="org.zstack.test.compute.vm.TestCreateVm"/>
    <TestCase class="org.zstack.test.compute.vm.CreateVmForSameL3"/>
    <TestCase class="org.zstack.test.compute.vm.TestDestroyVm"/>
    <TestCase class="org.zstack.test.compute.vm.TestDestroyVm1"/>
    <TestCase class="org.zstack.test.compute.vm.TestRebootVm"/>
    <TestCase class="org.zstack.test.compute.vm.TestRebootVmFailure"/>
    <TestCase class="org.zstack.test.compute.vm.TestStopVm"/>
    <TestCase class="org.zstack.test.compute.vm.TestStartVm"/>
    <TestCase class="org.zstack.test.compute.vm.TestPauseVm"/>
    <TestCase class="org.zstack.test.compute.vm.TestResumeVm"/>
    <TestCase class="org.zstack.test.compute.vm.TestStartCreatedVmExtensionPoint"/>
    <TestCase class="org.zstack.test.compute.vm.TestDestroyVmExtensionPoint"/>
    <TestCase class="org.zstack.test.compute.vm.TestRebootVmExtensionPoint"/>
    <TestCase class="org.zstack.test.compute.vm.TestStopVmExtensionPoint"/>
    <TestCase class="org.zstack.test.compute.vm.TestStartVmExtensionPoint"/>
    <TestCase class="org.zstack.test.compute.vm.TestCreateVmFailure"/>
    <TestCase class="org.zstack.test.compute.vm.TestCreateVmFailure1"/>
    <TestCase class="org.zstack.test.compute.vm.TestMigrateVm"/>
    <TestCase class="org.zstack.test.compute.vm.TestMigrateVm2"/>
    <TestCase class="org.zstack.test.compute.vm.TestMigrateVm3"/>
    <TestCase class="org.zstack.test.compute.vm.TestMigrateVm4"/>
    <TestCase class="org.zstack.test.compute.vm.TestMigrateVmExtensionPoint"/>
    <TestCase class="org.zstack.test.compute.vm.TestAttachVolumeToVm"/>
    <TestCase class="org.zstack.test.compute.vm.TestAttachVolumeToVm2"/>
    <TestCase class="org.zstack.test.compute.vm.TestAttachVolumeToVm3"/>
    <TestCase class="org.zstack.test.compute.vm.TestAttachVolumeToVmDeviceId"/>
    <TestCase class="org.zstack.test.compute.vm.TestAttachDataVolumeFromDestoryedVm"/>
    <TestCase class="org.zstack.test.compute.vm.TestDetachVolumeFromVm"/>
    <TestCase class="org.zstack.test.compute.vm.TestDetachVolumeFromVm2"/>
    <TestCase class="org.zstack.test.compute.vm.TestStartVmWithAttachedVolume"/>
    <TestCase class="org.zstack.test.compute.vm.TestAttachedVolumeToStoppedVm"/>
    <TestCase class="org.zstack.test.compute.vm.TestVmStateTracer"/>
    <TestCase class="org.zstack.test.compute.vm.TestVmStateTracer1"/>
    <TestCase class="org.zstack.test.compute.vm.TestVmStateTracer2"/>
    <TestCase class="org.zstack.test.compute.vm.TestVmStateTracer3"/>
    <TestCase class="org.zstack.test.compute.vm.TestVmStateTracer4"/>
    <TestCase class="org.zstack.test.compute.vm.TestVmStateTracer5"/>
    <TestCase class="org.zstack.test.compute.vm.TestVmStateTracer6"/>
    <TestCase class="org.zstack.test.compute.vm.TestVmStateTracer7"/>
    <TestCase class="org.zstack.test.compute.vm.TestQueryVm"/>
    <TestCase class="org.zstack.test.compute.vm.TestQueryVm2"/>
    <TestCase class="org.zstack.test.compute.vm.TestQueryVm3"/>
    <TestCase class="org.zstack.test.compute.vm.TestQueryVm4"/>
    <TestCase class="org.zstack.test.compute.vm.TestQueryVm5"/>
    <TestCase class="org.zstack.test.compute.vm.TestQueryVmNic"/>
    <TestCase class="org.zstack.test.compute.vm.TestVmStaticIp"/>
    <TestCase class="org.zstack.test.compute.vm.TestVmStaticIp1"/>
    <TestCase class="org.zstack.test.compute.vm.TestVmStaticIp2"/>
    <TestCase class="org.zstack.test.compute.vm.TestVmStaticIp3"/>
    <TestCase class="org.zstack.test.compute.vm.TestVmStaticIp4"/>
    <TestCase class="org.zstack.test.compute.vm.TestCreateVmWithMultipleSameDataVolume"/>
    <TestCase class="org.zstack.test.compute.vm.TestPolicyForVm"/>
    <TestCase class="org.zstack.test.compute.vm.TestPolicyForVm1"/>
    <TestCase class="org.zstack.test.compute.vm.TestPolicyForVm2"/>
    <TestCase class="org.zstack.test.compute.vm.TestPolicyForVm3"/>
    <TestCase class="org.zstack.test.compute.vm.TestPolicyForVm4"/>
    <TestCase class="org.zstack.test.compute.vm.TestPolicyForVm5"/>
    <TestCase class="org.zstack.test.compute.vm.TestPolicyForVm6"/>
    <TestCase class="org.zstack.test.compute.vm.TestQueryVm6"/>
    <TestCase class="org.zstack.test.compute.vm.TestChangeVmInstanceOffering"/>
    <TestCase class="org.zstack.test.compute.vm.TestChangeVmPassword"/>
    <TestCase class="org.zstack.test.compute.vm.TestChangeVmInstanceOffering1"/>
    <TestCase class="org.zstack.test.compute.vm.TestVmGetAttachableL3Networks"/>
    <TestCase class="org.zstack.test.compute.vm.TestQuotaUsageForVm"/>
    <TestCase class="org.zstack.test.compute.vm.TestVmHostname"/>
    <TestCase class="org.zstack.test.compute.vm.TestGetInterdependentL3NetworksImages"/>
    <TestCase class="org.zstack.test.compute.vm.TestGetVmAttachableDataVolume"/>
    <TestCase class="org.zstack.test.compute.vm.TestGetVmCapabilities"/>
</UnitTestSuiteConfig>