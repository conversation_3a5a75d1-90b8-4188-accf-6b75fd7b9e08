<?xml version="1.0" encoding="UTF-8"?>
<UnitTestSuiteConfig xmlns="http://zstack.org/schema/zstack" timeout="600">
	<TestCase class="org.zstack.test.securitygroup.TestCreateSecurityGroup"/>
    <TestCase class="org.zstack.test.securitygroup.TestChangeSecurityGroupState"/>
	<TestCase class="org.zstack.test.securitygroup.TestAddSecurityGroupRule"/>
	<TestCase class="org.zstack.test.securitygroup.TestApplySecurityGroupRuleToVmFailureRetry"/>
	<TestCase class="org.zstack.test.securitygroup.TestApplySecurityGroupRuleToVmOnKvm"/>
	<TestCase class="org.zstack.test.securitygroup.TestApplySecurityGroupRuleToVmOnKvm2"/>
	<TestCase class="org.zstack.test.securitygroup.TestApplySecurityGroupRuleToVmOnKvm3"/>
	<TestCase class="org.zstack.test.securitygroup.TestApplySecurityGroupRuleToVmOnKvm4"/>
	<TestCase class="org.zstack.test.securitygroup.TestRemoveSecurityGroupRuleOfVmOnKvm"/>
	<TestCase class="org.zstack.test.securitygroup.TestDeleteSecurityGroup"/>
	<TestCase class="org.zstack.test.securitygroup.TestSecurityGroupRuleOnVmDestroyed"/>
	<TestCase class="org.zstack.test.securitygroup.TestSecurityGroupRuleOnVmStopped"/>
	<TestCase class="org.zstack.test.securitygroup.TestSecurityGroupRuleInTwoGroup"/>
	<TestCase class="org.zstack.test.securitygroup.TestSecurityGroupRuleInTwoGroup2"/>
	<TestCase class="org.zstack.test.securitygroup.TestSecurityGroupRuleInTwoGroup3"/>
	<TestCase class="org.zstack.test.securitygroup.TestSecurityGroupRuleInTwoGroup4"/>
	<TestCase class="org.zstack.test.securitygroup.TestSecurityGroupRuleInTwoGroup5"/>
	<TestCase class="org.zstack.test.securitygroup.TestSecurityGroupRuleRandom" timeout="180"/>
	<TestCase class="org.zstack.test.securitygroup.TestSecurityGroupOnMultipleNetworks"/>
	<TestCase class="org.zstack.test.securitygroup.TestSecurityGroupOnMultipleNetworks2"/>
	<TestCase class="org.zstack.test.securitygroup.TestSecurityGroupOnMultipleNetworks3"/>
	<TestCase class="org.zstack.test.securitygroup.TestSecurityGroupOnMultipleNetworks4"/>
	<TestCase class="org.zstack.test.securitygroup.TestSecurityGroupOnMultipleNetworks5"/>
	<TestCase class="org.zstack.test.securitygroup.TestSecurityGroupOnMultipleNetworks6"/>
	<TestCase class="org.zstack.test.securitygroup.TestSecurityGroupOnMultipleNetworks7"/>
	<TestCase class="org.zstack.test.securitygroup.TestSecurityGroupOnMultipleNetworks8"/>
	<TestCase class="org.zstack.test.securitygroup.TestSecurityGroupOnMultipleNetworks9"/>
	<TestCase class="org.zstack.test.securitygroup.TestSecurityGroupOnMultipleNetworks10"/>
	<TestCase class="org.zstack.test.securitygroup.TestSecurityGroupOnMultipleNetworks11"/>
    <TestCase class="org.zstack.test.securitygroup.TestSecurityGroupOnMultipleNetworks12"/>
	<TestCase class="org.zstack.test.securitygroup.TestQuerySecurityGroup"/>
    <TestCase class="org.zstack.test.securitygroup.TestQueryCountSecurityGroup"/>
	<TestCase class="org.zstack.test.securitygroup.TestQueryVmNicInSecurityGroup"/>
	<TestCase class="org.zstack.test.securitygroup.TestRemoveVmNicFromSecurityGroupOnVmStopped"/>
	<TestCase class="org.zstack.test.securitygroup.TestRemoveVmNicFromSecurityGroupOnVmStopped2"/>
    <TestCase class="org.zstack.test.securitygroup.TestSecurityGroupRulesForVmMigration"/>
    <TestCase class="org.zstack.test.securitygroup.TestSecurityGroupRulesForVmMigration1"/>
    <TestCase class="org.zstack.test.securitygroup.TestQuerySecurityGroupRule"/>
    <TestCase class="org.zstack.test.securitygroup.TestGetCandidateVmNicForSecurityGroup"/>
	<TestCase class="org.zstack.test.securitygroup.TestGetCandidateVmNicForSecurityGroup1"/>
    <TestCase class="org.zstack.test.securitygroup.TestKvmSecurityGroupRefreshOnReconnect"/>
	<TestCase class="org.zstack.test.securitygroup.TestUpdateSecurityGroup"/>
	<TestCase class="org.zstack.test.securitygroup.TestPolicyForSecurityGroup"/>
	<TestCase class="org.zstack.test.securitygroup.TestPolicyForSecurityGroup1"/>
	<TestCase class="org.zstack.test.securitygroup.TestSecurityGroupVmNicNotInAttachedL3"/>
</UnitTestSuiteConfig>