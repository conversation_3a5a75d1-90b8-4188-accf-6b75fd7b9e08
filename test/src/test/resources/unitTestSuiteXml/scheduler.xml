<?xml version="1.0" encoding="UTF-8"?>
<UnitTestSuiteConfig xmlns="http://zstack.org/schema/zstack" timeout="600">
    <TestCase class="org.zstack.test.core.scheduler.TestSchedulerCreateVolumeSnapshot"/>
    <TestCase class="org.zstack.test.core.scheduler.TestSchedulerUpdate"/>
    <TestCase class="org.zstack.test.core.scheduler.TestSchedulerDelete"/>
    <TestCase class="org.zstack.test.core.scheduler.TestSchedulerChangeVmStatus"/>
    <TestCase class="org.zstack.test.core.scheduler.TestSchedulerReload"/>
    <TestCase class="org.zstack.test.compute.vm.TestStartVmScheduler"/>
    <TestCase class="org.zstack.test.compute.vm.TestStopVmScheduler"/>
    <TestCase class="org.zstack.test.compute.vm.TestRebootVmScheduler"/>
</UnitTestSuiteConfig>
