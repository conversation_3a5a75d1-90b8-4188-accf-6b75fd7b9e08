<?xml version="1.0" encoding="UTF-8"?>
<UnitTestSuiteConfig xmlns="http://zstack.org/schema/zstack" timeout="600">
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding2"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding3"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding4"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding5"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding6"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding7"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding8"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding9"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding10"/>
	<TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding11"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding12"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding13"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding14"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding15"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding16"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding17"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding18"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding19"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding20"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding21"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding22"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding23"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding24"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding25"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding26"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding27"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding28"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding30"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding31"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding32"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding33"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding34"/>
    <TestCase class="org.zstack.test.virtualrouter.TestVirtualRouterPortForwarding35"/>
    <TestCase class="org.zstack.test.virtualrouter.TestUpdateVirtualRouterPortForwardingAndVip"/>
    <TestCase class="org.zstack.test.virtualrouter.TestPolicyForPortForwarding"/>
    <TestCase class="org.zstack.test.virtualrouter.TestPolicyForPortForwarding1"/>
</UnitTestSuiteConfig>
