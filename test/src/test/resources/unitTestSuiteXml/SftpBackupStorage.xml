<?xml version="1.0" encoding="UTF-8"?>
<UnitTestSuiteConfig xmlns="http://zstack.org/schema/zstack" timeout="600">
	<TestCase class="org.zstack.test.storage.backup.sftp.TestAddSftpBackupStorage"/>
	<TestCase class="org.zstack.test.storage.backup.sftp.TestAddSftpBackupStorageFailure"/>
	<TestCase class="org.zstack.test.storage.backup.sftp.TestSftpBackupStorageDeleteImage"/>
	<TestCase class="org.zstack.test.storage.backup.sftp.TestSftpBackupStorageDeleteImage2"/>
	<TestCase class="org.zstack.test.storage.backup.sftp.TestSftpBackupStorageDownloadImage"/>
	<TestCase class="org.zstack.test.storage.backup.sftp.TestSftpBackupStorageDownloadImageFailure"/>
	<TestCase class="org.zstack.test.storage.backup.sftp.TestSftpBackupStorageDownloadImageFailure2"/>
	<TestCase class="org.zstack.test.storage.backup.sftp.TestSftpBackupStoragePing"/>
	<TestCase class="org.zstack.test.storage.backup.sftp.TestSftpBackupStoragePingFailure1"/>
	<TestCase class="org.zstack.test.storage.backup.sftp.TestSftpBackupStoragePingFailure2"/>
	<TestCase class="org.zstack.test.storage.backup.sftp.TestSftpBackupStoragePingFailure3"/>
    <TestCase class="org.zstack.test.storage.backup.sftp.TestSftpBackupStorageReconnect"/>
	<TestCase class="org.zstack.test.storage.backup.sftp.TestUpdateSftpBackupStorage"/>
	<TestCase class="org.zstack.test.storage.backup.sftp.TestSftpBackupStorageExpungeImage"/>
	<TestCase class="org.zstack.test.storage.backup.sftp.TestSftpBackupStorageAddImageTimeoutManagement"/>
</UnitTestSuiteConfig>