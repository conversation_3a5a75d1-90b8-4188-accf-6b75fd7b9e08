<?xml version="1.0" encoding="UTF-8"?>
<UnitTestSuiteConfig xmlns="http://zstack.org/schema/zstack" timeout="600">
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion1"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion2"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion3"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion4"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion5"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion6"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion7"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion8"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion9"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion10"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion11"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion12"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion13"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion14"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion15"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion16"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion17"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion18"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion19"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion20"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion21"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion22"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion23"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion24"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion25"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion26"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion27"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion28"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion29"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion30"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion31"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion32"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion33"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion34"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion35"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletion36"/>
    <TestCase class="org.zstack.test.cascade.TestCascadeDeletionAddon"/>
</UnitTestSuiteConfig>