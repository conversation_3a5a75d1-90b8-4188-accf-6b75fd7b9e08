<?xml version="1.0" encoding="UTF-8"?>
<UnitTestSuiteConfig xmlns="http://zstack.org/schema/zstack" timeout="600">
    <TestCase class="org.zstack.test.mevoco.billing.TestBilling"/>
    <TestCase class="org.zstack.test.mevoco.billing.TestBilling1"/>
    <TestCase class="org.zstack.test.mevoco.billing.TestBilling2"/>
    <TestCase class="org.zstack.test.mevoco.billing.TestBilling3"/>
    <TestCase class="org.zstack.test.mevoco.billing.TestBilling4"/>
    <TestCase class="org.zstack.test.mevoco.billing.TestBilling5"/>
    <TestCase class="org.zstack.test.mevoco.billing.TestBilling6"/>
    <TestCase class="org.zstack.test.mevoco.billing.TestBilling7"/>
    <TestCase class="org.zstack.test.mevoco.billing.TestBilling8"/>
    <TestCase class="org.zstack.test.mevoco.billing.TestBilling9"/>
    <TestCase class="org.zstack.test.mevoco.billing.TestBilling10"/>
    <TestCase class="org.zstack.test.mevoco.billing.TestBilling11"/>
    <TestCase class="org.zstack.test.mevoco.billing.TestBilling12"/>
    <TestCase class="org.zstack.test.mevoco.billing.TestBilling13"/>
    <TestCase class="org.zstack.test.mevoco.billing.TestBilling14"/>
    <TestCase class="org.zstack.test.mevoco.billing.TestBillingHourPrice"/>
</UnitTestSuiteConfig>