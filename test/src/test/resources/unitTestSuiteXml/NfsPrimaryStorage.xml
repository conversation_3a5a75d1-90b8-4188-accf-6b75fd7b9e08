<?xml version="1.0" encoding="UTF-8"?>
<UnitTestSuiteConfig xmlns="http://zstack.org/schema/zstack" timeout="600">
	<TestCase class="org.zstack.test.storage.primary.nfs.TestExpungeVolumeOnNfsPrimaryStorageForKvm"/>
    <TestCase class="org.zstack.test.storage.primary.nfs.TestImageCacheMissing"/>
    <TestCase class="org.zstack.test.storage.primary.nfs.TestImageCacheMissingFailure"/>
    <TestCase class="org.zstack.test.storage.primary.nfs.TestDeleteNfsPrimaryStorage"/>
    <TestCase class="org.zstack.test.storage.primary.nfs.TestReconnectNfsPrimaryStorage1"/>
    <TestCase class="org.zstack.test.storage.primary.nfs.TestReconnectNfsPrimaryStorage2"/>
    <TestCase class="org.zstack.test.storage.primary.nfs.TestReconnectNfsPrimaryStorage3"/>
    <TestCase class="org.zstack.test.storage.primary.nfs.TestReconnectNfsPrimaryStorage4"/>
    <TestCase class="org.zstack.test.storage.primary.nfs.TestNfsPrimaryStorageStatus"/>
    <TestCase class="org.zstack.test.storage.primary.nfs.TestNfsMountOptions1"/>
    <TestCase class="org.zstack.test.storage.primary.nfs.TestNfsGetVolumeBaseImage"/>
    <TestCase class="org.zstack.test.storage.primary.nfs.TestImageCacheMissingUuid"/>
    <TestCase class="org.zstack.test.storage.primary.nfs.TestNfsImageCleaner2"/>
    <TestCase class="org.zstack.test.storage.primary.nfs.TestNfsUpdateUrl"/>
</UnitTestSuiteConfig>