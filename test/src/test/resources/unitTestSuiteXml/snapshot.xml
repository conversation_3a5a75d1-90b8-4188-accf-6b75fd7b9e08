<?xml version="1.0" encoding="UTF-8"?>
<UnitTestSuiteConfig xmlns="http://zstack.org/schema/zstack" timeout="600">
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm1"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm2"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm3"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm4"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm5"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm6"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm7"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm8"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm9"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm10"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm11"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm12"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm13"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm14"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm15"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm16"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm17"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm18"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm19"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm20"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm21"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm22"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm23"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm24"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm25"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm26"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm27"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm28"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm29"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm30"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm31"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm32"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm33"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm34"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm35"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm36"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm37"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm38"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm39"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm40"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm41"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm42"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm43"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm44"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm45"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm46"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm47"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm48"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestSnapshotOnKvm49"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestQuerySnapshot"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestQuerySnapshot1"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestGetSnapshotTree"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestQuerySnapshotTree"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestUpdateSnapshot"/>
    <TestCase class="org.zstack.test.storage.snapshot.TestPolicyForSnapshot"/>
</UnitTestSuiteConfig>