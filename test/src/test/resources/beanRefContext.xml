<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd">

    <bean id="messageSource" class="org.springframework.context.support.ReloadableResourceBundleMessageSource">
        <property name="basename" value="i18n/messages"></property>
        <property name="defaultEncoding" value="UTF-8"></property>
        <property name="alwaysUseMessageFormat" value="true"></property>
    </bean>


    <bean id="parentContext"
        class="org.springframework.context.support.ClassPathXmlApplicationContext">
        <constructor-arg>
            <list>
                <value>spring-config-for-unit-test-from-BeanConstructor.xml</value>
            </list>
        </constructor-arg>
    </bean>
</beans>
