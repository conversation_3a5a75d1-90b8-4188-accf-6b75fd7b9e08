<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
         http://www.springframework.org/schema/beans/spring-beans-3.0.xsd"
       default-init-method="init" default-destroy-method="destory">

    <import resource="hybrid/datacenter.xml"/>
    <import resource="hybrid/aliyun.xml"/>
    <import resource="hybrid/ecsoss.xml"/>
    <import resource="hybrid/network.xml"/>
    <import resource="hybrid/identityzone.xml"/>
    <import resource="hybrid/ecs.xml"/>
    <import resource="hybrid/ecsimage.xml"/>
    <import resource="hybrid/disk.xml"/>
    <import resource="hybrid/snapshot.xml"/>
    <import resource="hybrid/vpn.xml"/>
    <import resource="hybrid/connection.xml"/>
    <import resource="hybrid/hybridaccount.xml"/>
    <import resource="mevoco.xml"/>
    <import resource="license.xml"/>
    <import resource="encryptPremium.xml"/>
    <import resource="localStorage.xml"/>
    <import resource="localStorageSimulator.xml"/>
    <import resource="flatNetworkServiceSimulator.xml"/>
    <import resource="flatNetworkProvider.xml"/>
    <import resource="agentManager.xml"/>
    <import resource="KVMRelated.xml"/>
</beans>
