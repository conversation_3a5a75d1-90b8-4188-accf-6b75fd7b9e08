<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:zstack="http://zstack.org/schema/zstack"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
         http://www.springframework.org/schema/beans/spring-beans-3.0.xsd




     	 http://zstack.org/schema/zstack
         http://zstack.org/schema/zstack/plugin.xsd"
       default-init-method="init" default-destroy-method="destroy">

    <bean id="SilentJobService" class="org.zstack.test.multinodes.SilentJobService">
        <zstack:plugin>
            <zstack:extension interface="org.zstack.header.Service"/>
        </zstack:plugin>
    </bean>

</beans>
