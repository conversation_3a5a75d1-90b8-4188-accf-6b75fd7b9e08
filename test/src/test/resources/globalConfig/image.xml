<?xml version="1.0" encoding="UTF-8"?>
<globalConfig xmlns="http://zstack.org/schema/zstack">
	<config>
		<name>deletionPolicy</name>
        <description>the behavior of deleting a image, options are [Direct, Delay, Never]. Direct: delete the image from database and directly delete its root volume; Delay: change the image's state to Destroyed in database; after the period controlled by 'expungePeriod' passes, delete the image from database and delete it's root volume; Never: delete the image from database but never delete its root volume</description>
		<category>image</category>
		<defaultValue>Delay</defaultValue>
	</config>

	<config>
		<name>expungePeriod</name>
        <description>the period before a destroyed image being deleted from database and before its root volume being deleted from backup storage, in seconds</description>
		<category>image</category>
		<defaultValue>259200</defaultValue>
		<type>java.lang.Long</type>
	</config>

	<config>
		<name>deletion.gcInterval</name>
        	<description>the interval the garbage collector to delete stale images</description>
		<category>image</category>
		<defaultValue>3600</defaultValue>
		<type>java.lang.Long</type>
	</config>

	<config>
		<name>expungeInterval</name>
        	<description>the interval the expunging image task runs, in seconds</description>
		<category>image</category>
		<defaultValue>3600</defaultValue>
		<type>java.lang.Long</type>
	</config>

	<config>
		<name>image.num</name>
		<description>default quota for image.num</description>
		<defaultValue>20</defaultValue>
		<category>quota</category>
		<type>java.lang.Long</type>
	</config>

	<config>
		<name>image.size</name>
		<description>default quota for image.size</description>
		<defaultValue>10995116277760</defaultValue>
		<category>quota</category>
		<type>java.lang.Long</type>
	</config>

	<config>
		<name>download.localPath.blacklist</name>
		<description>the black list to filter local download path</description>
		<category>image</category>
		<defaultValue>/**/.*/**;/**/~*/**;/etc/**;/usr/local/zstack/**;/var/log/**;/dev/**;/proc/**;/sys/**</defaultValue>
		<type>java.lang.String</type>
	</config>

	<config>
		<name>download.localPath.whitelist</name>
		<description>the white list to specify local available download paths</description>
		<category>image</category>
		<defaultValue>/home/<USER>/mnt/**;/opt/**;/root/**;</defaultValue>
		<type>java.lang.String</type>
	</config>

	<config>
		<name>download.localPath.customFilter</name>
		<description>default download path list for local image</description>
		<category>image</category>
		<defaultValue>blacklist</defaultValue>
		<type>java.lang.String</type>
	</config>

	<config>
		<name>upload.failure.tolerance.count</name>
		<description>number of errors can be tolerated when uploading images</description>
		<category>image</category>
		<defaultValue>1</defaultValue>
		<type>java.lang.Long</type>
	</config>

	<config>
		<name>upload.max.idle.duration.in.seconds</name>
		<description>the max duration can be tolerated when uploading images, in seconds</description>
		<category>image</category>
		<defaultValue>1</defaultValue>
		<type>java.lang.Long</type>
	</config>
</globalConfig>
