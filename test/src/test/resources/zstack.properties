#DbFacadeDataSource.jdbcUrl=**************************************
DB.url=***************************/
DB.user=root
DB.password=
DB.idleConnectionTestPeriod=500
DB.maxIdleTime=600

DbFacadeDataSource.maxPoolSize=64

RESTFacade.hostname=localhost
RESTFacade.port=8989
RESTFacade.path=

KVMHostFactory.agentPort=8989

Ceph.backupStorageAgent.port=8989
Ceph.primaryStorageAgent.port=8989

Zbs.primaryStorageAgent.port=8989

SftpBackupStorageFactory.deployPuppetModule=false
SftpBackupStorage.agentPort=8989

CdpBackupStorage.agentPort=8989
ImageStoreBackupStorage.agentPort=8989
Zabbix.port=8989

NfsPrimaryStorageKVMBackend.syncGetCapacity=true

VirtualRouterManager.agentPort=8989
VirtualRouterManager.cleanVirtualRouterVmWhenFail=true

ManagementServerConsoleProxyBackend.agentPort=8989

IscsiFileSystemBackendPrimaryStorage.agentPort=8989

BaremetalPxeServer.agentPort=8989

httpConsoleProxyPort = 8989

AppBuildSystem.agentPort=8989

Zdfs.agentPort=8989

ApiMediator.apiWorkerNum=50

unitTestOn=true
exitJVMOnStop=false

#CloudBus.closeTracker=true
#CloudBus.messageTTL=1
#CloudBus.closeTracker=true

#vmTracerOn=false
#Simulator.notCacheAgentCommand=true

ErrorFacade.dumpOnError=false

#ThreadFacade.maxThreadNum=2100

Ansible.cfg.forks=100
Ansible.cfg.host_key_checking=False
Ansible.cfg.pipelining=True

CloudBus.serverIp.0 = localhost

org.jboss.logging.provider=slf4j

exposeSimulatorType=true

Cassandra.contactPoints=127.0.0.1
Cassandra.port=9042
Cassandra.keyspace=zstack_billing
Cassandra.bin=~/apache-cassandra-2.2.3/bin/cassandra
Cassandra.cqlsh=~/apache-cassandra-2.2.3/bin/cqlsh

PrimaryStorage.capacityTrackerOn=true
shadowEntityOn=true
isThreadContextMapInheritable=true
enableElaboration=true
Prometheus.versionMode=2.x
InfluxDB.metadata.version=v2

chain.task.qos=true

identity.init.type=
