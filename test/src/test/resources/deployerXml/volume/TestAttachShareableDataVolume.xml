<?xml version="1.0" encoding="UTF-8"?>
<deployerConfig xmlns="http://zstack.org/schema/zstack">
    <backupStorages>
        <cephBackupStorage name="ceph-bk" description="Test"
                           url="/path1" fsid="7ff218d9-f525-435f-8a40-3618d1772a64"
                           monUrl="root:password@localhost:23,root:pass@#$word@127.0.0.1:23"
                           totalCapacity="1T" availableCapacity="500G"/>
    </backupStorages>

    <images>
        <image name="TestImage" description="Test">
            <backupStorageRef>ceph-bk</backupStorageRef>
        </image>
    </images>

    <diskOffering name="DiskOffering" description="Test"
                  diskSize="30G"/>

    <instanceOfferings>
        <instanceOffering name="TestInstanceOffering"
                          description="Test" memoryCapacity="3G" cpuNum="1" cpuSpeed="3000">
        </instanceOffering>
    </instanceOfferings>

    <vm>
        <userVm name="TestVm" description="Test">
            <imageRef>TestImage</imageRef>
            <instanceOfferingRef>TestInstanceOffering</instanceOfferingRef>
            <l3NetworkRef>TestL3Network1</l3NetworkRef>
            <diskOfferingRef>DiskOffering</diskOfferingRef>
        </userVm>

        <userVm name="TestVm2" description="Test">
            <imageRef>TestImage</imageRef>
            <instanceOfferingRef>TestInstanceOffering</instanceOfferingRef>
            <l3NetworkRef>TestL3Network1</l3NetworkRef>
            <diskOfferingRef>DiskOffering</diskOfferingRef>
        </userVm>
    </vm>

    <zones>
        <zone name="Zone1" description="Test">
            <clusters>
                <cluster name="Cluster1" description="Test" hypervisorType="KVM">
                    <hosts>
                        <kvmHost name="host1" description="Test" managementIp="localhost"
                                 memoryCapacity="8G" cpuNum="4" cpuSpeed="2600"/>
                    </hosts>
                    <primaryStorageRef>ceph-pri</primaryStorageRef>
                    <l2NetworkRef>TestL2Network</l2NetworkRef>
                </cluster>
            </clusters>

            <l2Networks>
                <l2NoVlanNetwork name="TestL2Network" description="Test"
                                 physicalInterface="eth0">
                    <l3Networks>
                        <l3BasicNetwork name="TestL3Network1" description="Test">
                            <ipRange name="TestIpRange" description="Test" startIp="**********"
                                     endIp="***********" gateway="********" netmask="*********"/>
                        </l3BasicNetwork>
                    </l3Networks>
                </l2NoVlanNetwork>
            </l2Networks>

            <backupStorageRef>ceph-bk</backupStorageRef>
            <primaryStorages>
                <cephPrimaryStorage name="ceph-pri" description="Test"
                                    totalCapacity="1T" availableCapacity="500G" url="ceph://test"
                                    fsid="7ff218d9-f525-435f-8a40-3618d1772a64"
                                    monUrl="root:password@localhost/?monPort=7777,root:password@127.0.0.1/?monPort=7777"/>

            </primaryStorages>
        </zone>
    </zones>
</deployerConfig>
