<?xml version="1.0" encoding="UTF-8"?>
<DeployerConfig xmlns="http://zstack.org/schema/zstack">
    <instanceOfferings>
        <instanceOffering name="TestInstanceOffering"
            description="Test" memoryCapacity="3G" cpuNum="1" cpuSpeed="3000" />
    </instanceOffering>

    <diskOffering name="TestRootDiskOffering" description="Test"
        diskSize="50G" />
    <diskOffering name="TestDataDiskOffering" description="Test"
        diskSize="120G" />


    <backupStorages>
        <simulatorBackupStorage name="TestBackupStorage"
            description="Test" totalCapacity="500G" usedCapacity="0G"
            url="nfs://localost/backup" />
    </backupStorages>

    <images>
        <image name="TestImage" description="Test" bits="64"
            url="http://zstack.org/download/test.qcows" mediaType="Template"
            guestOsType="centos63" hypervisorType="Simulator">
            <backupStorageRef>TestBackupStorage</backupStorageRef>
        </image>
    </images>


    <vm>
        <userVm name="TestVm" description="Test" type="UserVm">
            <rootDiskOfferingRef>TestRootDiskOffering</rootDiskOfferingRef>
            <imageRef>TestImage</imageRef>
            <instanceOfferingRef>TestInstanceOffering</instanceOfferingRef>
            <defaultL3NetworkRef>TestL3Network1</defaultL3NetworkRef>
            <l3NetworkRef>TestL3Network1</l3NetworkRef>
            <l3NetworkRef>TestL3Network2</l3NetworkRef>
            <l3NetworkRef>TestL3Network3</l3NetworkRef>
            <diskOfferingRef>TestDataDiskOffering</diskOfferingRef>
        </userVm>
    </vm>

    <zones>
        <zone name="TestZone" description="Test">
            <clusters>
                <cluster name="TestCluster" description="Test"
                    hypervisorType="Simulator">
                    <hosts>
                        <simulatorHost name="TestHost1" description="Test"
                            managementIp="*********" memoryCapacity="8G" cpuNum="4" cpuSpeed="2600" />
                        <simulatorHost name="TestHost2" description="Test"
                            managementIp="*********" memoryCapacity="4G" cpuNum="4" cpuSpeed="2600" />
                    </hosts>
                    <primaryStorageRef>TestPrimaryStorage</primaryStorageRef>
                    <l2NetworkRef>TestL2Network</l2NetworkRef>
                </cluster>
            </clusters>

            <l2Networks>
                <l2NoVlanNetwork name="TestL2Network" description="Test"
                    physicalInterface="eth0">
                    <l3Networks>
                        <l3BasicNetwork name="TestL3Network1" description="Test">
                            <ipRange name="TestIpRange" description="Test" startIp="**********"
                                endIp="***********" gateway="********" netmask="*********" type="Guest" />
                        </l3BasicNetwork>
                        <l3BasicNetwork name="TestL3Network2" description="Test">
                            <ipRange name="TestIpRange" description="Test" startIp="*********00"
                                endIp="***********" gateway="*********" netmask="*********"
                                type="Guest" />
                        </l3BasicNetwork>
                        <l3BasicNetwork name="TestL3Network3" description="Test">
                            <ipRange name="TestIpRange" description="Test" startIp="***********"
                                endIp="***********" gateway="*********" netmask="*********"
                                type="Guest" />
                        </l3BasicNetwork>
                    </l3Networks>
                </l2NoVlanNetwork>
            </l2Networks>

            <primaryStorages>
                <simulatorPrimaryStorage name="TestPrimaryStorage"
                    description="Test" totalCapacity="1T" usedCapacity="0G"
                    url="nfs://localost:/mnt" />
            </primaryStorages>
        </zone>
    </zones>
</DeployerConfig>
