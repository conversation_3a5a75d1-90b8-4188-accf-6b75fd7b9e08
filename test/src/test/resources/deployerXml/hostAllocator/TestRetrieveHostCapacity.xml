<?xml version="1.0" encoding="UTF-8"?>
<deployerConfig xmlns="http://zstack.org/schema/zstack">
    <zones>
        <zone name="TestZone" description="Test">
            <clusters>
                <cluster name="TestCluster" description="Test">
                    <hosts>
                        <simulatorHost name="TestHost1"
                            description="TestDefaultHostAllocationStrategy" managementIp="*********"
                            memoryCapacity="8G" cpuNum="4" cpuSpeed="2600" />
                        <simulatorHost name="TestHost2"
                            description="TestDefaultHostAllocationStrategy" managementIp="*********"
                            memoryCapacity="4G" cpuNum="4" cpuSpeed="2600" />
                    </hosts>
                </cluster>
            </clusters>
        </zone>
    </zones>
</deployerConfig>
