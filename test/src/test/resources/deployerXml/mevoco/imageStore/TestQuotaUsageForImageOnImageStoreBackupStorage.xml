<?xml version="1.0" encoding="UTF-8"?>
<deployerConfig xmlns="http://zstack.org/schema/zstack">
    <account name="test" password="password"/>

    <instanceOfferings>
        <instanceOffering name="TestInstanceOffering" description="Test" memoryCapacity="3G" cpuNum="6" cpuSpeed="3000">
            <accountRef>test</accountRef>
        </instanceOffering>
    </instanceOfferings>

    <backupStorages>
        <imageStoreBackupStorage name="TestImageStoreBackupStorage" hostname="localhost" password="password"
                               url="/path1" description="Test" />
    </backupStorages>

    <diskOffering name="disk50G" description="Test"
                  diskSize="50G">
        <accountRef>test</accountRef>
    </diskOffering>
    <diskOffering name="rootDisk" description="Test"
                  diskSize="10G">
        <accountRef>test</accountRef>
    </diskOffering>

    <images>
        <image name="testLocalImageFile" description="Test"
               url="file://///home/<USER>/Desktop/zstack2/kairosdb-1.1.1-1.tar.gz"
               format="simulator">
            <backupStorageRef>TestImageStoreBackupStorage</backupStorageRef>
            <accountRef>test</accountRef>
        </image>
    </images>


    <zones>
        <zone name="TestZone" description="Test">
            <clusters>
                <cluster name="TestCluster" description="Test">
                    <hosts>
                        <simulatorHost name="TestHost1" description="Test"
                                       managementIp="*********" memoryCapacity="80G" cpuNum="10" cpuSpeed="2600"/>
                        <simulatorHost name="TestHost2" description="Test"
                                       managementIp="*********" memoryCapacity="40G" cpuNum="10" cpuSpeed="2600"/>
                    </hosts>
                    <primaryStorageRef>TestPrimaryStorage</primaryStorageRef>
                    <l2NetworkRef>TestL2Network</l2NetworkRef>
                </cluster>
            </clusters>

            <l2Networks>
                <l2NoVlanNetwork name="TestL2Network" description="Test"
                                 physicalInterface="eth0">
                    <l3Networks>
                        <l3BasicNetwork name="TestL3Network1" description="Test">
                            <ipRange name="TestIpRange1" description="Test" startIp="**********"
                                     endIp="***********" gateway="********" netmask="*********"/>
                            <accountRef>test</accountRef>
                        </l3BasicNetwork>
                        <l3BasicNetwork name="TestL3Network2" description="Test">
                            <ipRange name="TestIpRange2" description="Test" startIp="***********"
                                     endIp="***********" gateway="*********" netmask="*********"/>
                            <accountRef>test</accountRef>
                        </l3BasicNetwork>
                        <l3BasicNetwork name="TestL3Network3" description="Test">
                            <ipRange name="TestIpRange3" description="Test" startIp="***********"
                                     endIp="***********" gateway="*********" netmask="*********"/>
                            <accountRef>test</accountRef>
                        </l3BasicNetwork>
                    </l3Networks>
                </l2NoVlanNetwork>
            </l2Networks>

            <primaryStorages>
                <simulatorPrimaryStorage name="TestPrimaryStorage"
                                         description="Test" totalCapacity="1T" url="nfs://test"/>
            </primaryStorages>

            <backupStorageRef>TestImageStoreBackupStorage</backupStorageRef>

        </zone>
    </zones>
</deployerConfig>
