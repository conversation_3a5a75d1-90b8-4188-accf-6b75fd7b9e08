<?xml version="1.0" encoding="UTF-8"?>
<deployerConfig xmlns="http://zstack.org/schema/zstack">
    <backupStorages>
        <sftpBackupStorage name="sftp" description="Test"
            url="nfs://test" />
    </backupStorages>

    <images>
        <image name="TestImage" description="Test">
            <backupStorageRef>sftp</backupStorageRef>
        </image>
    </images>

    <instanceOfferings>
        <instanceOffering name="TestInstanceOffering"
            description="Test" memoryCapacity="3G" cpuNum="1" cpuSpeed="3000">
        </instanceOffering>
    </instanceOfferings>

    <vm>
        <userVm name="TestVm" description="Test">
            <imageRef>TestImage</imageRef>
            <instanceOfferingRef>TestInstanceOffering</instanceOfferingRef>
            <l3NetworkRef>TestL3Network1</l3NetworkRef>
            <l3NetworkRef>TestL3Network2</l3NetworkRef>
            <l3NetworkRef>TestL3Network3</l3NetworkRef>
            <defaultL3NetworkRef>TestL3Network1</defaultL3NetworkRef>
        </userVm>
    </vm>

    <zones>
        <zone name="Zone1" description="Test">
            <clusters>
                <cluster name="Cluster1" description="Test" hypervisorType="KVM">
                    <hosts>
                        <kvmHost name="host1" description="Test" managementIp="localhost"
                            memoryCapacity="8G" cpuNum="4" cpuSpeed="2600" />
                    </hosts>
                    <primaryStorageRef>nfs</primaryStorageRef>
                    <l2NetworkRef>TestL2Network</l2NetworkRef>
                </cluster>
            </clusters>

            <l2Networks>
                <l2NoVlanNetwork name="TestL2Network" description="Test"
                    physicalInterface="eth0">
                    <l3Networks>
                        <l3BasicNetwork name="TestL3Network1" description="Test">
                            <ipRange name="TestIpRange" description="Test" startIp="**********"
                                endIp="***********" gateway="********" netmask="*********" />
                        </l3BasicNetwork>
                        <l3BasicNetwork name="TestL3Network2" description="Test">
                            <ipRange name="TestIpRange" description="Test" startIp="***********"
                                endIp="***********" gateway="*********" netmask="*********" />
                        </l3BasicNetwork>
                        <l3BasicNetwork name="TestL3Network3" description="Test">
                            <ipRange name="TestIpRange" description="Test" startIp="***********"
                                endIp="***********" gateway="*********" netmask="*********" />
                        </l3BasicNetwork>
                        <l3BasicNetwork name="TestL3Network4" description="Test">
                            <ipRange name="TestIpRange" description="Test" startIp="***********"
                                     endIp="***********" gateway="*********" netmask="*********" />
                        </l3BasicNetwork>
                    </l3Networks>
                </l2NoVlanNetwork>
            </l2Networks>

            <backupStorageRef>sftp</backupStorageRef>
            <primaryStorages>
                <nfsPrimaryStorage name="nfs" description="Test"
                    totalCapacity="1T" url="nfs://test" />
            </primaryStorages>
        </zone>
    </zones>
</deployerConfig>
