<?xml version="1.0" encoding="UTF-8"?>
<deployerConfig xmlns="http://zstack.org/schema/zstack">
    <zones>
        <zone name="TestZone" description="Test">
            <l2Networks>
                <l2NoVlanNetwork name="TestL2Network" description="Test"
                    physicalInterface="eth0">
                    <l3Networks>
                        <l3BasicNetwork name="TestL3Network1" description="Test">
                            <ipRange name="TestIpRange" description="Test" startIp="**********"
                                endIp="***********" gateway="********" netmask="*********" />
                            
                            
							<networkService provider="VirtualRouter">
								<serviceType>DHCP</serviceType>
								<serviceType>DNS</serviceType>
								<serviceType>SNAT</serviceType>
							</networkService>
							
                        </l3BasicNetwork>
                        <l3BasicNetwork name="TestL3Network2" description="Test">
                            <ipRange name="TestIpRange" description="Test" startIp="***********"
                                endIp="***********" gateway="*********" netmask="*********" />
                            <dns>*******</dns>
                        </l3BasicNetwork>
                        <l3BasicNetwork name="TestL3Network3" description="Test">
                            <ipRange name="TestIpRange" description="Test" startIp="***********"
                                endIp="***********" gateway="*********" netmask="*********" />
                                
                            <dns>*******</dns>
                        </l3BasicNetwork>
                    </l3Networks>
                </l2NoVlanNetwork>
            </l2Networks>
        </zone>
    </zones>
</deployerConfig>
