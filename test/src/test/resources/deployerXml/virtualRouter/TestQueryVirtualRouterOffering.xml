<?xml version="1.0" encoding="UTF-8"?>
<deployerConfig xmlns="http://zstack.org/schema/zstack">
	<account name="test" password="password"></account>
	<backupStorages>
		<sftpBackupStorage name="sftp" description="Test"
			url="nfs://test" />
	</backupStorages>
	
	<images>
        <image name="TestImage" description="Test">
			<backupStorageRef>sftp</backupStorageRef>
			<accountRef>test</accountRef>
		</image>
	</images>

	<zones>
		<zone name="Zone1" description="Test">
			<l2Networks>
				<l2NoVlanNetwork name="TestL2Network" description="Test"
					physicalInterface="eth0">
					<l3Networks>
						<l3BasicNetwork name="TestL3Network1" description="Test">
							<ipRange name="TestIpRange" description="Test" startIp="**********"
								endIp="***********" gateway="********" netmask="*********" />
							<accountRef>test</accountRef>
						</l3BasicNetwork>
						<l3BasicNetwork name="TestL3Network2" description="Test">
							<ipRange name="TestIpRange" description="Test" startIp="***********"
								endIp="***********" gateway="*********" netmask="*********" />
							<accountRef>test</accountRef>
						</l3BasicNetwork>
					</l3Networks>
				</l2NoVlanNetwork>
			</l2Networks>
		</zone>
	</zones>
</deployerConfig>
