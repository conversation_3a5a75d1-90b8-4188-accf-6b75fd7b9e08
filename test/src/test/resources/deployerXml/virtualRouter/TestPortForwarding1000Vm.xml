<?xml version="1.0" encoding="UTF-8"?>
<deployerConfig xmlns="http://zstack.org/schema/zstack">
    <instanceOfferings>
        <virtualRouterOffering name="virtualRouterOffering"
                               isDefault="true" memoryCapacity="1" cpuNum="1" cpuSpeed="1">
            <zoneRef>Zone1</zoneRef>
            <managementL3NetworkRef>MgmtNetwork</managementL3NetworkRef>
            <publicL3NetworkRef>MgmtNetwork</publicL3NetworkRef>
            <imageRef>TestImage</imageRef>
        </virtualRouterOffering>

        <instanceOffering name="TestInstanceOffering"
                          description="Test" memoryCapacity="1" cpuNum="1" cpuSpeed="1" />
    </instanceOfferings>

    <diskOffering name="DataDiskOffering" description="Test"
                  diskSize="1" />

    <backupStorages>
        <sftpBackupStorage name="sftp" description="Test"
                           url="nfs://test" />
    </backupStorages>

    <images>
        <image name="TestImage" description="Test">
            <backupStorageRef>sftp</backupStorageRef>
        </image>
    </images>

    <zones>
        <zone name="Zone1" description="Test">
            <clusters>
                <cluster name="Cluster1" description="Test" hypervisorType="KVM">
                    <hosts>
                        <kvmHost name="host1" description="Test" managementIp="localhost"
                                 memoryCapacity="10000G" cpuNum="32000" cpuSpeed="2600" />
                    </hosts>

                    <primaryStorageRef>nfs1</primaryStorageRef>
                    <primaryStorageRef>nfs2</primaryStorageRef>
                    <primaryStorageRef>nfs3</primaryStorageRef>
                    <primaryStorageRef>nfs4</primaryStorageRef>
                    <l2NetworkRef>TestL2Network</l2NetworkRef>
                    <l2NetworkRef>ManagementL2Network</l2NetworkRef>
                </cluster>
            </clusters>

            <l2Networks>
                <l2NoVlanNetwork name="ManagementL2Network" description="Test"
                                 physicalInterface="eth1">
                    <l3Networks>
                        <l3BasicNetwork name="MgmtNetwork" description="Test">
                            <ipRange name="MgmtIpRange" description="Test" startIp="***********"
                                     endIp="*************" gateway="*********" netmask="*********" />
                        </l3BasicNetwork>
                    </l3Networks>
                </l2NoVlanNetwork>

                <l2NoVlanNetwork name="TestL2Network" description="Test"
                                 physicalInterface="eth0">
                    <l3Networks>
                        <l3BasicNetwork name="TestL3Network1" description="Test">
                            <ipRange name="TestIpRange" description="Test" startIp="**********"
                                     endIp="***********" gateway="********" netmask="*********" />

                            <networkService provider="VirtualRouter">
                                <serviceType>PortForwarding</serviceType>
                            </networkService>

                        </l3BasicNetwork>
                    </l3Networks>
                </l2NoVlanNetwork>
            </l2Networks>

            <backupStorageRef>sftp</backupStorageRef>
            <primaryStorages>
                <nfsPrimaryStorage name="nfs1" description="Test"
                                   totalCapacity="5000T" url="nfs://test1" />
                <nfsPrimaryStorage name="nfs2" description="Test"
                                   totalCapacity="5000T" url="nfs://test2" />
                <nfsPrimaryStorage name="nfs3" description="Test"
                                   totalCapacity="5000T" url="nfs://test3" />
                <nfsPrimaryStorage name="nfs4" description="Test"
                                   totalCapacity="5000T" url="nfs://test4" />
            </primaryStorages>
        </zone>
    </zones>
</deployerConfig>
