<?xml version="1.0" encoding="UTF-8"?>
<deployerConfig xmlns="http://zstack.org/schema/zstack">
	<instanceOfferings>
		<virtualRouterOffering name="virtualRouterOffering"
			isDefault="true">
			<zoneRef>Zone1</zoneRef>
			<managementL3NetworkRef>PublicNetwork</managementL3NetworkRef>
			<publicL3NetworkRef>PublicNetwork</publicL3NetworkRef>
			<imageRef>TestImage</imageRef>
		</virtualRouterOffering>
		
		<instanceOffering name="TestInstanceOffering"
			description="Test" memoryCapacity="3G" cpuNum="1" cpuSpeed="3000" />
	</instanceOfferings>

	<backupStorages>
		<sftpBackupStorage name="sftp" description="Test"
			url="nfs://test" />
	</backupStorages>

	<images>
        <image name="TestImage" description="Test">
			<backupStorageRef>sftp</backupStorageRef>
		</image>
	</images>
	
    <vm>
        <userVm name="TestVm" description="Test">
            <imageRef>TestImage</imageRef>
            <instanceOfferingRef>TestInstanceOffering</instanceOfferingRef>
            <l3NetworkRef>GuestNetwork</l3NetworkRef>
        </userVm>
    </vm>
    
	<zones>
		<zone name="Zone1" description="Test">
			<clusters>
				<cluster name="Cluster1" description="Test" hypervisorType="KVM">
					<hosts>
						<kvmHost name="host1" description="Test" managementIp="localhost"
							memoryCapacity="64G" cpuNum="32" cpuSpeed="2600" />
					</hosts>
					<primaryStorageRef>nfs</primaryStorageRef>
					<l2NetworkRef>TestL2Network</l2NetworkRef>
				</cluster>
			</clusters>
			
			<l2Networks>
				<l2NoVlanNetwork name="TestL2Network" description="Test"
					physicalInterface="eth0">
					<l3Networks>
						<l3BasicNetwork name="PublicNetwork" description="Test">
							<ipRange name="TestIpRange" description="Test" startIp="************"
								endIp="************0" gateway="***********" netmask="*************" />
						</l3BasicNetwork>
						
						<l3BasicNetwork name="GuestNetwork" description="Test">
							<ipRange name="TestIpRange" description="Test" startIp="***********"
								endIp="***********" gateway="*********" netmask="*********" />

							<networkService provider="VirtualRouter">
								<serviceType>PortForwarding</serviceType>
                                <serviceType>SNAT</serviceType>
							</networkService>
						</l3BasicNetwork>
						
					</l3Networks>
				</l2NoVlanNetwork>
			</l2Networks>

			<backupStorageRef>sftp</backupStorageRef>
			<primaryStorages>
				<nfsPrimaryStorage name="nfs" description="Test"
					totalCapacity="5T" url="nfs://test" />
			</primaryStorages>
		</zone>
	</zones>
</deployerConfig>
