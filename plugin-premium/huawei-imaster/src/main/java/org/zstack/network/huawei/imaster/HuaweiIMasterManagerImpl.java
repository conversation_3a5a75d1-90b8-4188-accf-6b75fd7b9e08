package org.zstack.network.huawei.imaster;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.compute.allocator.AttachedL2NetworkAllocatorExtensionPoint;
import org.zstack.core.Platform;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.cloudbus.EventCallback;
import org.zstack.core.cloudbus.EventFacade;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.core.db.Q;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.header.AbstractService;
import org.zstack.header.core.Completion;
import org.zstack.header.core.FutureCompletion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.core.workflow.FlowChain;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.host.HostVO;
import org.zstack.header.host.HostVO_;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.header.network.NetworkException;
import org.zstack.header.network.l2.*;
import org.zstack.sdnController.header.HardwareL2VxlanNetworkPoolVO;
import org.zstack.sdnController.header.HardwareL2VxlanNetworkPoolVO_;
import org.zstack.header.network.sdncontroller.SdnControllerVO;
import org.zstack.network.hostNetworkInterface.HostNetworkBondingVO;
import org.zstack.network.hostNetworkInterface.HostNetworkInterfaceVO;
import org.zstack.network.hostNetworkInterface.HostNetworkInterfaceCanonicalEvents;
import org.zstack.network.l2.vxlan.vxlanNetwork.VxlanNetworkConstant;
import org.zstack.network.l2.vxlan.vxlanNetworkPool.VniRangeVO;
import org.zstack.sdnController.header.*;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * Huawei iMaster Controller Manager Implementation
 */
public class HuaweiIMasterManagerImpl extends AbstractService implements
        HuaweiIMasterManager, L2NetworkCreateExtensionPoint, AttachedL2NetworkAllocatorExtensionPoint {
    
    private static final CLogger logger = Utils.getLogger(HuaweiIMasterManagerImpl.class);

    @Autowired
    private PluginRegistry pluginRgty;
    @Autowired
    private CloudBus bus;
    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private HuaweiIMasterSdnControllerFactory factory;
    @Autowired
    private EventFacade evtf;

    private HuaweiIMasterHelper helper = new HuaweiIMasterHelper();

    private List<String> huaweiIMasterControllerSyncFlows;

    private FlowChainBuilder huaweiIMasterSyncFlowsBuilder;

    public List<String> getHuaweiIMasterControllerSyncFlows() {
        return huaweiIMasterControllerSyncFlows;
    }

    public void setHuaweiIMasterControllerSyncFlows(List<String> huaweiIMasterControllerSyncFlows) {
        this.huaweiIMasterControllerSyncFlows = huaweiIMasterControllerSyncFlows;
    }

    protected void buildWorkFlowBuilder() {
        huaweiIMasterSyncFlowsBuilder = FlowChainBuilder.newBuilder().setFlowClassNames(huaweiIMasterControllerSyncFlows).construct();
    }

    @Override
    public FlowChain getSyncChain() {
        FlowChain c = huaweiIMasterSyncFlowsBuilder.build();
        return c;
    }

    @Override
    public boolean start() {
        logger.info("Starting Huawei iMaster Controller Manager");
        buildWorkFlowBuilder();
        addEvent();
        return true;
    }

    @Override
    public boolean stop() {
        logger.info("Stopping Huawei iMaster Controller Manager");
        return true;
    }

    @Override
    public void handleMessage(Message msg) {
        if (msg instanceof SdnControllerMessage) {
            handleHuaweiIMasterMsg((SdnControllerMessage)msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    private void handleHuaweiIMasterMsg(SdnControllerMessage msg) {
        HuaweiIMasterSdnControllerVO vo = dbf.findByUuid(msg.getSdnControllerUuid(), HuaweiIMasterSdnControllerVO.class);
        if (vo == null) {
            bus.dealWithUnknownMessage((APIMessage)msg);
            return;
        }

        HuaweiIMasterSdnController controller = new HuaweiIMasterSdnController(vo);
        controller.handleMessage(msg);
    }

    @Override
    public String getId() {
        return bus.makeLocalServiceId(HuaweiIMasterConstant.SERVICE_ID);
    }

    @Override
    public void beforeCreateL2Network(APICreateL2NetworkMsg msg) throws NetworkException {

    }

    @Override
    public void postCreateL2Network(L2NetworkInventory l2Network, APICreateL2NetworkMsg msg, Completion completion) {
        L2NetworkCreateExtensionPoint.super.postCreateL2Network(l2Network, msg, completion);
    }

    @Override
    public void afterCreateL2Network(L2NetworkInventory l2Network) {
        HardwareL2VxlanNetworkPoolVO vxlanNetworkPoolVO = dbf.findByUuid(l2Network.getUuid(), HardwareL2VxlanNetworkPoolVO.class);
        if (vxlanNetworkPoolVO == null) {
            return;
        }

        SdnControllerVO sdnControllerVO = dbf.findByUuid(vxlanNetworkPoolVO.getSdnControllerUuid(), SdnControllerVO.class);
        if (!sdnControllerVO.getVendorType().equals(HuaweiIMasterConstant.HUAWEI_IMASTER_CONTROLLER_TYPE)) {
            return;
        }

        // create a default vni range
        VniRangeVO vo = new VniRangeVO();
        vo.setUuid(Platform.getUuid());
        vo.setName(l2Network.getName());
        vo.setDescription(l2Network.getDescription());
        vo.setStartVni(VxlanNetworkConstant.MIN_VNI);
        vo.setEndVni(VxlanNetworkConstant.MAX_VNI);
        vo.setL2NetworkUuid(l2Network.getUuid());
        vo = dbf.persistAndRefresh(vo);
        logger.debug(String.format("create default vni range [%s] for vxlan pool", JSONObjectUtil.toJsonString(vo)));
    }

    @Override
    public List<String> filter(List<String> hostUuids, List<String> l2Uuids) {
        if (hostUuids.isEmpty()) {
            return hostUuids;
        }

        if (l2Uuids.isEmpty()) {
            return hostUuids;
        }

        for (String uuid : l2Uuids) {
            HardwareL2VxlanNetworkVO l2Vo = Q.New(HardwareL2VxlanNetworkVO.class).eq(L2NetworkVO_.uuid, uuid).find();
            if (l2Vo == null) {
                continue;
            }

            Map<String, List<HuaweiIMasterNceFabricCommands.HuaweiLogicalSwitchPortLocationStruct>> locations =
                    helper.getPhysicalSwitchPortForHosts(HardwareL2VxlanNetworkInventory.valueOf(l2Vo), hostUuids, true);
            hostUuids.removeIf(huuid -> !locations.containsKey(huuid));
        }

        return hostUuids;
    }

    private void addEvent() {
        evtf.on(HostNetworkInterfaceCanonicalEvents.PEER_PORT_CHANGED, new EventCallback() {
            @Override
            protected void run(Map tokens, Object data) {
                HostNetworkInterfaceCanonicalEvents.PeerPortChangedData data1
                        = (HostNetworkInterfaceCanonicalEvents.PeerPortChangedData) data;
                HostNetworkInterfaceVO hostNetworkInterfaceVO = dbf.findByUuid(data1.getInterfaceUuid(), HostNetworkInterfaceVO.class);

                logger.debug(String.format("interface[hostUuid:%s, name:%s] peer physical switch port changed from [%s] to [%s]",
                        hostNetworkInterfaceVO.getHostUuid(), hostNetworkInterfaceVO.getInterfaceName(),
                        JSONObjectUtil.toJsonString(data1.getOldPhysicalSwitchPort()),
                        JSONObjectUtil.toJsonString(data1.getNewPhysiaclSwitchPort())));

                String interfaceName = hostNetworkInterfaceVO.getInterfaceName();
                if (hostNetworkInterfaceVO.getBondingUuid() != null) {
                    HostNetworkBondingVO bondingVO = dbf.findByUuid(hostNetworkInterfaceVO.getBondingUuid(),
                            HostNetworkBondingVO.class);
                    interfaceName = bondingVO.getBondingName();
                }
                List<HardwareL2VxlanNetworkPoolVO> vxlanPools = Q.New(HardwareL2VxlanNetworkPoolVO.class)
                        .eq(HardwareL2VxlanNetworkPoolVO_.physicalInterface, interfaceName).list();
                if (vxlanPools.isEmpty()) {
                    return;
                }

                String hostClusterUUid = Q.New(HostVO.class).eq(HostVO_.uuid, hostNetworkInterfaceVO.getHostUuid())
                        .select(HostVO_.clusterUuid).findValue();
                List<HardwareL2VxlanNetworkPoolVO> attachedPools = new ArrayList<>();
                for (HardwareL2VxlanNetworkPoolVO pool : vxlanPools) {
                    if (pool.getAttachedClusterRefs().stream().anyMatch(ref -> ref.getClusterUuid().equals(hostClusterUUid))) {
                        attachedPools.add(pool);
                    }
                }

                if (attachedPools.isEmpty()) {
                    return;
                }

                Map<String, List<HardwareL2VxlanNetworkPoolVO>> sdnL2PoolMap = new HashMap<>();
                for (HardwareL2VxlanNetworkPoolVO pool : attachedPools) {
                    List<HardwareL2VxlanNetworkPoolVO> poolList = sdnL2PoolMap.computeIfAbsent(pool.getSdnControllerUuid(),
                            k -> new ArrayList<>());
                    poolList.add(pool);
                }

                List<HuaweiIMasterUpdateLogicalSwitchPortMsg> msgs = new ArrayList<>();
                for (Map.Entry<String, List<HardwareL2VxlanNetworkPoolVO>> entry : sdnL2PoolMap.entrySet()) {
                    for (HardwareL2VxlanNetworkPoolVO pool : entry.getValue()) {
                        HuaweiIMasterUpdateLogicalSwitchPortMsg msg = new HuaweiIMasterUpdateLogicalSwitchPortMsg();
                        msg.setSdnControllerUuid(entry.getKey());
                        msg.setHostUuid(hostNetworkInterfaceVO.getHostUuid());
                        msg.setHardwareVxlanPoolUuid(pool.getUuid());
                        msg.setOldPort(data1.getOldPhysicalSwitchPort());
                        msg.setNewPort(data1.getNewPhysiaclSwitchPort());
                        bus.makeTargetServiceIdByResourceUuid(msg, SdnControllerConstant.SERVICE_ID, entry.getKey());
                        msgs.add(msg);
                    }
                }

                FutureCompletion completion = new FutureCompletion(null);
                new While<>(msgs).each((msg, comp) -> {
                    bus.send(msg, new CloudBusCallBack(comp) {
                        @Override
                        public void run(MessageReply reply) {
                            if (!reply.isSuccess()) {
                                logger.warn(String.format("failed to update logical switch port for host[uuid:%s] due to: %s",
                                        hostNetworkInterfaceVO.getHostUuid(), reply.getError()));
                                comp.addError(reply.getError());

                            }
                            comp.done();
                        }
                    });
                }).run(new WhileDoneCompletion(completion) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        if (errorCodeList.getCauses().isEmpty()) {
                            completion.success();
                        } else {
                            completion.fail(errorCodeList.getCauses().get(0));
                        }
                    }
                });

                completion.await(TimeUnit.MINUTES.toMillis(5));

                if (!completion.isSuccess()) {
                    logger.debug(String.format("update huawei sdn logical switch failed, %s",
                            completion.getErrorCode().getDetails()));
                }
            }
        });
    }
}
