package org.zstack.network.huawei.imaster


import org.zstack.header.network.sdncontroller.SdnControllerStatus
import org.zstack.sdnController.header.SdnVniRange
import org.zstack.sdnController.header.HardwareL2VxlanNetworkPoolInventory
import org.zstack.header.network.sdncontroller.SdnControllerHostRefInventory

doc {

	title "在这里输入结构的名称"

	ref {
		name "fabrics"
		path "org.zstack.network.huawei.imaster.HuaweiIMasterSdnControllerInventory.fabrics"
		desc "null"
		type "List"
		since "5.3.28"
		clz HuaweiIMasterFabricInventory.class
	}
	ref {
		name "tenants"
		path "org.zstack.network.huawei.imaster.HuaweiIMasterSdnControllerInventory.tenants"
		desc "null"
		type "List"
		since "5.3.28"
		clz HuaweiIMasterTenantInventory.class
	}
	ref {
		name "vpcs"
		path "org.zstack.network.huawei.imaster.HuaweiIMasterSdnControllerInventory.vpcs"
		desc "null"
		type "List"
		since "5.3.28"
		clz HuaweiIMasterVpcInventory.class
	}
	ref {
		name "vrouters"
		path "org.zstack.network.huawei.imaster.HuaweiIMasterSdnControllerInventory.vrouters"
		desc "null"
		type "List"
		since "5.3.28"
		clz HuaweiIMasterVRouterInventory.class
	}
	field {
		name "uuid"
		desc "资源的UUID，唯一标示该资源"
		type "String"
		since "5.3.28"
	}
	field {
		name "vendorType"
		desc ""
		type "String"
		since "5.3.28"
	}
	field {
		name "name"
		desc "资源名称"
		type "String"
		since "5.3.28"
	}
	field {
		name "description"
		desc "资源的详细描述"
		type "String"
		since "5.3.28"
	}
	field {
		name "ip"
		desc ""
		type "String"
		since "5.3.28"
	}
	field {
		name "username"
		desc ""
		type "String"
		since "5.3.28"
	}
	field {
		name "password"
		desc ""
		type "String"
		since "5.3.28"
	}
	ref {
		name "status"
		path "org.zstack.network.huawei.imaster.HuaweiIMasterSdnControllerInventory.status"
		desc "null"
		type "SdnControllerStatus"
		since "5.3.28"
		clz SdnControllerStatus.class
	}
	field {
		name "createDate"
		desc "创建时间"
		type "Timestamp"
		since "5.3.28"
	}
	field {
		name "lastOpDate"
		desc "最后一次修改时间"
		type "Timestamp"
		since "5.3.28"
	}
	ref {
		name "vniRanges"
		path "org.zstack.network.huawei.imaster.HuaweiIMasterSdnControllerInventory.vniRanges"
		desc "null"
		type "List"
		since "5.3.28"
		clz SdnVniRange.class
	}
	ref {
		name "vxlanPools"
		path "org.zstack.network.huawei.imaster.HuaweiIMasterSdnControllerInventory.vxlanPools"
		desc "null"
		type "List"
		since "5.3.28"
		clz HardwareL2VxlanNetworkPoolInventory.class
	}
	ref {
		name "hostRefs"
		path "org.zstack.network.huawei.imaster.HuaweiIMasterSdnControllerInventory.hostRefs"
		desc "null"
		type "List"
		since "5.3.28"
		clz SdnControllerHostRefInventory.class
	}
}
