package org.zstack.network.huawei.imaster;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APICreateMessage;
import org.zstack.header.message.APIEvent;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.other.APIAuditor;
import org.zstack.header.rest.APINoSee;
import org.zstack.header.rest.RestRequest;
import org.zstack.header.tag.TagResourceType;
import org.zstack.sdnController.header.SdnControllerMessage;

/**
 * API message for creating Huawei iMaster VRouter
 */
@TagResourceType(HuaweiIMasterVRouterVO.class)
@Action(category = HuaweiIMasterConstant.ACTION_CATEGORY)
@RestRequest(
        path = "/sdn-controller/huawei-imaster/vrouters",
        method = HttpMethod.POST,
        responseClass = APICreateHuaweiIMasterVRouterEvent.class,
        parameterName = "params"
)
public class APICreateHuaweiIMasterVRouterMsg extends APICreateMessage implements APIAuditor, SdnControllerMessage {

    @APINoSee
    private String sdnControllerUuid;

    /**
     * VRouter name - required parameter, maximum length 255
     */
    @APIParam(maxLength = 255)
    private String name;

    /**
     * VRouter description - optional parameter, maximum length 2048
     */
    @APIParam(maxLength = 2048, required = false)
    private String description;

    /**
     * Logical Network UUID - optional parameter, references HuaweiIMasterLogicalNetworkVO
     */
    @APIParam(resourceType = HuaweiIMasterVpcVO.class)
    private String huaweiVpcUuid;

    // Getters and Setters

    public String getSdnControllerUuid() {
        return sdnControllerUuid;
    }

    public void setSdnControllerUuid(String sdnControllerUuid) {
        this.sdnControllerUuid = sdnControllerUuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getHuaweiVpcUuid() {
        return huaweiVpcUuid;
    }

    public void setHuaweiVpcUuid(String huaweiVpcUuid) {
        this.huaweiVpcUuid = huaweiVpcUuid;
    }

    @Override
    public Result audit(APIMessage msg, APIEvent rsp) {
        return new Result(rsp.isSuccess() ? ((APICreateHuaweiIMasterVRouterEvent) rsp).getInventory().getUuid() : "",
                         HuaweiIMasterVRouterVO.class);
    }

    public static APICreateHuaweiIMasterVRouterMsg __example__() {
        APICreateHuaweiIMasterVRouterMsg msg = new APICreateHuaweiIMasterVRouterMsg();
        msg.setSdnControllerUuid(uuid());
        msg.setName("test-vrouter");
        msg.setDescription("Test VRouter for Huawei iMaster");
        msg.setHuaweiVpcUuid(uuid());
        return msg;
    }
}
