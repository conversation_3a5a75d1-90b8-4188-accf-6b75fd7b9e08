package org.zstack.network.huawei.imaster;

import org.zstack.header.identity.OwnedByAccount;
import org.zstack.header.tag.AutoDeleteTag;
import org.zstack.header.vo.ForeignKey;
import org.zstack.header.vo.ResourceVO;
import org.zstack.header.vo.ToInventory;
import org.zstack.header.network.sdncontroller.SdnControllerVO;
import org.zstack.sdnController.header.SdnControllerTableState;

import javax.persistence.*;
import java.sql.Timestamp;

/**
 * Huawei iMaster Logical Network Value Object
 */
@Entity
@Table
@AutoDeleteTag
public class HuaweiIMasterVpcVO extends ResourceVO implements ToInventory, OwnedByAccount {
    
    @Column
    private String name;
    
    @Column
    private String description;
    
    @Column
    @ForeignKey(parentEntityClass = HuaweiIMasterTenantVO.class, parentKey = "uuid", onDeleteAction = ForeignKey.ReferenceOption.CASCADE)
    private String tenantId;
    
    @Column
    private String fabricId;

    @Column
    @ForeignKey(parentEntityClass = SdnControllerVO.class, parentKey = "uuid", onDeleteAction = ForeignKey.ReferenceOption.CASCADE)
    private String sdnControllerUuid;

    @Column
    @Enumerated(EnumType.STRING)
    private SdnControllerTableState state;

    @Column
    private Timestamp createDate;

    @Column
    private Timestamp lastOpDate;
    
    @Transient
    private String accountUuid;
    
    @PreUpdate
    private void preUpdate() {
        lastOpDate = null;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getFabricId() {
        return fabricId;
    }

    public void setFabricId(String fabricId) {
        this.fabricId = fabricId;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }

    public String getSdnControllerUuid() {
        return sdnControllerUuid;
    }

    public void setSdnControllerUuid(String sdnControllerUuid) {
        this.sdnControllerUuid = sdnControllerUuid;
    }

    public SdnControllerTableState getState() {
        return state;
    }

    public void setState(SdnControllerTableState state) {
        this.state = state;
    }

    @Override
    public String getAccountUuid() {
        return accountUuid;
    }

    @Override
    public void setAccountUuid(String accountUuid) {
        this.accountUuid = accountUuid;
    }
}
