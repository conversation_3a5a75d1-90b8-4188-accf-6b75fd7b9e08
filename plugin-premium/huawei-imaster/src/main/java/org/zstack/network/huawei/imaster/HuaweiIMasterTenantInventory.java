package org.zstack.network.huawei.imaster;

import org.zstack.header.configuration.PythonClassInventory;
import org.zstack.header.search.Inventory;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Huawei iMaster Tenant Inventory
 */
@Inventory(mappingVOClass = HuaweiIMasterTenantVO.class)
@PythonClassInventory
public class HuaweiIMasterTenantInventory implements Serializable {
    
    private String uuid;
    private String name;
    private String description;
    private List<String> fabricIds;
    private String sdnControllerUuid;
    private String state;
    private Timestamp createDate;
    private Timestamp lastOpDate;

    public static HuaweiIMasterTenantInventory valueOf(HuaweiIMasterTenantVO vo) {
        HuaweiIMasterTenantInventory inv = new HuaweiIMasterTenantInventory();
        inv.setUuid(vo.getUuid());
        inv.setName(vo.getName());
        inv.setDescription(vo.getDescription());
        inv.setFabricIds(vo.getFabricIds().stream()
                .map(HuaweiIMasterTenantFabricRefVO::getFabricUuid).collect(Collectors.toList()));
        inv.setSdnControllerUuid(vo.getSdnControllerUuid());
        inv.setState(vo.getState().toString());
        inv.setCreateDate(vo.getCreateDate());
        inv.setLastOpDate(vo.getLastOpDate());
        return inv;
    }

    public static List<HuaweiIMasterTenantInventory> valueOf(Collection<HuaweiIMasterTenantVO> vos) {
        List<HuaweiIMasterTenantInventory> invs = new ArrayList<>();
        for (HuaweiIMasterTenantVO vo : vos) {
            invs.add(valueOf(vo));
        }
        return invs;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }

    public List<String> getFabricIds() {
        return fabricIds;
    }

    public void setFabricIds(List<String> fabricIds) {
        this.fabricIds = fabricIds;
    }

    public String getSdnControllerUuid() {
        return sdnControllerUuid;
    }

    public void setSdnControllerUuid(String sdnControllerUuid) {
        this.sdnControllerUuid = sdnControllerUuid;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }
}
