package org.zstack.network.huawei.imaster;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cascade.AbstractAsyncCascadeExtension;
import org.zstack.core.cascade.CascadeAction;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.header.core.Completion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.message.MessageReply;
import org.zstack.header.network.l2.L2NetworkVO;
import org.zstack.header.network.sdncontroller.SdnControllerInventory;
import org.zstack.header.network.sdncontroller.SdnControllerVO;
import org.zstack.utils.CollectionUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.function.Function;
import org.zstack.utils.logging.CLogger;

import java.util.Arrays;
import java.util.List;

/**
 * Cascade extension for Huawei iMaster Fabric
 */
public class HuaweiIMasterFabricCascadeExtension extends AbstractAsyncCascadeExtension {
    private static final CLogger logger = Utils.getLogger(HuaweiIMasterFabricCascadeExtension.class);
    private static final String NAME = HuaweiIMasterFabricVO.class.getSimpleName();

    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private CloudBus bus;

    @Override
    public void asyncCascade(CascadeAction action, Completion completion) {
        if (action.isActionCode(CascadeConstant.DELETION_CHECK_CODE)) {
            handleDeletionCheck(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_DELETE_CODE, CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
            handleDeletion(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_CLEANUP_CODE)) {
            handleDeletionCleanup(action, completion);
        } else {
            completion.success();
        }
    }

    private void handleDeletionCleanup(CascadeAction action, Completion completion) {
        dbf.eoCleanup(HuaweiIMasterFabricVO.class);
        completion.success();
    }

    private void handleDeletion(CascadeAction action, final Completion completion) {
        final List<HuaweiIMasterFabricInventory> fabricInvs = fabricFromAction(action);
        if (fabricInvs == null || fabricInvs.isEmpty()) {
            completion.success();
            return;
        }

        List<HuaweiIMasterFabricDeletionMsg> msgs = CollectionUtils.transformToList(fabricInvs,
                new Function<HuaweiIMasterFabricDeletionMsg, HuaweiIMasterFabricInventory>() {
            @Override
            public HuaweiIMasterFabricDeletionMsg call(HuaweiIMasterFabricInventory arg) {
                HuaweiIMasterFabricDeletionMsg msg = new HuaweiIMasterFabricDeletionMsg();
                msg.setFabricUuid(arg.getUuid());
                msg.setSdnControllerUuid(arg.getSdnControllerUuid());
                bus.makeTargetServiceIdByResourceUuid(msg, HuaweiIMasterConstant.SERVICE_ID, arg.getUuid());
                return msg;
            }
        });

        new While<>(msgs).each((msg, compl) -> {
            bus.send(msg, new CloudBusCallBack(compl) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        logger.warn(String.format("failed to delete Huawei iMaster fabric[uuid:%s], %s",
                                msg.getFabricUuid(), reply.getError()));
                    }
                    compl.done();
                }
            });
        }).run(new WhileDoneCompletion(completion) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                completion.success();
            }
        });
    }

    private void handleDeletionCheck(CascadeAction action, Completion completion) {
        completion.success();
    }

    @Override
    public List<String> getEdgeNames() {
        return Arrays.asList(
                SdnControllerVO.class.getSimpleName(),
                L2NetworkVO.class.getSimpleName()
        );
    }

    @Override
    public String getCascadeResourceName() {
        return NAME;
    }

    @Override
    public CascadeAction createActionForChildResource(CascadeAction action) {
        if (CascadeConstant.DELETION_CODES.contains(action.getActionCode())) {
            List<HuaweiIMasterFabricInventory> fabrics = fabricFromAction(action);
            if (fabrics != null) {
                return action.copy().setParentIssuer(NAME).setParentIssuerContext(fabrics);
            }
        }
        return null;
    }

    private List<HuaweiIMasterFabricInventory> fabricFromAction(CascadeAction action) {
        if (SdnControllerVO.class.getSimpleName().equals(action.getParentIssuer())) {
            List<String> controllerUuids = CollectionUtils.transformToList((List<SdnControllerInventory>) action.getParentIssuerContext(), new Function<String, SdnControllerInventory>() {
                @Override
                public String call(SdnControllerInventory arg) {
                    return arg.getUuid();
                }
            });
            if (controllerUuids.isEmpty()) {
                return null;
            }

            List<HuaweiIMasterFabricVO> fabricVOs = Q.New(HuaweiIMasterFabricVO.class).in(HuaweiIMasterFabricVO_.sdnControllerUuid, controllerUuids).list();
            return HuaweiIMasterFabricInventory.valueOf(fabricVOs);
        } else if (HuaweiIMasterFabricVO.class.getSimpleName().equals(action.getParentIssuer())) {
            return action.getParentIssuerContext();
        }
        return null;
    }
}
