package org.zstack.network.huawei.imaster;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import java.sql.Timestamp;

@StaticMetamodel(HuaweiIMasterTenantFabricRefVO.class)
public class HuaweiIMasterTenantFabricRefVO_ {
    public static volatile SingularAttribute<HuaweiIMasterTenantFabricRefVO, Long> id;
    public static volatile SingularAttribute<HuaweiIMasterTenantFabricRefVO, String> tenantUuid;
    public static volatile SingularAttribute<HuaweiIMasterTenantFabricRefVO, String> fabricUuid;
    public static volatile SingularAttribute<HuaweiIMasterTenantFabricRefVO, Timestamp> createDate;
    public static volatile SingularAttribute<HuaweiIMasterTenantFabricRefVO, Timestamp> lastOpDate;
}
