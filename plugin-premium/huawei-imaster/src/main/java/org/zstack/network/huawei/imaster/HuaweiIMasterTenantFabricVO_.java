package org.zstack.network.huawei.imaster;

import javax.persistence.metamodel.SingularAttribute;
import javax.persistence.metamodel.StaticMetamodel;
import java.sql.Timestamp;

@StaticMetamodel(HuaweiIMasterTenantFabricVO.class)
public class HuaweiIMasterTenantFabricVO_ {
    public static volatile SingularAttribute<HuaweiIMasterTenantFabricVO, Long> id;
    public static volatile SingularAttribute<HuaweiIMasterTenantFabricVO, String> tenantUuid;
    public static volatile SingularAttribute<HuaweiIMasterTenantFabricVO, String> fabricUuid;
    public static volatile SingularAttribute<HuaweiIMasterTenantFabricVO, Timestamp> createDate;
    public static volatile SingularAttribute<HuaweiIMasterTenantFabricVO, Timestamp> lastOpDate;
}
