package org.zstack.network.huawei.imaster;

import org.springframework.http.HttpMethod;
import org.zstack.header.identity.Action;
import org.zstack.header.message.APIMessage;
import org.zstack.header.message.APIParam;
import org.zstack.header.rest.RestRequest;
import org.zstack.sdnController.header.SdnControllerConstant;
import org.zstack.sdnController.header.SdnControllerMessage;
import org.zstack.header.network.sdncontroller.SdnControllerVO;

/**
 * Created by shixn.ruan 2025.07.29
 */

@RestRequest(
        path = "/sdn-controller/huawei-imaster/{uuid}/actions",
        method = HttpMethod.PUT,
        responseClass = APIPullHuaweiIMasterControllerEvent.class,
        isAction = true
)
@Action(category = SdnControllerConstant.ACTION_CATEGORY)
public class APIPullHuaweiIMasterControllerMsg extends APIMessage implements SdnControllerMessage {
    @APIParam(resourceType = SdnControllerVO.class, checkAccount = true, operationTarget = true)
    private String uuid;

    @APIParam(required = false)
    private boolean pullSwitch = false;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public boolean isPullSwitch() {
        return pullSwitch;
    }

    public void setPullSwitch(boolean pullSwitch) {
        this.pullSwitch = pullSwitch;
    }

    @Override
    public String getSdnControllerUuid() {
        return uuid;
    }

    public static APIPullHuaweiIMasterControllerMsg __example__() {
        APIPullHuaweiIMasterControllerMsg msg = new APIPullHuaweiIMasterControllerMsg();
        msg.setUuid(uuid());

        return msg;
    }
}