package org.zstack.network.huawei.imaster;

import org.zstack.header.vo.EntityGraph;
import org.zstack.header.vo.ForeignKey;
import org.zstack.header.vo.ForeignKey.ReferenceOption;

import javax.persistence.*;
import java.sql.Timestamp;

/**
 * Huawei iMaster Tenant Fabric Reference Value Object
 * This class represents the relationship between HuaweiIMasterTenantVO and HuaweiIMasterFabricVO
 */
@Entity
@Table
@EntityGraph(
        friends = {
                @EntityGraph.Neighbour(type = HuaweiIMasterTenantVO.class, myField = "tenantUuid", targetField = "uuid"),
                @EntityGraph.Neighbour(type = HuaweiIMasterFabricVO.class, myField = "fabricUuid", targetField = "uuid"),
        }
)
public class HuaweiIMasterTenantFabricRefVO {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column
    private Long id;

    @Column
    @ForeignKey(parentEntityClass = HuaweiIMasterTenantVO.class, parentKey = "uuid", onDeleteAction = ReferenceOption.CASCADE)
    private String tenantUuid;

    @Column
    @ForeignKey(parentEntityClass = HuaweiIMasterFabricVO.class, parentKey = "uuid", onDeleteAction = ReferenceOption.CASCADE)
    private String fabricUuid;

    @Column
    private Timestamp createDate;

    @Column
    private Timestamp lastOpDate;

    @PreUpdate
    private void preUpdate() {
        lastOpDate = null;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTenantUuid() {
        return tenantUuid;
    }

    public void setTenantUuid(String tenantUuid) {
        this.tenantUuid = tenantUuid;
    }

    public String getFabricUuid() {
        return fabricUuid;
    }

    public void setFabricUuid(String fabricUuid) {
        this.fabricUuid = fabricUuid;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }
}
