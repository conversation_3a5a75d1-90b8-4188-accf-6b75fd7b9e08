package org.zstack.network.huawei.imaster;

import org.zstack.header.configuration.PythonClassInventory;
import org.zstack.header.search.Inventory;
import org.zstack.header.search.Parent;
import org.zstack.sdnController.SdnControllerSystemTags;
import org.zstack.header.network.sdncontroller.SdnControllerInventory;
import org.zstack.sdnController.header.SdnVlanRange;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Huawei iMaster SDN Controller Inventory
 * Created by shixin.ruan on 07/29/2025.
 */
@Inventory(mappingVOClass = HuaweiIMasterSdnControllerVO.class, collectionValueOfMethod="valueOf1",
        parent = {@Parent(inventoryClass = SdnControllerInventory.class, type = HuaweiIMasterConstant.HUAWEI_IMASTER_CONTROLLER_TYPE)})
@PythonClassInventory
public class HuaweiIMasterSdnControllerInventory extends SdnControllerInventory {
    
    private List<HuaweiIMasterFabricInventory> fabrics;
    private List<HuaweiIMasterTenantInventory> tenants;
    private List<HuaweiIMasterVpcInventory> vpcs;
    private List<HuaweiIMasterVRouterInventory> vrouters;
    private List<SdnVlanRange> vlanRanges;

    public HuaweiIMasterSdnControllerInventory() {
    }

    protected HuaweiIMasterSdnControllerInventory(HuaweiIMasterSdnControllerVO vo) {
        super(vo);
        this.setFabrics(HuaweiIMasterFabricInventory.valueOf(vo.getFabrics()));
        this.setTenants(HuaweiIMasterTenantInventory.valueOf(vo.getTenants()));
        this.setVpcs(HuaweiIMasterVpcInventory.valueOf(vo.getVpcs()));
        this.setVrouters(HuaweiIMasterVRouterInventory.valueOf(vo.getVrouters()));
        this.vlanRanges = new ArrayList<>();
        List<Map<String, String>> vlanRanges = SdnControllerSystemTags.VLAN_RANGE.getTokensOfTagsByResourceUuid(vo.getUuid());
        for (Map<String, String> token : vlanRanges) {
            SdnVlanRange range = new SdnVlanRange();
            range.startVlan = Integer.parseInt(token.get(SdnControllerSystemTags.START_VLAN_TOKEN));
            range.endVlan = Integer.parseInt(token.get(SdnControllerSystemTags.END_VLAN_TOKEN));
            this.vlanRanges.add(range);
        }
    }

    public static HuaweiIMasterSdnControllerInventory valueOf(HuaweiIMasterSdnControllerVO vo) {
        HuaweiIMasterSdnControllerInventory inv = new HuaweiIMasterSdnControllerInventory(vo);
        return inv;
    }

    public static List<HuaweiIMasterSdnControllerInventory> valueOf1(Collection<HuaweiIMasterSdnControllerVO> vos) {
        List<HuaweiIMasterSdnControllerInventory> invs = new ArrayList<>();
        for (HuaweiIMasterSdnControllerVO vo : vos) {
            invs.add(valueOf(vo));
        }
        return invs;
    }

    public List<HuaweiIMasterFabricInventory> getFabrics() {
        return fabrics;
    }

    public void setFabrics(List<HuaweiIMasterFabricInventory> fabrics) {
        this.fabrics = fabrics;
    }

    public List<HuaweiIMasterTenantInventory> getTenants() {
        return tenants;
    }

    public void setTenants(List<HuaweiIMasterTenantInventory> tenants) {
        this.tenants = tenants;
    }

    public List<HuaweiIMasterVpcInventory> getVpcs() {
        return vpcs;
    }

    public void setVpcs(List<HuaweiIMasterVpcInventory> vpcs) {
        this.vpcs = vpcs;
    }

    public List<HuaweiIMasterVRouterInventory> getVrouters() {
        return vrouters;
    }

    public void setVrouters(List<HuaweiIMasterVRouterInventory> vrouters) {
        this.vrouters = vrouters;
    }

    public List<SdnVlanRange> getVlanRanges() {
        return vlanRanges;
    }

    public void setVlanRanges(List<SdnVlanRange> vlanRanges) {
        this.vlanRanges = vlanRanges;
    }
}
