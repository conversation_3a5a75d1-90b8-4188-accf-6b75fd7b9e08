package org.zstack.network.huawei.imaster;

import com.aliyuncs.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.http.HttpMethod;
import org.zstack.core.CoreGlobalProperty;
import org.zstack.core.Platform;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cascade.CascadeFacade;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.header.core.Completion;
import org.zstack.header.core.NopeCompletion;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.core.workflow.*;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.host.HostInventory;
import org.zstack.header.host.HostVO;
import org.zstack.header.host.HostVO_;
import org.zstack.header.message.APIDeleteMessage;
import org.zstack.header.message.Message;
import org.zstack.header.message.MessageReply;
import org.zstack.header.network.l2.*;
import org.zstack.header.network.l3.*;
import org.zstack.header.network.sdncontroller.SdnControllerInventory;
import org.zstack.header.network.sdncontroller.SdnControllerVO;
import org.zstack.network.hostNetworkInterface.*;
import org.zstack.network.hostNetworkInterface.lldp.entity.HostNetworkInterfaceLldpRefVO;
import org.zstack.network.hostNetworkInterface.lldp.entity.HostNetworkInterfaceLldpRefVO_;
import org.zstack.network.hostNetworkInterface.lldp.entity.HostNetworkInterfaceLldpVO;
import org.zstack.network.hostNetworkInterface.lldp.entity.HostNetworkInterfaceLldpVO_;
import org.zstack.network.l2.vxlan.vxlanNetwork.L2VxlanNetworkInventory;
import org.zstack.sdnController.*;
import org.zstack.sdnController.header.*;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;
import org.zstack.utils.network.NetworkUtils;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.Arrays.asList;
import static org.zstack.core.Platform.argerr;
import static org.zstack.core.Platform.operr;
import static org.zstack.network.huawei.imaster.HuaweiIMasterConstant.HUAWEI_IMASTER_FABRIC_DEFAULT_VENDOR_VERSION;
import static org.zstack.network.huawei.imaster.HuaweiIMasterNceFabricCommands.*;

@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class HuaweiIMasterSdnController implements SdnController, SdnControllerL2, SdnControllerL3 {
    private static final CLogger logger = Utils.getLogger(HuaweiIMasterSdnController.class);

    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    CloudBus bus;
    @Autowired
    private CascadeFacade casf;

    private HuaweiIMasterSdnControllerVO self;
    private HuaweiIMasterHelper helper = new HuaweiIMasterHelper();
    private HardwareVxlanHelper vxlanHelper = new HardwareVxlanHelper();

    public HuaweiIMasterSdnController(HuaweiIMasterSdnControllerVO self) {
        this.self = self;
    }
    
    @Override
    public void handleMessage(SdnControllerMessage msg) {
        if (msg instanceof APICreateHuaweiIMasterVRouterMsg) {
            handle((APICreateHuaweiIMasterVRouterMsg) msg);
        } else if (msg instanceof APIDeleteHuaweiIMasterVRouterMsg) {
            handle((APIDeleteHuaweiIMasterVRouterMsg) msg);
        } else if (msg instanceof APIPullHuaweiIMasterControllerMsg) {
            handle((APIPullHuaweiIMasterControllerMsg)msg);
        } else if (msg instanceof APIDeleteHuaweiIMasterTenantMsg) {
            handle((APIDeleteHuaweiIMasterTenantMsg)msg);
        } else if (msg instanceof APIDeleteHuaweiIMasterFabricMsg) {
            handle((APIDeleteHuaweiIMasterFabricMsg)msg);
        } else if (msg instanceof APIDeleteHuaweiIMasterVpcMsg) {
            handle((APIDeleteHuaweiIMasterVpcMsg)msg);
        } else if (msg instanceof SdnControllerPingMsg) {
            handle((SdnControllerPingMsg)msg);
        } else if (msg instanceof HuaweiIMasterFabricDeletionMsg) {
            handle((HuaweiIMasterFabricDeletionMsg)msg);
        } else if (msg instanceof HuaweiIMasterTenantDeletionMsg) {
            handle((HuaweiIMasterTenantDeletionMsg)msg);
        } else if (msg instanceof HuaweiIMasterVpcDeletionMsg) {
            handle((HuaweiIMasterVpcDeletionMsg)msg);
        } else if (msg instanceof HuaweiIMasterVRouterDeletionMsg) {
            handle((HuaweiIMasterVRouterDeletionMsg)msg);
        } else if (msg instanceof HuaweiIMasterUpdateLogicalSwitchPortMsg) {
            handle((HuaweiIMasterUpdateLogicalSwitchPortMsg)msg);
        } else {
            bus.dealWithUnknownMessage((Message) msg);
        }
    }

    private void handle(HuaweiIMasterUpdateLogicalSwitchPortMsg msg) {
        HuaweiIMasterUpdateLogicalSwitchPortReply reply = new HuaweiIMasterUpdateLogicalSwitchPortReply();
        List<HardwareL2VxlanNetworkVO> hardwareL2Vos = Q.New(HardwareL2VxlanNetworkVO.class)
                .eq(HardwareL2VxlanNetworkVO_.poolUuid, msg.hardwareVxlanPoolUuid).list();
        if (hardwareL2Vos.isEmpty()) {
            bus.reply(msg, reply);
            return;
        }

        new While<>(hardwareL2Vos).each((vo, comp) -> {
            List<Flow> flows = new ArrayList<>();
            HardwareL2VxlanNetworkInventory inv = HardwareL2VxlanNetworkInventory.valueOf(vo);
            flows.add(new NoRollbackFlow() {
                String __name__ = "get-old-logical-switch-port";

                @Override
                public void run(FlowTrigger trigger, Map data) {
                    String token = (String) data.get("token");

                    getHuaweiIMasterControllerLogicalSwitchPort(self, token, inv,
                            new ReturnValueCompletion<HuaweiLogicalSwitchPortStruct>(trigger) {
                        @Override
                        public void success(HuaweiLogicalSwitchPortStruct returnValue) {
                            List<HuaweiLogicalSwitchPortLocationStruct> curLocations = new ArrayList<>();
                            if (returnValue == null) {
                                data.put("locations", curLocations);
                                trigger.next();
                                return;
                            }

                            if (returnValue.accessInfo == null || returnValue.accessInfo.location == null) {
                                data.put("locations", curLocations);
                                trigger.next();
                                return;
                            }
                            for (HuaweiLogicalSwitchPortLocationStruct loc : returnValue.accessInfo.location) {
                                // ignore all fields except: deviceId, portName
                                loc.deviceGroupId = null;
                                loc.deviceGroupName = null;
                                loc.deviceName = null;
                                loc.deviceId = null;
                                loc.deviceIp = null;
                                curLocations.add(loc);
                            }
                            data.put("locations", curLocations);
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
            flows.add(new NoRollbackFlow() {
                String __name__ = "update-logical-switch-port";

                @Override
                public void run(FlowTrigger trigger, Map data) {
                    String token = (String) data.get("token");
                    List<HuaweiLogicalSwitchPortLocationStruct> curLocations =
                            (List<HuaweiLogicalSwitchPortLocationStruct>) data.get("locations");
                    HttpMethod method = HttpMethod.PUT;
                    if (curLocations == null) {
                        curLocations = new ArrayList<>();
                        method = HttpMethod.POST;
                    }

                    logger.debug("old location: " + JSONObjectUtil.toJsonString(curLocations));
                    if (msg.getOldPort() != null) {
                        curLocations.removeIf(loc -> loc.portName.equals(msg.getOldPort().getName()));
                    }
                    curLocations.add(new HuaweiLogicalSwitchPortLocationStruct(msg.getNewPort()));

                    logger.debug("new location: " + JSONObjectUtil.toJsonString(curLocations));

                    HostInventory hostInv = HostInventory.valueOf(dbf.findByUuid(msg.hostUuid, HostVO.class));
                    updateHuaweiIMasterControllerLogicalSwitchPort(self, token, curLocations, inv,
                            hostInv, method, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });

            FlowChain chain = getHuaweiIMasterFlowChain(String.format("attach-logical-switch-to-hosts"),
                    flows, new Completion(msg) {
                        @Override
                        public void success() {
                            comp.done();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            comp.addError(errorCode);
                            comp.done();
                        }
                    });

            chain.start();
        }).run(new WhileDoneCompletion(msg) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (errorCodeList.getCauses().isEmpty()) {
                    bus.reply(msg, reply);
                } else {
                    reply.setError(errorCodeList.getCauses().get(0));
                    bus.reply(msg, reply);
                }
            }
        });
    }


    private void handle(HuaweiIMasterVRouterDeletionMsg msg) {
        HuaweiIMasterVRouterDeletionReply reply = new HuaweiIMasterVRouterDeletionReply();
        doDeleteHuaweiIMasterLogicalRouter(Collections.singletonList(msg.getvRouterUuid()), new Completion(msg) {
            @Override
            public void success() {
                SQL.New(HuaweiIMasterVRouterVO.class)
                        .eq(HuaweiIMasterVRouterVO_.uuid, msg.getvRouterUuid()).delete();
                bus.reply(msg, reply);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                reply.setError(errorCode);
                bus.reply(msg, reply);
            }
        });
    }

    private void handle(HuaweiIMasterVpcDeletionMsg msg) {
        HuaweiIMasterVpcDeletionReply reply = new HuaweiIMasterVpcDeletionReply();
        List<String> l2Uuids = Q.New(HardwareL2VxlanNetworkVO.class).select(HardwareL2VxlanNetworkVO_.uuid).listValues();
        if (l2Uuids.isEmpty()) {
            SQL.New(HuaweiIMasterVpcVO.class)
                    .eq(HuaweiIMasterVpcVO_.uuid, msg.getVpcUuid()).delete();
            bus.reply(msg, reply);
            return;
        }

        new While<>(l2Uuids).each((uuid, wcopl) -> {
            String vpcUuid = HuaweiIMasterHelper.getL2NetworkVpcId(uuid);
            if (vpcUuid == null || !vpcUuid.equals(msg.vpcUuid)) {
                wcopl.done();
                return;
            }

            L2NetworkDeletionMsg dmsg = new L2NetworkDeletionMsg();
            dmsg.setL2NetworkUuid(uuid);
            bus.makeTargetServiceIdByResourceUuid(dmsg, L2NetworkConstant.SERVICE_ID, uuid);
            bus.send(dmsg, new CloudBusCallBack(dmsg) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        logger.debug(String.format("delete l2 network[uuid:%s] failed, because: %s",
                                uuid, reply.getError().getDetails()));
                    }
                    wcopl.done();
                }
            });
        }).run(new WhileDoneCompletion(msg) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                SQL.New(HuaweiIMasterVpcVO.class)
                        .eq(HuaweiIMasterVpcVO_.uuid, msg.getVpcUuid()).delete();
                bus.reply(msg, reply);
            }
        });
    }

    private void handle(HuaweiIMasterFabricDeletionMsg msg) {
        // ordinary resource is delete in cascade
        HuaweiIMasterFabricDeletionReply reply = new HuaweiIMasterFabricDeletionReply();
        SQL.New(HuaweiIMasterFabricVO.class)
                .eq(HuaweiIMasterFabricVO_.uuid, msg.getFabricUuid()).delete();
        bus.reply(msg, reply);
    }

    private void handle(HuaweiIMasterTenantDeletionMsg msg) {
        // ordinary resource is delete in cascade
        HuaweiIMasterTenantDeletionReply reply = new HuaweiIMasterTenantDeletionReply();
        SQL.New(HuaweiIMasterTenantVO.class)
                .eq(HuaweiIMasterTenantVO_.uuid, msg.getTenantUuid()).delete();
        bus.reply(msg, reply);
    }

    private void handle(APIPullHuaweiIMasterControllerMsg msg) {
        APIPullHuaweiIMasterControllerEvent event = new APIPullHuaweiIMasterControllerEvent(msg.getId());
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("init-huawei-imaster-%s", self.getIp()));
        chain.getData().put("accountUuid", msg.getSession().getAccountUuid());
        chain.getData().put("addSdnFlow", false);
        chain.then(getHuaweiIMasterTokenFlow());
        chain.then(getHuaweiIMasterFabricFlow());
        chain.then(getHuaweiIMasterTenantFlow());
        chain.then(getHuaweiIMasterVpcFlow());
        if (msg.isPullSwitch()) {
            chain.then(getHuaweiIMasterSwitchFlow());
            chain.then(getHuaweiIMasterSwitchPortFlow());
        }
        chain.then(deleteHuaweiIMasterTokenFlow());
        chain.done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                List<HuaweiIMasterSdnControllerInventory> invs = new ArrayList<>();
                HuaweiIMasterSdnControllerVO vo = dbf.findByUuid(msg.getSdnControllerUuid(), HuaweiIMasterSdnControllerVO.class);
                invs.add(HuaweiIMasterSdnControllerInventory.valueOf(vo));
                event.setInventories(invs);
                bus.publish(event);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                event.setError(errCode);
                bus.publish(event);
            }
        }).start();

    }

    private void handle(APICreateHuaweiIMasterVRouterMsg msg) {
        APICreateHuaweiIMasterVRouterEvent event = new APICreateHuaweiIMasterVRouterEvent(msg.getId());

        HuaweiIMasterVpcVO vpc = dbf.findByUuid(msg.getHuaweiVpcUuid(), HuaweiIMasterVpcVO.class);
        HuaweiIMasterVRouterVO vo = new HuaweiIMasterVRouterVO();
        if (msg.getResourceUuid() != null) {
            vo.setUuid(msg.getResourceUuid());
        } else {
            vo.setUuid(Platform.getUuid());
        }
        vo.setName(msg.getName());
        vo.setDescription(msg.getDescription());
        vo.setTenantId(vpc.getTenantId());
        vo.setLogicalNetworkId(msg.getHuaweiVpcUuid());
        vo.setAccountUuid(msg.getSession().getAccountUuid());
        vo.setFabricUuid(vpc.getFabricId());
        vo.setSdnControllerUuid(self.getUuid());
        vo.setState(SdnControllerTableState.Enabled);

        List<Flow> flows = new ArrayList<>();
        flows.add(new NoRollbackFlow() {
            String __name__ = "create-logical-router";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                createHuaweiIMasterLogicalRouter(self, token,
                        HuaweiIMasterVRouterInventory.valueOf(vo), new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(
                String.format("create-huawei-imaster-logical-router"), flows, new Completion(msg) {
            @Override
            public void success() {
                HuaweiIMasterVRouterVO fvo = dbf.persistAndRefresh(vo);
                event.setInventory(HuaweiIMasterVRouterInventory.valueOf(fvo));
                bus.publish(event);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                event.setError(errorCode);
                bus.publish(event);
            }
        });

        chain.start();
    }

    private void doDeleteHuaweiIMasterLogicalRouter(List<String> vRouterUuids, Completion completion) {
        List<Flow> flows = new ArrayList<>();
        flows.add(new NoRollbackFlow() {
            String __name__ = "delete-logical-router";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                new While<>(vRouterUuids).each((uuid, wcompl) -> {
                    deleteHuaweiIMasterLogicalRouter(self, token, uuid, new Completion(trigger) {
                        @Override
                        public void success() {
                            wcompl.done();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            wcompl.addError(errorCode);
                            wcompl.allDone();
                        }
                    });
                }).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        if (errorCodeList.getCauses().isEmpty()) {
                            trigger.next();
                        } else {
                            trigger.fail(errorCodeList);
                        }
                    }
                });
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(
                String.format("delete-huawei-imaster-logical-router"), flows, new Completion(completion) {
                    @Override
                    public void success() {
                        SQL.New(HuaweiIMasterVRouterVO.class).in(HuaweiIMasterVRouterVO_.uuid, vRouterUuids).delete();
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });

        chain.start();
    }

    Flow deleteTenantL2NetworkFlow() {
        return new NoRollbackFlow() {
            String __name__ = "delete-tenant-l2-network";
            @Override
            public void run(FlowTrigger trigger, Map data) {
                List<String> l2NetworkUuids = (List<String>) data.get("l2NetworkUuids");
                if (l2NetworkUuids.isEmpty()) {
                    trigger.next();
                    return;
                }

                new While<>(l2NetworkUuids).each((uuid, wcomp) -> {
                    DeleteL2NetworkMsg dmsg = new DeleteL2NetworkMsg();
                    dmsg.setUuid(uuid);
                    bus.makeTargetServiceIdByResourceUuid(dmsg, L2NetworkConstant.SERVICE_ID, uuid);
                    bus.send(dmsg, new CloudBusCallBack(wcomp) {
                        @Override
                        public void run(MessageReply reply) {
                            if (!reply.isSuccess()) {
                                logger.info(String.format("delete hardware vxlan network[uuid:%s] failed, reason:%s", uuid, reply.getError().getDetails()));
                            }
                            wcomp.done();
                        }
                    });
                }).run(new WhileDoneCompletion(trigger) {
                    @Override
                    public void done(ErrorCodeList errorCodeList) {
                        if (errorCodeList.getCauses().isEmpty()) {
                            trigger.next();
                        } else {
                            trigger.fail(errorCodeList);
                        }
                    }
                });
            }
        };
    }

    private void handle(APIDeleteHuaweiIMasterTenantMsg msg) {
        APIDeleteHuaweiIMasterTenantEvent event = new APIDeleteHuaweiIMasterTenantEvent(msg.getId());

        final String issuer = HuaweiIMasterTenantVO.class.getSimpleName();
        HuaweiIMasterTenantVO vo = dbf.findByUuid(msg.getUuid(), HuaweiIMasterTenantVO.class);
        final List<HuaweiIMasterTenantInventory> ctx = asList(HuaweiIMasterTenantInventory.valueOf(vo));
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("delete-huawei-imaster-tenant-%s-%s", msg.getUuid(), vo.getName()));
        List<String> l2NetworkUuids = helper.getL2UuidsByTenantUuid(msg.getUuid());
        chain.getData().put("l2NetworkUuids", l2NetworkUuids);
        chain.then(deleteTenantL2NetworkFlow());
        if (msg.getDeletionMode() == APIDeleteMessage.DeletionMode.Permissive) {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_CHECK_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            }).then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        } else {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_FORCE_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        }
        chain.done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                casf.asyncCascadeFull(CascadeConstant.DELETION_CLEANUP_CODE, issuer, ctx, new NopeCompletion());
                bus.publish(event);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                event.setError(errCode);
                bus.publish(event);
            }
        }).start();
    }

    private void handle(APIDeleteHuaweiIMasterFabricMsg msg) {
        APIDeleteHuaweiIMasterFabricEvent event = new APIDeleteHuaweiIMasterFabricEvent(msg.getId());

        final String issuer = HuaweiIMasterFabricVO.class.getSimpleName();
        HuaweiIMasterFabricVO vo = dbf.findByUuid(msg.getUuid(), HuaweiIMasterFabricVO.class);
        final List<HuaweiIMasterFabricInventory> ctx = asList(HuaweiIMasterFabricInventory.valueOf(vo));
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("delete-huawei-imaster-fabric-%s-%s", msg.getUuid(), vo.getName()));
        List<String> l2NetworkUuids = helper.getL2UuidsByFarbicUuid(msg.getUuid());
        chain.getData().put("l2NetworkUuids", l2NetworkUuids);
        chain.then(deleteTenantL2NetworkFlow());
        if (msg.getDeletionMode() == APIDeleteMessage.DeletionMode.Permissive) {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_CHECK_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            }).then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        } else {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_FORCE_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        }
        chain.done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                casf.asyncCascadeFull(CascadeConstant.DELETION_CLEANUP_CODE, issuer, ctx, new NopeCompletion());
                bus.publish(event);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                event.setError(errCode);
                bus.publish(event);
            }
        }).start();
    }

    private void handle(APIDeleteHuaweiIMasterVpcMsg msg) {
        APIDeleteHuaweiIMasterVpcEvent event = new APIDeleteHuaweiIMasterVpcEvent(msg.getId());

        final String issuer = HuaweiIMasterVpcVO.class.getSimpleName();
        HuaweiIMasterVpcVO vo = dbf.findByUuid(msg.getUuid(), HuaweiIMasterVpcVO.class);
        final List<HuaweiIMasterVpcInventory> ctx = asList(HuaweiIMasterVpcInventory.valueOf(vo));
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("delete-huawei-imaster-vpc-%s-%s", msg.getUuid(), vo.getName()));
        List<String> l2NetworkUuids = helper.getL2UuidsByVpcUuid(msg.getUuid());
        chain.getData().put("l2NetworkUuids", l2NetworkUuids);
        chain.then(deleteTenantL2NetworkFlow());
        if (msg.getDeletionMode() == APIDeleteMessage.DeletionMode.Permissive) {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_CHECK_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            }).then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        } else {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_FORCE_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        }
        chain.done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                casf.asyncCascadeFull(CascadeConstant.DELETION_CLEANUP_CODE, issuer, ctx, new NopeCompletion());
                bus.publish(event);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                event.setError(errCode);
                bus.publish(event);
            }
        }).start();
    }

    private void handle(APIDeleteHuaweiIMasterVRouterMsg msg) {
        APIDeleteHuaweiIMasterVRouterEvent event = new APIDeleteHuaweiIMasterVRouterEvent(msg.getId());

        final String issuer = HuaweiIMasterVRouterVO.class.getSimpleName();
        HuaweiIMasterVRouterVO vo = dbf.findByUuid(msg.getUuid(), HuaweiIMasterVRouterVO.class);
        final List<HuaweiIMasterVRouterInventory> ctx = asList(HuaweiIMasterVRouterInventory.valueOf(vo));
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("delete-huawei-imaster-vrouter-%s-%s", msg.getUuid(), vo.getName()));
        if (msg.getDeletionMode() == APIDeleteMessage.DeletionMode.Permissive) {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_CHECK_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            }).then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        } else {
            chain.then(new NoRollbackFlow() {
                @Override
                public void run(final FlowTrigger trigger, Map data) {
                    casf.asyncCascade(CascadeConstant.DELETION_FORCE_DELETE_CODE, issuer, ctx, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                }
            });
        }
        chain.done(new FlowDoneHandler(msg) {
            @Override
            public void handle(Map data) {
                casf.asyncCascadeFull(CascadeConstant.DELETION_CLEANUP_CODE, issuer, ctx, new NopeCompletion());
                bus.publish(event);
            }
        }).error(new FlowErrorHandler(msg) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                event.setError(errCode);
                bus.publish(event);
            }
        }).start();
    }

    void handle(SdnControllerPingMsg msg) {
        SdnControllerPingReply reply = new SdnControllerPingReply();

        getHuaweiIMasterToken(self, new ReturnValueCompletion<String>(msg) {
            @Override
            public void success(String returnValue) {
                deleteHuaweiIMasterToken(self, returnValue, new Completion(msg) {
                    @Override
                    public void success() {
                        bus.reply(msg, reply);
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        //ignore delete toke error
                        bus.reply(msg, reply);
                    }
                });
            }

            @Override
            public void fail(ErrorCode errorCode) {
                reply.setError(errorCode);
                bus.reply(msg, reply);
            }
        });
    }

    @Override
    @SdnControllerLog
    public void preInitSdnController(APIAddSdnControllerMsg msg, Completion completion) {
        completion.success();
    }

    @Override
    public void createSdnControllerDb(APIAddSdnControllerMsg msg, SdnControllerVO vo, Completion completion) {
        HuaweiIMasterSdnControllerVO hwVo = new HuaweiIMasterSdnControllerVO(vo);
        if (hwVo.getVendorVersion() == null) {
            hwVo.setVendorVersion(HUAWEI_IMASTER_FABRIC_DEFAULT_VENDOR_VERSION);
        }
        dbf.persist(hwVo);
        completion.success();
    }

    @Override
    public void deleteSdnControllerDb(SdnControllerVO vo) {
        dbf.removeByPrimaryKey(vo.getUuid(), HuaweiIMasterSdnControllerVO.class);
    }

    private Flow getHuaweiIMasterFabricFlow() {
        return new Flow() {
            String __name__ = "get_huawei_imaster_fabric";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String accountUuid = (String) data.get("accountUuid");
                boolean addSdnFlow = (boolean) data.get("addSdnFlow");
                String token = (String) data.get("token");
                getHuaweiIMasterControllerFabric(self, token, new ReturnValueCompletion<List<HuaweiIMasterFabricVO>>(trigger) {
                    @Override
                    public void success(List<HuaweiIMasterFabricVO> returnValue) {
                        if (returnValue == null || returnValue.isEmpty()) {
                            if (addSdnFlow) {
                                trigger.fail(operr("there is no fabric on sdn controller [ip:%s]", self.getIp()));
                            } else {
                                SQL.New(HuaweiIMasterFabricVO.class)
                                        .eq(HuaweiIMasterFabricVO_.sdnControllerUuid, self.getUuid())
                                        .set(HuaweiIMasterFabricVO_.state, SdnControllerTableState.Disabled).update();
                                trigger.next();
                            }
                            return;
                        }

                        List<String> fabricIds = returnValue.stream().map(vo ->
                                vo.getUuid()).collect(Collectors.toList());
                        List<String> oldfabricIds = Q.New(HuaweiIMasterFabricVO.class)
                                .eq(HuaweiIMasterFabricVO_.sdnControllerUuid, self.getUuid())
                                .select(HuaweiIMasterFabricVO_.uuid).listValues();

                        //disable not exised
                        SQL.New(HuaweiIMasterFabricVO.class)
                                .eq(HuaweiIMasterFabricVO_.sdnControllerUuid, self.getUuid())
                                .notIn(HuaweiIMasterFabricVO_.uuid, fabricIds)
                                .set(HuaweiIMasterFabricVO_.state, SdnControllerTableState.Disabled).update();

                        // update old
                        List<HuaweiIMasterFabricVO> oldFabrics = returnValue.stream().filter(
                                        vo -> oldfabricIds.contains(vo.getUuid()))
                                .collect(Collectors.toList());
                        for (HuaweiIMasterFabricVO vo: oldFabrics) {
                            SQL.New(HuaweiIMasterFabricVO.class)
                                    .eq(HuaweiIMasterFabricVO_.uuid, vo.getUuid())
                                    .set(HuaweiIMasterFabricVO_.name, vo.getName())
                                    .set(HuaweiIMasterFabricVO_.description, vo.getDescription()).update();
                        }

                        //add new
                        fabricIds.removeAll(oldfabricIds);
                        if (fabricIds.isEmpty()) {
                            trigger.next();
                            return;
                        }
                        List<HuaweiIMasterFabricVO> newFarbics = returnValue.stream().filter(
                                        vo -> fabricIds.contains(vo.getUuid()))
                                .collect(Collectors.toList());

                        for (HuaweiIMasterFabricVO vo: newFarbics) {
                            vo.setAccountUuid(accountUuid);
                            vo.setSdnControllerUuid(self.getUuid());
                        }

                        dbf.persistCollection(newFarbics);
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                SQL.New(HuaweiIMasterFabricVO.class).eq(HuaweiIMasterFabricVO_.sdnControllerUuid, self.getUuid()).delete();
                trigger.rollback();
            }
        };
    }

    private void syncHuaweiIMasterTenantFabricRef(String tenantUuid, Set<HuaweiIMasterTenantFabricRefVO> refVOS) {
        List<String> fabricIds = refVOS.stream().map(HuaweiIMasterTenantFabricRefVO::getFabricUuid).collect(Collectors.toList());
        // delete not existed ref
        SQL.New(HuaweiIMasterTenantFabricRefVO.class)
                .eq(HuaweiIMasterTenantFabricRefVO_.tenantUuid, tenantUuid)
                .notIn(HuaweiIMasterTenantFabricRefVO_.fabricUuid, fabricIds).delete();
        List<String> existedFabricIds = Q.New(HuaweiIMasterTenantFabricRefVO.class)
                .select(HuaweiIMasterTenantFabricRefVO_.fabricUuid)
                .eq(HuaweiIMasterTenantFabricRefVO_.tenantUuid, tenantUuid).listValues();
        fabricIds.removeAll(existedFabricIds);
        if (!fabricIds.isEmpty()) {
            List<HuaweiIMasterTenantFabricRefVO> newRefVOS = new ArrayList<>();
            for (String fabricId : fabricIds) {
                HuaweiIMasterTenantFabricRefVO refVO = new HuaweiIMasterTenantFabricRefVO();
                refVO.setFabricUuid(fabricId);
                refVO.setTenantUuid(tenantUuid);
                newRefVOS.add(refVO);
            }
            dbf.persistCollection(newRefVOS);
        }
    }

    private Flow getHuaweiIMasterTenantFlow() {
        return new Flow() {
            String __name__ = "get_huawei_imaster_tenant";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String accountUuid = (String) data.get("accountUuid");
                boolean addSdnFlow = (boolean) data.get("addSdnFlow");
                String token = (String) data.get("token");
                getHuaweiIMasterControllerTenant(self, token, new ReturnValueCompletion<List<HuaweiIMasterTenantVO>>(trigger) {
                    @Override
                    public void success(List<HuaweiIMasterTenantVO> returnValue) {
                        if (returnValue == null || returnValue.isEmpty()) {
                            if (addSdnFlow) {
                                trigger.fail(operr("there is no tenant on sdn controller [ip:%s]", self.getIp()));
                            } else {
                                SQL.New(HuaweiIMasterTenantVO.class)
                                        .eq(HuaweiIMasterTenantVO_.sdnControllerUuid, self.getUuid())
                                        .set(HuaweiIMasterTenantVO_.state, SdnControllerTableState.Disabled).update();
                                trigger.next();
                            }
                            return;
                        }

                        List<HuaweiIMasterTenantVO> tenantVOS = new ArrayList<>();
                        Map<String, List<HuaweiIMasterTenantFabricRefVO>> tenantFabricRefMap = new HashMap<>();
                        for (HuaweiIMasterTenantVO tenant : returnValue) {
                            tenantFabricRefMap.put(tenant.getUuid(), new ArrayList<>(tenant.getFabricIds()));

                            tenant.setState(SdnControllerTableState.Enabled);
                            tenant.setSdnControllerUuid(self.getUuid());
                            tenant.setAccountUuid(accountUuid);
                            tenantVOS.add(tenant);
                        }

                        List<String> tenantIds = tenantVOS.stream().map(vo ->
                                vo.getUuid()).collect(Collectors.toList());
                        List<String> oldTenantIds = Q.New(HuaweiIMasterTenantVO.class)
                                .eq(HuaweiIMasterTenantVO_.sdnControllerUuid, self.getUuid())
                                .select(HuaweiIMasterTenantVO_.uuid).listValues();

                        //disable not exised
                        SQL.New(HuaweiIMasterTenantVO.class)
                                .eq(HuaweiIMasterTenantVO_.sdnControllerUuid, self.getUuid())
                                .notIn(HuaweiIMasterTenantVO_.uuid, tenantIds)
                                .set(HuaweiIMasterTenantVO_.state, SdnControllerTableState.Disabled).update();

                        // update old
                        List<HuaweiIMasterTenantVO> oldTenants = tenantVOS.stream().filter(
                                        vo -> oldTenantIds.contains(vo.getUuid()))
                                .collect(Collectors.toList());
                        for (HuaweiIMasterTenantVO vo: oldTenants) {
                            SQL.New(HuaweiIMasterTenantVO.class)
                                    .eq(HuaweiIMasterTenantVO_.uuid, vo.getUuid())
                                    .set(HuaweiIMasterTenantVO_.name, vo.getName())
                                    .set(HuaweiIMasterTenantVO_.description, vo.getDescription())
                                    .set(HuaweiIMasterTenantVO_.state, SdnControllerTableState.Enabled).update();
                        }

                        //add new
                        tenantIds.removeAll(oldTenantIds);
                        if (tenantIds.isEmpty()) {
                            for (Map.Entry<String, List<HuaweiIMasterTenantFabricRefVO>> entry : tenantFabricRefMap.entrySet()) {
                                syncHuaweiIMasterTenantFabricRef(entry.getKey(), new HashSet<>(entry.getValue()));
                            }
                            trigger.next();
                            return;
                        }
                        List<HuaweiIMasterTenantVO> newTenant = tenantVOS.stream().filter(
                                        vo -> tenantIds.contains(vo.getUuid()))
                                .collect(Collectors.toList());

                        for (HuaweiIMasterTenantVO vo: newTenant) {
                            vo.setAccountUuid(accountUuid);
                            vo.setSdnControllerUuid(self.getUuid());
                        }

                        dbf.persistCollection(newTenant);

                        for (Map.Entry<String, List<HuaweiIMasterTenantFabricRefVO>> entry : tenantFabricRefMap.entrySet()) {
                            syncHuaweiIMasterTenantFabricRef(entry.getKey(), new HashSet<>(entry.getValue()));
                        }
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                SQL.New(HuaweiIMasterTenantVO.class).eq(HuaweiIMasterTenantVO_.sdnControllerUuid, self.getUuid()).delete();
                trigger.rollback();
            }
        };
    }

    private Flow getHuaweiIMasterSwitchFlow() {
        return new Flow() {
            String __name__ = "get_huawei_imaster_phy_switch";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String accountUuid = (String) data.get("accountUuid");
                String token = (String) data.get("token");
                boolean addSdnFlow = (boolean) data.get("addSdnFlow");

                getHuaweiIMasterControllerSwitches(self, token,
                        new ReturnValueCompletion<List<PhysicalSwitchVO>>(trigger) {
                            @Override
                            public void success(List<PhysicalSwitchVO> returnValue) {
                                if (returnValue == null || returnValue.isEmpty()) {
                                    if (addSdnFlow) {
                                        trigger.fail(operr("there is no switch on sdn controller [ip:%s]", self.getIp()));
                                    }
                                    trigger.next();
                                    return;
                                }

                                // delete old switch and switch port
                                SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.sdnControllerUuid, self.getUuid()).delete();
                                SQL.New(PhysicalSwitchVO.class).eq(PhysicalSwitchVO_.sdnControllerUuid, self.getUuid()).delete();

                                for (PhysicalSwitchVO vo: returnValue) {
                                    vo.setAccountUuid(accountUuid);
                                    vo.setSdnControllerUuid(self.getUuid());
                                }

                                dbf.persistCollection(returnValue);
                                data.put("switchVOS", returnValue);
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                List<PhysicalSwitchVO> switchVOS = (List<PhysicalSwitchVO>) data.get("switchVOS");
                if (switchVOS != null && !switchVOS.isEmpty()) {
                    SQL.New(PhysicalSwitchVO.class)
                            .in(PhysicalSwitchVO_.uuid, switchVOS.stream().map(PhysicalSwitchVO::getUuid).collect(Collectors.toList()))
                            .delete();
                }
                trigger.rollback();
            }
        };
    }

    private Flow getHuaweiIMasterSwitchPortFlow() {
        return new Flow() {
            String __name__ = "get_huawei_imaster_switch_ports";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String accountUuid = (String) data.get("accountUuid");
                String token = (String) data.get("token");
                boolean addSdnFlow = (boolean) data.get("addSdnFlow");
                List<PhysicalSwitchVO> switchVos = (List<PhysicalSwitchVO>) data.get("switchVOS");

                if (switchVos == null || switchVos.isEmpty()) {
                    if (addSdnFlow) {
                        trigger.fail(argerr("there is no switch attached to sdn controller"));
                    } else {
                        trigger.next();
                    }
                    return;
                }

                Map<String, String> switchIdToNameMap = switchVos.stream().collect(
                        Collectors.toMap(vo -> vo.getUuid(), vo -> vo.getName()));
                List<String> switchIds = switchVos.stream().map(vo ->
                        HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(vo.getUuid())).collect(Collectors.toList());
                getHuaweiIMasterControllerSwitchPorts(self, token, switchIds,
                        new ReturnValueCompletion<Map<String, List<PhysicalSwitchPortVO>>>(trigger) {
                            @Override
                            public void success(Map<String, List<PhysicalSwitchPortVO>> returnValue) {
                                List<PhysicalSwitchPortVO> newSwitchPortVOS = new ArrayList<>();
                                for (Map.Entry<String, List<PhysicalSwitchPortVO>> entry: returnValue.entrySet()) {
                                    for (PhysicalSwitchPortVO vo : entry.getValue()) {
                                        vo.setAccountUuid(accountUuid);
                                        vo.setSdnControllerUuid(self.getUuid());
                                        String switchName = switchIdToNameMap.get(vo.getSwitchUuid());
                                        if (switchName != null) {
                                            String lldpUuid = Q.New(HostNetworkInterfaceLldpRefVO.class).select(HostNetworkInterfaceLldpRefVO_.lldpUuid)
                                                    .eq(HostNetworkInterfaceLldpRefVO_.systemName, switchName)
                                                    .eq(HostNetworkInterfaceLldpRefVO_.portId, vo.getName())
                                                    .findValue();
                                            if (lldpUuid != null) {
                                                String interfaceUuid = Q.New(HostNetworkInterfaceLldpVO.class).select(HostNetworkInterfaceLldpVO_.interfaceUuid)
                                                        .eq(HostNetworkInterfaceLldpVO_.uuid, lldpUuid).findValue();
                                                logger.debug(String.format("link switch port[switchName:%s, portName:%s] to interface[%s]",
                                                        switchName, vo.getName(), interfaceUuid));
                                                vo.setPeerInterfaceUuid(interfaceUuid);
                                            }
                                        }

                                        newSwitchPortVOS.add(vo);
                                    }
                                }

                                if (!newSwitchPortVOS.isEmpty()) {
                                    dbf.persistCollection(newSwitchPortVOS);
                                }

                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                trigger.rollback();
            }
        };
    }

    private Flow getHuaweiIMasterVpcFlow() {
        return new Flow() {
            String __name__ = "get_huawei_imaster_vpc";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String accountUuid = (String) data.get("accountUuid");
                String token = (String) data.get("token");
                boolean addSdnFlow = (boolean) data.get("addSdnFlow");
                getHuaweiIMasterControllerVpc(self, token,
                        new ReturnValueCompletion<List<HuaweiIMasterVpcVO>>(trigger) {
                            @Override
                            public void success(List<HuaweiIMasterVpcVO> returnValue) {
                                if (returnValue == null || returnValue.isEmpty()) {
                                    if (addSdnFlow) {
                                        trigger.fail(operr("there is no vpc on sdn controller [ip:%s]", self.getIp()));
                                    } else {
                                        SQL.New(HuaweiIMasterVpcVO.class)
                                                .eq(HuaweiIMasterVpcVO_.sdnControllerUuid, self.getUuid())
                                                .set(HuaweiIMasterVpcVO_.state, SdnControllerTableState.Disabled).update();
                                        trigger.next();
                                    }
                                    return;
                                }

                                List<String> vpcUuids = returnValue.stream().map(HuaweiIMasterVpcVO::getUuid).collect(Collectors.toList());
                                List<String> oldVpcUuids = Q.New(HuaweiIMasterVpcVO.class)
                                        .eq(HuaweiIMasterVpcVO_.sdnControllerUuid, self.getUuid())
                                        .select(HuaweiIMasterVpcVO_.uuid).listValues();

                                //disable not exised
                                SQL.New(HuaweiIMasterVpcVO.class)
                                        .eq(HuaweiIMasterVpcVO_.sdnControllerUuid, self.getUuid())
                                        .notIn(HuaweiIMasterVpcVO_.uuid, vpcUuids)
                                        .set(HuaweiIMasterVpcVO_.state, SdnControllerTableState.Disabled).update();

                                // update old
                                List<HuaweiIMasterVpcVO> oldVps = returnValue.stream().filter(
                                        vo -> oldVpcUuids.contains(vo.getUuid()))
                                        .collect(Collectors.toList());
                                for (HuaweiIMasterVpcVO vo: oldVps) {
                                    SQL.New(HuaweiIMasterVpcVO.class)
                                            .eq(HuaweiIMasterVpcVO_.uuid, vo.getUuid())
                                            .set(HuaweiIMasterVpcVO_.name, vo.getName())
                                            .set(HuaweiIMasterVpcVO_.description, vo.getDescription())
                                            .set(HuaweiIMasterVpcVO_.tenantId, vo.getTenantId())
                                            .set(HuaweiIMasterVpcVO_.fabricId, vo.getFabricId()).update();
                                }

                                //add new
                                vpcUuids.removeAll(oldVpcUuids);
                                if (vpcUuids.isEmpty()) {
                                    trigger.next();
                                    return;
                                }
                                List<HuaweiIMasterVpcVO> newVps = returnValue.stream().filter(
                                                vo -> vpcUuids.contains(vo.getUuid()))
                                        .collect(Collectors.toList());

                                for (HuaweiIMasterVpcVO vo: newVps) {
                                    vo.setAccountUuid(accountUuid);
                                    vo.setSdnControllerUuid(self.getUuid());
                                }

                                dbf.persistCollection(newVps);
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                boolean addSdnFlow = (boolean) data.get("addSdnFlow");
                if (addSdnFlow) {
                    SQL.New(HuaweiIMasterVpcVO.class)
                            .eq(HuaweiIMasterVpcVO_.sdnControllerUuid, self.getUuid()).delete();
                }

                trigger.rollback();
            }
        };
    }

    private NoRollbackFlow getHuaweiIMasterSystemInfoFlow() {
        return new NoRollbackFlow() {
            String __name__ = "get_huawei_imaster_system_info";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                getHuaweiIMasterSystemInfo(self, token, new ReturnValueCompletion<HuaweiIMasterNceFabricCommands.GetSystemInfoRsp>(trigger) {
                    @Override
                    public void success(HuaweiIMasterNceFabricCommands.GetSystemInfoRsp returnValue) {
                        if (returnValue != null) {
                            SQL.New(HuaweiIMasterSdnControllerVO.class)
                                    .eq(HuaweiIMasterSdnControllerVO_.uuid, self.getUuid())
                                    .set(HuaweiIMasterSdnControllerVO_.vendorVersion, returnValue.productVersion)
                                    .update();
                        }
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        };
    }

    private Flow getHuaweiIMasterTokenFlow() {
        return new Flow() {
            String __name__ = "get_huawei_imaster_token";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                getHuaweiIMasterToken(self, new ReturnValueCompletion<String>(trigger) {
                    @Override
                    public void success(String returnValue) {
                        data.put("token", returnValue);
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                String token = (String) data.get("token");
                deleteHuaweiIMasterToken(self, token, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.rollback();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.rollback();
                    }
                });
            }
        };
    }

    private NoRollbackFlow deleteHuaweiIMasterTokenFlow() {
        return new NoRollbackFlow() {
            String __name__ = "delete_huawei_imaster_token";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                deleteHuaweiIMasterToken(self, token, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        //ignore delete toke error
                        trigger.next();
                    }
                });
            }
        };
    }

    @Override
    @SdnControllerLog
    public void initSdnController(APIAddSdnControllerMsg msg, Completion completion) {
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("init-huawei-imaster-%s", self.getIp()));
        chain.getData().put("accountUuid", msg.getSession().getAccountUuid());
        chain.getData().put("addSdnFlow", false);
        chain.then(getHuaweiIMasterTokenFlow());
        chain.then(getHuaweiIMasterSystemInfoFlow());
        chain.then(getHuaweiIMasterFabricFlow());
        chain.then(getHuaweiIMasterTenantFlow());
        chain.then(getHuaweiIMasterSwitchFlow());
        chain.then(getHuaweiIMasterSwitchPortFlow());
        chain.then(getHuaweiIMasterVpcFlow());
        chain.then(deleteHuaweiIMasterTokenFlow());
        chain.done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.success();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).start();
    }

    @Override
    @SdnControllerLog
    public void postInitSdnController(SdnControllerVO vo, Completion completion) {
        completion.success();
    }

    @Override
    @SdnControllerLog
    public void preCreateVxlanNetwork(L2VxlanNetworkInventory vxlan, List<String> systemTags, Completion completion) {
        completion.success();
    }

    @Override
    @SdnControllerLog
    public void createL2Network(L2NetworkInventory inv, List<String> systemTags, Completion completion) {
        List<Flow> flows = new ArrayList<>();
        flows.add(new Flow() {
            String __name__ = "create-logical-switch";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                String vpcId = null;
                String tenantId = null;
                for (String tag : systemTags) {
                    if (HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID.isMatch(tag)) {
                        vpcId = HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID.getTokenByTag(
                                tag, HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID_TOKEN);
                    }

                    if (HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_TENANT_UUID.isMatch(tag)) {
                        tenantId = HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_TENANT_UUID.getTokenByTag(
                                tag, HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_TENANT_UUID_TOKEN);
                    }
                }

                createHuaweiIMasterControllerLogicalSwitch(self, token, inv, tenantId, vpcId, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                String token = (String) data.get("token");
                deleteHuaweiIMasterControllerLogicalSwitch(self, token, inv, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.rollback();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.rollback();
                    }
                });
            }
        });

        flows.add(new NoRollbackFlow() {
            String __name__ = "update-logical-switch-vni";
            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                getHuaweiIMasterControllerLogicalSwitch(self, token, inv.getUuid(), new ReturnValueCompletion<List<HuaweiLogicalSwitchStruct>>(trigger) {
                    @Override
                    public void success(List<HuaweiLogicalSwitchStruct> returnValue) {
                        if (returnValue == null) {
                            if (CoreGlobalProperty.UNIT_TEST_ON) {
                                trigger.next();
                                return;
                            }

                            trigger.fail(operr("failed to get logical switch for l2 network"));
                            return;
                        }

                        if (returnValue.size() != 1) {
                            trigger.fail(operr("get logical switch for l2 network failed with result: %s",
                                    JSONObjectUtil.toJsonString(returnValue)));
                            return;
                        }

                        if (!CoreGlobalProperty.UNIT_TEST_ON) {
                            HuaweiLogicalSwitchStruct struct = returnValue.get(0);
                            HardwareL2VxlanNetworkVO vo = dbf.findByUuid(inv.getUuid(), HardwareL2VxlanNetworkVO.class);
                            vo.setVni(struct.vni);
                            dbf.update(vo);
                        }
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(
                String.format("create-huawei-imaster-logical-switch"), flows, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });

        chain.start();
    }

    @Override
    @SdnControllerLog
    public void deleteL2Network(L2NetworkInventory vxlan, Completion completion) {
        List<Flow> flows = new ArrayList<>();
        flows.add(new NoRollbackFlow() {
            String __name__ = "delete-logical-switch";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                deleteHuaweiIMasterControllerLogicalSwitch(self, token, vxlan, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(
                String.format("delete-logical-switch"), flows, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });

        chain.start();
    }

    @Override
    @SdnControllerLog
    public void postCreateVxlanNetwork(L2VxlanNetworkInventory vxlan, List<String> systemTags, Completion completion) {
        completion.success();
    }

    @Override
    @SdnControllerLog
    public void preAttachL2NetworkToCluster(L2VxlanNetworkInventory vxlan, List<String> systemTags, Completion completion) {
        completion.success();
    }

    Flow getLogicalSwithcPortFlow() {
        return new NoRollbackFlow() {
            String __name__ = "get-logical-switch-port";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                L2VxlanNetworkInventory vxlan = (L2VxlanNetworkInventory) data.get("L2VxlanNetworkInventory");
                String token = (String) data.get("token");

                getHuaweiIMasterControllerLogicalSwitchPort(self, token, vxlan,
                        new ReturnValueCompletion<HuaweiLogicalSwitchPortStruct>(trigger) {
                            @Override
                            public void success(HuaweiLogicalSwitchPortStruct returnValue) {
                                List<HuaweiLogicalSwitchPortLocationStruct> curLocations = new ArrayList<>();
                                if (returnValue == null) {
                                    data.put("locations", curLocations);
                                    trigger.next();
                                    return;
                                }

                                if (returnValue.accessInfo == null || returnValue.accessInfo.location == null) {
                                    data.put("locations", curLocations);
                                    trigger.next();
                                    return;
                                }
                                for (HuaweiLogicalSwitchPortLocationStruct loc : returnValue.accessInfo.location) {
                                    // ignore all fields except: deviceId, portName
                                    loc.deviceGroupId = null;
                                    loc.deviceGroupName = null;
                                    loc.deviceName = null;
                                    loc.deviceId = null;
                                    loc.deviceIp = null;
                                    curLocations.add(loc);
                                }
                                data.put("locations", curLocations);
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }
        };
    }
    @Override
    @SdnControllerLog
    public void attachL2NetworkToCluster(L2VxlanNetworkInventory vxlan, List<String> clusterUuids, List<String> systemTags, Completion completion) {
        HardwareL2VxlanNetworkVO hardwareL2VO = dbf.findByUuid(vxlan.getUuid(), HardwareL2VxlanNetworkVO.class);
        HardwareL2VxlanNetworkInventory hardL2Inv = HardwareL2VxlanNetworkInventory.valueOf(hardwareL2VO);
        List<Flow> flows = new ArrayList<>();
        flows.add(getLogicalSwithcPortFlow());
        flows.add(new NoRollbackFlow() {
            String __name__ = "attach-logical-switch-to-cluster";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                List<HuaweiLogicalSwitchPortLocationStruct> curLocations =
                        (List<HuaweiLogicalSwitchPortLocationStruct>) data.get("locations");
                HttpMethod method = HttpMethod.PUT;
                if (curLocations == null) {
                    curLocations = new ArrayList<>();
                    method = HttpMethod.POST;
                }

                try {
                    Map<String, List<HuaweiLogicalSwitchPortLocationStruct>> newLocations =
                            helper.getPhysicalSwitchPortForClusters(hardL2Inv, clusterUuids);
                    if (newLocations.isEmpty()) {
                        logger.debug("there is no new physical switch port linked");
                        trigger.next();
                        return;
                    }

                    for (List<HuaweiLogicalSwitchPortLocationStruct> locs : newLocations.values()) {
                        for (HuaweiLogicalSwitchPortLocationStruct loc : locs) {
                            if (!curLocations.contains(loc)) {
                                curLocations.add(loc);
                            }
                        }
                    }
                } catch (CloudRuntimeException e) {
                    trigger.fail(operr("can not get physical switch port because: %s", e.getMessage()));
                    return;
                }

                HostVO hostVO = Q.New(HostVO.class).eq(HostVO_.clusterUuid, clusterUuids.get(0)).limit(1).find();
                updateHuaweiIMasterControllerLogicalSwitchPort(self, token, curLocations,
                        hardL2Inv,
                        HostInventory.valueOf(hostVO), method, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(String.format("attach-logical-switch-to-cluster"),
                flows, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });
        chain.getData().put("L2VxlanNetworkInventory", vxlan);

        chain.start();
    }

    @Override
    public void attachL2NetworkToHosts(L2VxlanNetworkInventory vxlan, List<HostInventory> hinvs, List<String> systemTags, Completion completion) {
        HardwareL2VxlanNetworkVO hardwareL2VO = dbf.findByUuid(vxlan.getUuid(), HardwareL2VxlanNetworkVO.class);
        HardwareL2VxlanNetworkInventory hardL2Inv = HardwareL2VxlanNetworkInventory.valueOf(hardwareL2VO);
        List<Flow> flows = new ArrayList<>();
        flows.add(getLogicalSwithcPortFlow());
        flows.add(new NoRollbackFlow() {
            String __name__ = "attach-logical-switch-to-hosts";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                List<HuaweiLogicalSwitchPortLocationStruct> curLocations =
                        (List<HuaweiLogicalSwitchPortLocationStruct>) data.get("locations");
                HttpMethod method = HttpMethod.PUT;
                if (curLocations == null) {
                    curLocations = new ArrayList<>();
                    method = HttpMethod.POST;
                }

                List<String> hostUuids = hinvs.stream().map(HostInventory::getUuid).collect(Collectors.toList());
                try {
                    Map<String, List<HuaweiLogicalSwitchPortLocationStruct>> newLocations =
                            helper.getPhysicalSwitchPortForHosts(hardL2Inv, hostUuids, false);
                    if (newLocations.isEmpty()) {
                        logger.debug("there is no new physical switch port linked");
                        trigger.next();
                        return;
                    }

                    for (List<HuaweiLogicalSwitchPortLocationStruct> locs : newLocations.values()) {
                        for (HuaweiLogicalSwitchPortLocationStruct loc : locs) {
                            if (!curLocations.contains(loc)) {
                                curLocations.add(loc);
                            }
                        }
                    }
                } catch (CloudRuntimeException e) {
                    trigger.fail(operr("can not get physical switch port because: %s", e.getMessage()));
                    return;
                }

                updateHuaweiIMasterControllerLogicalSwitchPort(self, token, curLocations,
                        hardL2Inv, hinvs.get(0), method, new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(String.format("attach-logical-switch-to-hosts"),
                flows, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });
        chain.getData().put("L2VxlanNetworkInventory", vxlan);

        chain.start();
    }

    @Override
    @SdnControllerLog
    public void detachL2NetworkFromCluster(L2VxlanNetworkInventory vxlan, List<String> clusterUuids, Completion completion) {
        List<Flow> flows = new ArrayList<>();
        HardwareL2VxlanNetworkVO hardwareL2VO = dbf.findByUuid(vxlan.getUuid(), HardwareL2VxlanNetworkVO.class);
        HardwareL2VxlanNetworkInventory hardL2Inv = HardwareL2VxlanNetworkInventory.valueOf(hardwareL2VO);
        flows.add(getLogicalSwithcPortFlow());
        flows.add(new NoRollbackFlow() {
            String __name__ = "detach-logical-switch-from-cluster";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                List<HuaweiLogicalSwitchPortLocationStruct> curLocations =
                        (List<HuaweiLogicalSwitchPortLocationStruct>) data.get("locations");
                HttpMethod method = HttpMethod.PUT;
                if (curLocations == null) {
                    curLocations = new ArrayList<>();
                    method = HttpMethod.DELETE;
                }

                try {
                    Map<String, List<HuaweiLogicalSwitchPortLocationStruct>> newLocations =
                            helper.getPhysicalSwitchPortForClusters(hardL2Inv, clusterUuids);
                    if (newLocations.isEmpty()) {
                        logger.debug("there is no new physical switch port linked");
                        trigger.next();
                        return;
                    }

                    for (List<HuaweiLogicalSwitchPortLocationStruct> locs : newLocations.values()) {
                        for (HuaweiLogicalSwitchPortLocationStruct loc : locs) {
                            if (curLocations.contains(loc)) {
                                curLocations.remove(loc);
                            }
                        }
                    }
                } catch (CloudRuntimeException e) {
                    logger.debug(String.format("can not get physical switch port because: %s", e.getMessage()));
                    trigger.next();
                    return;
                }

                if (curLocations.isEmpty()) {
                    method = HttpMethod.DELETE;
                }

                if (method == HttpMethod.DELETE) {
                    String huiweiUuid = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(vxlan.getUuid());
                    deleteHuaweiIMasterControllerLogicalSwitchPort(self, token, huiweiUuid, new Completion(trigger) {
                        @Override
                        public void success() {
                            trigger.next();
                        }

                        @Override
                        public void fail(ErrorCode errorCode) {
                            trigger.fail(errorCode);
                        }
                    });
                } else {
                    HostVO hostVO = Q.New(HostVO.class).eq(HostVO_.clusterUuid, clusterUuids.get(0)).limit(1).find();
                    updateHuaweiIMasterControllerLogicalSwitchPort(self, token, curLocations,
                            hardL2Inv,
                            HostInventory.valueOf(hostVO), method, new Completion(trigger) {
                                @Override
                                public void success() {
                                    trigger.next();
                                }

                                @Override
                                public void fail(ErrorCode errorCode) {
                                    trigger.fail(errorCode);
                                }
                            });
                }
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(
                String.format("detach-logical-switch"), flows, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });
        chain.getData().put("L2VxlanNetworkInventory", vxlan);

        chain.start();
    }

    @Override
    @SdnControllerLog
    public void postAttachL2NetworkToCluster(L2VxlanNetworkInventory vxlan, List<String> systemTags, Completion completion) {
        completion.success();
    }

    private void deleteSdnControllerDbResource(String controllerUuid) {
        SQL.New(HuaweiIMasterVpcVO.class).eq(HuaweiIMasterVpcVO_.sdnControllerUuid, controllerUuid).delete();
        SQL.New(HuaweiIMasterTenantVO.class).eq(HuaweiIMasterTenantVO_.sdnControllerUuid, controllerUuid).delete();
        SQL.New(HuaweiIMasterFabricVO.class).eq(HuaweiIMasterFabricVO_.sdnControllerUuid, controllerUuid).delete();
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.sdnControllerUuid, controllerUuid).delete();
        SQL.New(PhysicalSwitchVO.class).eq(PhysicalSwitchVO_.sdnControllerUuid, controllerUuid).delete();
    }

    @Override
    @SdnControllerLog
    public void deleteSdnController(SdnControllerDeletionMsg msg, SdnControllerInventory sdn, Completion completion) {
        List<String> vRouterUuids = Q.New(HuaweiIMasterVRouterVO.class)
                .eq(HuaweiIMasterVRouterVO_.sdnControllerUuid, msg.getSdnControllerUuid())
                .select(HuaweiIMasterVRouterVO_.uuid).listValues();

        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(String.format("delete-sdn-controller-%s", msg.getSdnControllerUuid()));
        if (!vRouterUuids.isEmpty()) {
            chain.then(getHuaweiIMasterTokenFlow());
        }
        chain.then(new NoRollbackFlow() {
            String __name__ = "delete-sdn-controller-logical-router";
            @Override
            public void run(FlowTrigger trigger, Map data) {
                if (vRouterUuids.isEmpty()) {
                    trigger.next();
                    return;
                }

                doDeleteHuaweiIMasterLogicalRouter(vRouterUuids, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });
        chain.then(deleteHuaweiIMasterTokenFlow());
        chain.done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                deleteSdnControllerDbResource(sdn.getUuid());
                completion.success();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        }).start();
    }

    @Override
    public List<SdnVniRange> getVniRange(SdnControllerInventory controller) {
        List<SdnVniRange> vniRanges = new ArrayList<>();
        List<Map<String, String>> tokens = SdnControllerSystemTags.VNI_RANGE.getTokensOfTagsByResourceUuid(
                controller.getUuid());
        for (Map<String, String> token : tokens) {
            SdnVniRange range = new SdnVniRange();
            for (Map.Entry<String, String> entry : token.entrySet()) {
                if (entry.getKey().equals(SdnControllerSystemTags.START_VNI_TOKEN)) {
                    range.startVni = Integer.parseInt(entry.getValue());
                }

                if (entry.getKey().equals(SdnControllerSystemTags.END_VNI_TOKEN)) {
                    range.endVni = Integer.parseInt(entry.getValue());
                }
            }
            vniRanges.add(range);
        }

        return vniRanges;
    }

    @Override
    public List<SdnVlanRange> getVlanRange(SdnControllerInventory controller) {
        List<SdnVlanRange> vlanRanges = new ArrayList<>();
        List<Map<String, String>> tokens = SdnControllerSystemTags.VLAN_RANGE.getTokensOfTagsByResourceUuid(
                controller.getUuid());
        for (Map<String, String> token : tokens) {
            SdnVlanRange range = new SdnVlanRange();
            for (Map.Entry<String, String> entry : token.entrySet()) {
                if (entry.getKey() == SdnControllerSystemTags.START_VLAN_TOKEN) {
                    range.startVlan = Integer.parseInt(entry.getValue());
                }

                if (entry.getKey() == SdnControllerSystemTags.END_VLAN_TOKEN) {
                    range.endVlan = Integer.parseInt(entry.getValue());
                }
            }
            vlanRanges.add(range);
        }

        return vlanRanges;
    }


    @Override
    public void createL3Network(L3NetworkInventory inv, List<String> systemTags, Completion completion) {
        String lRouterUuid = HuaweiIMasterHelper.getL3NetworkLogicalRouterId(systemTags);
        String logicalNetworkUuid = HuaweiIMasterHelper.getL2NetworkVpcId(inv.getL2NetworkUuid());

        List<Flow> flows = new ArrayList<>();
        flows.add(new NoRollbackFlow() {
            String __name__ = "create-logical-link";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                createHuaweiIMasterLogicalLink(self, token, lRouterUuid, logicalNetworkUuid, inv.getUuid(),
                        inv.getL2NetworkUuid(), new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(
                String.format("create-logical-link"), flows, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });

        chain.start();
    }

    @Override
    public void deleteL3Network(L3NetworkInventory inv, Completion completion) {
        List<Flow> flows = new ArrayList<>();
        flows.add(new NoRollbackFlow() {
            String __name__ = "delete-logical-link";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                deleteHuaweiIMasterLogicalLink(self, token, inv.getUuid(), new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(
                String.format("delete-logical-link"), flows, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });

        chain.start();
    }

    @Override
    public void createIpRange(IpRangeInventory inv, Completion completion) {
        List<Flow> flows = new ArrayList<>();
        flows.add(new Flow() {
            String __name__ = "create-subnet";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                L3NetworkVO l3Vo = dbf.findByUuid(inv.getL3NetworkUuid(), L3NetworkVO.class);
                createHuaweiIMasterSubnet(self, token, L3NetworkInventory.valueOf(l3Vo), inv, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }

            @Override
            public void rollback(FlowRollback trigger, Map data) {
                String token = (String) data.get("token");
                deleteHuaweiIMasterSubnet(self, token, inv, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.rollback();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.rollback();
                    }
                });
            }
        });

        flows.add(new NoRollbackFlow() {
            String __name__ = "update-logical-switch-subnet";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                L3NetworkVO l3vo = dbf.findByUuid(inv.getL3NetworkUuid(), L3NetworkVO.class);

                getHuaweiIMasterControllerLogicalSwitch(self, token, l3vo.getL2NetworkUuid(), new ReturnValueCompletion<List<HuaweiLogicalSwitchStruct>>(trigger) {
                    @Override
                    public void success(List<HuaweiLogicalSwitchStruct> returnValue) {
                        if (returnValue == null || returnValue.isEmpty()) {
                            trigger.fail(operr("get logical switch failed: uuid: %s", l3vo.getL2NetworkUuid()));
                            return;
                        }

                        HuaweiLogicalSwitchStruct logicalSwitch = new HuaweiLogicalSwitchStruct();
                        logicalSwitch.id = returnValue.get(0).id;
                        logicalSwitch.logicNetworkId = returnValue.get(0).logicNetworkId;
                        logicalSwitch.name = returnValue.get(0).name;
                        logicalSwitch.subnets = new ArrayList<>();
                        if (returnValue.get(0).subnets != null) {
                            for (String subnet : returnValue.get(0).subnets) {
                                if (!StringUtils.isEmpty(subnet)) {
                                    logicalSwitch.subnets.add(subnet);
                                }
                            }
                        }
                        String huaweiCidr = String.format("%s/%s", inv.getGateway(),
                                NetworkUtils.getPrefixLengthFromNetmask(inv.getNetmask()));
                        if (logicalSwitch.subnets.contains(huaweiCidr)) {
                            logger.debug(String.format("logical switch[%s] already contains subnet[%s]", logicalSwitch.name, inv.getNetworkCidr()));
                            trigger.next();
                            return;
                        }
                        logicalSwitch.subnets.add(huaweiCidr);
                        updateHuaweiIMasterControllerLogicalSwitch(self, token, logicalSwitch, new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                trigger.fail(errorCode);
                            }
                        });
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(
                String.format("create-subnet"), flows, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });

        chain.start();
    }

    @Override
    public void deleteIpRange(IpRangeInventory inv, Completion completion) {
        List<Flow> flows = new ArrayList<>();
        flows.add(new NoRollbackFlow() {
            String __name__ = "delete-subnet";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                deleteHuaweiIMasterSubnet(self, token, inv, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });

        FlowChain chain = getHuaweiIMasterFlowChain(
                String.format("delete-subnet"), flows, new Completion(completion) {
                    @Override
                    public void success() {
                        completion.success();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        completion.fail(errorCode);
                    }
                });

        chain.start();
    }


    private FlowChain getHuaweiIMasterFlowChain(String name, List<Flow> flows, Completion completion) {
        FlowChain chain = FlowChainBuilder.newSimpleFlowChain();
        chain.setName(name);
        chain.then(new NoRollbackFlow() {
            String __name__ = "get_huawei_imaster_token";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                getHuaweiIMasterToken(self, new ReturnValueCompletion<String>(trigger) {
                    @Override
                    public void success(String returnValue) {
                        data.put("token", returnValue);
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        });
        for (Flow flow : flows) {
            chain.then(flow);
        }
        chain.then(new NoRollbackFlow() {
            String __name__ = "delete_huawei_imaster_token";

            @Override
            public void run(FlowTrigger trigger, Map data) {
                String token = (String) data.get("token");
                deleteHuaweiIMasterToken(self, token, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        //ignore delete toke error
                        trigger.next();
                    }
                });
            }
        }).done(new FlowDoneHandler(completion) {
            @Override
            public void handle(Map data) {
                completion.success();
            }
        }).error(new FlowErrorHandler(completion) {
            @Override
            public void handle(ErrorCode errCode, Map data) {
                completion.fail(errCode);
            }
        });

        return chain;
    }
}