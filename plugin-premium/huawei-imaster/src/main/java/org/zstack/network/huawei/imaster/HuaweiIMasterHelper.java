package org.zstack.network.huawei.imaster;

import com.aliyuncs.utils.StringUtils;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.compute.bonding.HostNetworkBondingConstant;
import org.zstack.core.db.Q;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.host.HostVO;
import org.zstack.header.host.HostVO_;
import org.zstack.header.network.l2.L2NetworkInventory;
import org.zstack.header.network.l3.L3NetworkVO;
import org.zstack.header.network.l3.L3NetworkVO_;
import org.zstack.sdnController.header.HardwareL2VxlanNetworkPoolVO;
import org.zstack.sdnController.header.HardwareL2VxlanNetworkPoolVO_;
import org.zstack.network.hostNetworkInterface.*;
import org.zstack.network.l3.L3NetworkSystemTags;
import org.zstack.sdnController.SdnControllerSystemTags;
import org.zstack.sdnController.header.*;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;
import org.zstack.network.huawei.imaster.HuaweiIMasterNceFabricCommands.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Huawei iMaster Helper Class
 */
@Configurable(preConstruction = true, autowire = org.springframework.beans.factory.annotation.Autowire.BY_TYPE)
public class HuaweiIMasterHelper {

    private static final CLogger logger = Utils.getLogger((HuaweiIMasterHelper.class));

    public static String getDescription(String uuid) {
        return "created by ZStack with uuid:" + uuid;
    }

    public static String zStackUuidToHuaweiIMasterId(String uuid) {
        if (uuid == null || uuid.length() < 32) {
            return uuid;
        }
        return uuid.substring(0, 8) + "-" + uuid.substring(8, 12) + "-"
                + uuid.substring(12, 16) + "-" + uuid.substring(16, 20) + "-"
                + uuid.substring(20);
    }

    public static String huaweiIMasterIdToZStackUuid(String id) {
        return id.replaceAll("-", "");
    }

    public static String getL2NetworkTenantId(String l2Uuid) {
        String tenantUuid = HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_TENANT_UUID.getTokenByResourceUuid(l2Uuid,
                HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_TENANT_UUID_TOKEN);
        if (tenantUuid == null || tenantUuid.isEmpty()) {
            return null;
        }

        return zStackUuidToHuaweiIMasterId(tenantUuid);
    }

    public static String getL2NetworkVpcId(String l2Uuid) {
        String vpcUuid = HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID.getTokenByResourceUuid(l2Uuid,
                HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID_TOKEN);
        if (vpcUuid == null || vpcUuid.isEmpty()) {
            return null;
        }

        return zStackUuidToHuaweiIMasterId(vpcUuid);
    }

    public static String getL2NetworkFabricId(String l2Uuid) {
        String vpcUuid = HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID.getTokenByResourceUuid(l2Uuid,
                HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID_TOKEN);
        if (vpcUuid == null || vpcUuid.isEmpty()) {
            return null;
        }

        String fabricId = Q.New(HuaweiIMasterVpcVO.class)
                .select(HuaweiIMasterVpcVO_.fabricId)
                .eq(HuaweiIMasterVpcVO_.uuid, vpcUuid).findValue();
        if (fabricId == null) {
            return null;
        }

        return zStackUuidToHuaweiIMasterId(fabricId);
    }

    public static String getL3NetworkVpcId(String l3Uuid) {
        String l2Uuid = Q.New(L3NetworkVO.class).select(L3NetworkVO_.l2NetworkUuid)
                .eq(L3NetworkVO_.uuid, l3Uuid).findValue();
        String vpcUuid = HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID.getTokenByResourceUuid(l2Uuid,
                HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID_TOKEN);
        if (vpcUuid == null || vpcUuid.isEmpty()) {
            return null;
        }

        return zStackUuidToHuaweiIMasterId(vpcUuid);
    }

    public static String getL3NetworkTenantId(String l3Uuid) {
        String l2Uuid = Q.New(L3NetworkVO.class).select(L3NetworkVO_.l2NetworkUuid)
                .eq(L3NetworkVO_.uuid, l3Uuid).findValue();
        String tenantUuid = HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_TENANT_UUID.getTokenByResourceUuid(l2Uuid,
                HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_TENANT_UUID_TOKEN);
        if (tenantUuid == null || tenantUuid.isEmpty()) {
            return null;
        }

        return zStackUuidToHuaweiIMasterId(tenantUuid);
    }

    public static String getL3NetworkLogicalRouterId(List<String> systemTags) {
        String lRouterUuid = null;
        for (String tag : systemTags) {
            if (L3NetworkSystemTags.L3_NETWORK_HUAWEI_LOGICAL_ROUTER.isMatch(tag)) {
                lRouterUuid = L3NetworkSystemTags.L3_NETWORK_HUAWEI_LOGICAL_ROUTER.getTokenByTag(tag,
                        L3NetworkSystemTags.L3_NETWORK_HUAWEI_LOGICAL_ROUTER_TOKEN);
            }
        }

        if (lRouterUuid == null || lRouterUuid.isEmpty()) {
            return null;
        }

        return zStackUuidToHuaweiIMasterId(lRouterUuid);
    }

    public static String getL3NetworkLogicalRouterId(String l3Uuid) {
        String lRouterUuid = L3NetworkSystemTags.L3_NETWORK_HUAWEI_LOGICAL_ROUTER.getTokenByResourceUuid(
                l3Uuid, L3NetworkSystemTags.L3_NETWORK_HUAWEI_LOGICAL_ROUTER_TOKEN);
        if (lRouterUuid == null || lRouterUuid.isEmpty()) {
            return null;
        }

        return zStackUuidToHuaweiIMasterId(lRouterUuid);
    }

    public static String getHuaweiIpv6AddressMode(String addressMode) {
        if (addressMode == null || addressMode.isEmpty()) {
            return null;
        }

        return "slaac";
        /*
        switch (addressMode) {
            case IPv6Constants.SLAAC:
                return "slaac";
            case IPv6Constants.Stateless_DHCP:
                return "stateless";
            case IPv6Constants.Stateful_DHCP:
                return "stateful";
            default:
                return "";
        }*/
    }

    public Map<String, List<HuaweiLogicalSwitchPortLocationStruct>> getPhysicalSwitchPortForClusters(L2NetworkInventory l2Inv,
                                                                                                     List<String> clusterUuids)
            throws CloudRuntimeException {
        Map<String, List<HuaweiLogicalSwitchPortLocationStruct>> locations = new HashMap<>();
        List<String> hostUuids = Q.New(HostVO.class).select(HostVO_.uuid)
                .in(HostVO_.clusterUuid, clusterUuids).listValues();
        if (hostUuids.isEmpty()) {
            logger.debug("there is no location because no host in cluster");
            return locations;
        }

        return getPhysicalSwitchPortForHosts(l2Inv, hostUuids, false);
    }

    public Map<String, List<HuaweiLogicalSwitchPortLocationStruct>> getPhysicalSwitchPortForHosts(L2NetworkInventory l2Inv,
                                                                                               List<String> hostUuids, boolean noException)
            throws CloudRuntimeException {
        Map<String, List<HuaweiLogicalSwitchPortLocationStruct>> locations = new HashMap<>();

        HardwareL2VxlanNetworkPoolVO poolVO;
        if (l2Inv.getType().equals(SdnControllerConstant.HARDWARE_VXLAN_NETWORK_TYPE)) {
            HardwareL2VxlanNetworkInventory inv = (HardwareL2VxlanNetworkInventory) l2Inv;
            poolVO = Q.New(HardwareL2VxlanNetworkPoolVO.class).eq(HardwareL2VxlanNetworkPoolVO_.uuid, inv.getPoolUuid()).find();
        } else if (l2Inv.getType().equals(SdnControllerConstant.HARDWARE_VXLAN_NETWORK_POOL_TYPE)) {
            poolVO = Q.New(HardwareL2VxlanNetworkPoolVO.class).eq(HardwareL2VxlanNetworkPoolVO_.uuid, l2Inv.getUuid()).find();
        } else {
            logger.debug(String.format("there is no location for l2 networkType {%s}", l2Inv.getType()));
            return locations;
        }

        List<HostNetworkBondingVO> bondvos = Q.New(HostNetworkBondingVO.class)
                .in(HostNetworkBondingVO_.hostUuid, hostUuids)
                .eq(HostNetworkBondingVO_.type, HostNetworkBondingConstant.LINUX_BONDING_TYPE)
                .eq(HostNetworkBondingVO_.bondingName, poolVO.getPhysicalInterface()).list();
        if (!bondvos.isEmpty()) {
            for (HostNetworkBondingVO vo : bondvos) {
                List<String> interfaceUuids = vo.getSlaves().stream().map(HostNetworkInterfaceVO::getUuid).collect(Collectors.toList());
                if (interfaceUuids.isEmpty()) {
                    if (noException) {
                        continue;
                    } else {
                        throw new CloudRuntimeException(String.format(
                                "bond [name:%s] of host [uuid:%s] has no members",
                                poolVO.getPhysicalInterface(), vo.getHostUuid()));
                    }
                }

                List<PhysicalSwitchPortVO> pports = Q.New(PhysicalSwitchPortVO.class)
                        .in(PhysicalSwitchPortVO_.peerInterfaceUuid, interfaceUuids).list();
                List<String> pportsInterfaceUuids = pports.stream().map(PhysicalSwitchPortVO::getPeerInterfaceUuid)
                        .collect(Collectors.toList());
                interfaceUuids.removeIf(pportsInterfaceUuids::contains);
                if (!interfaceUuids.isEmpty()) {
                    if (noException) {
                        continue;
                    } else {
                        throw new CloudRuntimeException(String.format(
                                "bond interface[uuid:%s] is not attached physical switch port",
                                interfaceUuids));
                    }
                }

                locations.put(vo.getHostUuid(), new ArrayList<>());
                if (vo.getMode().equals(HostNetworkBondingConstant.BONDING_MODE_LACP)) {
                    List<String> trunkNames = pports.stream().map(PhysicalSwitchPortVO::getEthTrunkName)
                            .filter(n -> n != null && !n.isEmpty()).distinct()
                            .collect(Collectors.toList());
                    if (trunkNames.size() != 1) {
                        if (noException) {
                            continue;
                        } else {
                            throw new CloudRuntimeException(String.format(
                                    "bond[name:%s] of host [uuid:%s] is not link to same switch trunk port [%s]",
                                    l2Inv.getPhysicalInterface(), vo.getHostUuid(), trunkNames));
                        }
                    }

                    for (PhysicalSwitchPortVO pvo : pports) {
                        if (StringUtils.isEmpty(pvo.getEthTrunkName())) {
                            if (!noException) {
                                throw new CloudRuntimeException(String.format(
                                        "bond[name:%s] of host [uuid:%s] is not linked to eth trunk port: %s",
                                        l2Inv.getPhysicalInterface(), vo.getHostUuid(), pvo.getName()));
                            }
                        }
                    }

                    List<String> deviceIds = pports.stream().map(PhysicalSwitchPortVO::getSwitchUuid).distinct()
                            .collect(Collectors.toList());
                    logger.debug(String.format("    lacp bond [name:{%s}] on host [uuid:{%s}] is linked to switch [uuid:{%s}]",
                            vo.getBondingName(), vo.getHostUuid(), deviceIds));
                    for (String deviceId : deviceIds) {
                        HuaweiLogicalSwitchPortLocationStruct struct = new HuaweiLogicalSwitchPortLocationStruct();
                        struct.deviceId = zStackUuidToHuaweiIMasterId(deviceId);
                        struct.portName = trunkNames.get(0);
                        locations.get(vo.getHostUuid()).add(struct);
                    }
                } else {
                    logger.debug(String.format("    ab bond [name:{%s}] on host [uuid:{%s}] is linked to switch [uuid:{%s}]",
                            vo.getBondingName(), vo.getHostUuid(), JSONObjectUtil.toJsonString(pports)));
                    for (PhysicalSwitchPortVO pvo : pports) {
                        HuaweiLogicalSwitchPortLocationStruct struct = new HuaweiLogicalSwitchPortLocationStruct();
                        struct.deviceId = zStackUuidToHuaweiIMasterId(pvo.getSwitchUuid());
                        struct.portName = pvo.getName();
                        locations.get(vo.getHostUuid()).add(struct);
                    }
                }
            }
        }

        List<HostNetworkInterfaceVO> interfaceVOS = Q.New(HostNetworkInterfaceVO.class)
                .in(HostNetworkInterfaceVO_.hostUuid, hostUuids)
                .eq(HostNetworkInterfaceVO_.interfaceName, poolVO.getPhysicalInterface()).list();
        List<String> interfaceUuids = interfaceVOS.stream().map(HostNetworkInterfaceVO::getUuid).collect(Collectors.toList());
        if (!interfaceUuids.isEmpty()) {
            Map<String, String> interfaceHostMap = new HashMap<>();
            for (HostNetworkInterfaceVO vo : interfaceVOS) {
                interfaceHostMap.put(vo.getUuid(), vo.getHostUuid());
            }

            logger.debug(String.format("find physical switch port for interfaces [uuid:{%s}]", interfaceUuids));

            List<PhysicalSwitchPortVO> pports = Q.New(PhysicalSwitchPortVO.class)
                    .in(PhysicalSwitchPortVO_.peerInterfaceUuid, interfaceUuids).list();
            List<String> pportsInterfaceUuids = pports.stream().map(PhysicalSwitchPortVO::getPeerInterfaceUuid)
                    .collect(Collectors.toList());
            interfaceUuids.removeIf(pportsInterfaceUuids::contains);
            if (!noException) {
                if (!interfaceUuids.isEmpty()) {
                    throw new CloudRuntimeException(String.format(
                            "interface[uuid:%s] is not attached physical switch port", interfaceUuids));
                }
            }

            logger.debug(String.format("    interfaces are linked to physical switch port [{%s}]",
                    JSONObjectUtil.toJsonString(pports)));
            for (PhysicalSwitchPortVO port : pports) {
                if (!StringUtils.isEmpty(port.getEthTrunkName())) {
                    if (noException) {
                        continue;
                    } else {
                        throw new CloudRuntimeException(String.format(
                                "physical switch port [%s] is a eth trunk: %s", port.getName(), port.getEthTrunkName()));
                    }
                }

                HuaweiLogicalSwitchPortLocationStruct struct = new HuaweiLogicalSwitchPortLocationStruct();
                struct.deviceId = zStackUuidToHuaweiIMasterId(port.getSwitchUuid());
                struct.portName = port.getName();
                locations.put(interfaceHostMap.get(port.getPeerInterfaceUuid()), new ArrayList<>());
                locations.get(interfaceHostMap.get(port.getPeerInterfaceUuid())).add(struct);
            }
        }

        return locations;
    }

    public static String getLogicalSwitchPortDescription(String l2Name, String hostUuid) {
        return String.format("%s for host: %s", l2Name, hostUuid);
    }

    public static String getHostUuidFromLogicalSwitchPortDescription(String des) {
        String[] res = des.split(":");
        if (res.length != 2) {
            return null;
        } else {
            return res[1];
        }
    }

    public static List<SdnVlanRange> getSdnVlanRange(String sdnControllerUuid) {
        List<SdnVlanRange>  vlanRanges = new ArrayList<>();
        List<Map<String, String>> vlanRangesToken = SdnControllerSystemTags.VLAN_RANGE.getTokensOfTagsByResourceUuid(sdnControllerUuid);
        for (Map<String, String> token : vlanRangesToken) {
            SdnVlanRange range = new SdnVlanRange();
            range.startVlan = Integer.parseInt(token.get(SdnControllerSystemTags.START_VLAN_TOKEN));
            range.endVlan = Integer.parseInt(token.get(SdnControllerSystemTags.END_VLAN_TOKEN));
            vlanRanges.add(range);
        }
        return vlanRanges;
    }
}
