package org.zstack.network.huawei.imaster;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cascade.AbstractAsyncCascadeExtension;
import org.zstack.core.cascade.CascadeAction;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.header.core.Completion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.message.MessageReply;
import org.zstack.header.network.l2.L2NetworkVO;
import org.zstack.header.network.sdncontroller.SdnControllerInventory;
import org.zstack.header.network.sdncontroller.SdnControllerVO;
import org.zstack.utils.CollectionUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.function.Function;
import org.zstack.utils.logging.CLogger;

import java.util.Arrays;
import java.util.List;

/**
 * Cascade extension for Huawei iMaster Tenant
 */
public class HuaweiIMasterTenantCascadeExtension extends AbstractAsyncCascadeExtension {
    private static final CLogger logger = Utils.getLogger(HuaweiIMasterTenantCascadeExtension.class);
    private static final String NAME = HuaweiIMasterTenantVO.class.getSimpleName();

    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private CloudBus bus;

    @Override
    public void asyncCascade(CascadeAction action, Completion completion) {
        if (action.isActionCode(CascadeConstant.DELETION_CHECK_CODE)) {
            handleDeletionCheck(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_DELETE_CODE, CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
            handleDeletion(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_CLEANUP_CODE)) {
            handleDeletionCleanup(action, completion);
        } else {
            completion.success();
        }
    }

    private void handleDeletionCleanup(CascadeAction action, Completion completion) {
        dbf.eoCleanup(HuaweiIMasterTenantVO.class);
        completion.success();
    }

    private void handleDeletion(CascadeAction action, final Completion completion) {
        final List<HuaweiIMasterTenantInventory> tenantInvs = tenantFromAction(action);
        if (tenantInvs == null || tenantInvs.isEmpty()) {
            completion.success();
            return;
        }

        List<HuaweiIMasterTenantDeletionMsg> msgs = CollectionUtils.transformToList(tenantInvs,
                new Function<HuaweiIMasterTenantDeletionMsg, HuaweiIMasterTenantInventory>() {
            @Override
            public HuaweiIMasterTenantDeletionMsg call(HuaweiIMasterTenantInventory arg) {
                HuaweiIMasterTenantDeletionMsg msg = new HuaweiIMasterTenantDeletionMsg();
                msg.setTenantUuid(arg.getUuid());
                msg.setSdnControllerUuid(arg.getSdnControllerUuid());
                bus.makeTargetServiceIdByResourceUuid(msg, HuaweiIMasterConstant.SERVICE_ID, arg.getUuid());
                return msg;
            }
        });

        new While<>(msgs).each((msg, compl) -> {
            bus.send(msg, new CloudBusCallBack(compl) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        logger.warn(String.format("failed to delete Huawei iMaster tenant[uuid:%s], %s",
                                msg.getTenantUuid(), reply.getError()));
                    }
                    compl.done();
                }
            });
        }).run(new WhileDoneCompletion(completion) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                completion.success();
            }
        });
    }

    private void handleDeletionCheck(CascadeAction action, Completion completion) {
        completion.success();
    }

    @Override
    public List<String> getEdgeNames() {
        return Arrays.asList(
                SdnControllerVO.class.getSimpleName(),
                L2NetworkVO.class.getSimpleName()
        );
    }

    @Override
    public String getCascadeResourceName() {
        return NAME;
    }

    @Override
    public CascadeAction createActionForChildResource(CascadeAction action) {
        if (CascadeConstant.DELETION_CODES.contains(action.getActionCode())) {
            List<HuaweiIMasterTenantInventory> tenants = tenantFromAction(action);
            if (tenants != null) {
                return action.copy().setParentIssuer(NAME).setParentIssuerContext(tenants);
            }
        }
        return null;
    }

    private List<HuaweiIMasterTenantInventory> tenantFromAction(CascadeAction action) {
        if (SdnControllerVO.class.getSimpleName().equals(action.getParentIssuer())) {
            List<String> controllerUuids = CollectionUtils.transformToList((List<SdnControllerInventory>) action.getParentIssuerContext(), new Function<String, SdnControllerInventory>() {
                @Override
                public String call(SdnControllerInventory arg) {
                    return arg.getUuid();
                }
            });
            if (controllerUuids.isEmpty()) {
                return null;
            }

            List<HuaweiIMasterTenantVO> tenantVOs = Q.New(HuaweiIMasterTenantVO.class).in(HuaweiIMasterTenantVO_.sdnControllerUuid, controllerUuids).list();
            return HuaweiIMasterTenantInventory.valueOf(tenantVOs);
        } else if (HuaweiIMasterTenantVO.class.getSimpleName().equals(action.getParentIssuer())) {
            return action.getParentIssuerContext();
        }
        return null;
    }
}
