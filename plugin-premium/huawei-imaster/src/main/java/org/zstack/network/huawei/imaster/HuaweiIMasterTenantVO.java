package org.zstack.network.huawei.imaster;

import org.zstack.header.identity.OwnedByAccount;
import org.zstack.header.tag.AutoDeleteTag;
import org.zstack.header.vo.ForeignKey;
import org.zstack.header.vo.NoView;
import org.zstack.header.vo.ResourceVO;
import org.zstack.header.vo.ToInventory;
import org.zstack.header.network.sdncontroller.SdnControllerVO;
import org.zstack.sdnController.header.SdnControllerTableState;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.HashSet;
import java.util.Set;

/**
 * Huawei iMaster Tenant Value Object
 */
@Entity
@Table
@AutoDeleteTag
public class HuaweiIMasterTenantVO extends ResourceVO implements ToInventory, OwnedByAccount {

    @Column
    @ForeignKey(parentEntityClass = SdnControllerVO.class, parentKey = "uuid", onDeleteAction = ForeignKey.ReferenceOption.CASCADE)
    private String sdnControllerUuid;

    @Column
    private String name;
    
    @Column
    private String description;

    @OneToMany(fetch = FetchType.EAGER)
    @JoinColumn(name = "tenantUuid", insertable = false, updatable = false)
    @NoView
    private Set<HuaweiIMasterTenantFabricRefVO> fabricIds = new HashSet<>();

    @Column
    @Enumerated(EnumType.STRING)
    private SdnControllerTableState state;

    @Column
    private Timestamp createDate;

    @Column
    private Timestamp lastOpDate;
    
    @Transient
    private String accountUuid;
    
    @PreUpdate
    private void preUpdate() {
        lastOpDate = null;
    }

    public HuaweiIMasterTenantVO() {
    }

    public HuaweiIMasterTenantVO(HuaweiIMasterTenantVO other) {
        this.uuid = other.getUuid();
        this.sdnControllerUuid = other.getSdnControllerUuid();
        this.name = other.getName();
        this.description = other.getDescription();
        this.state = other.getState();
    }

    public String getSdnControllerUuid() {
        return sdnControllerUuid;
    }

    public void setSdnControllerUuid(String sdnControllerUuid) {
        this.sdnControllerUuid = sdnControllerUuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Timestamp getLastOpDate() {
        return lastOpDate;
    }

    public void setLastOpDate(Timestamp lastOpDate) {
        this.lastOpDate = lastOpDate;
    }

    public Set<HuaweiIMasterTenantFabricRefVO> getFabricIds() {
        return fabricIds;
    }

    public void setFabricIds(Set<HuaweiIMasterTenantFabricRefVO> fabricIds) {
        this.fabricIds = fabricIds;
    }

    public SdnControllerTableState getState() {
        return state;
    }

    public void setState(SdnControllerTableState state) {
        this.state = state;
    }

    @Override
    public String getAccountUuid() {
        return accountUuid;
    }

    @Override
    public void setAccountUuid(String accountUuid) {
        this.accountUuid = accountUuid;
    }
}
