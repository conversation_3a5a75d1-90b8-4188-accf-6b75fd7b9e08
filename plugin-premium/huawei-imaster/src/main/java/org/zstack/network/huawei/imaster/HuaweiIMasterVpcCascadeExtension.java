package org.zstack.network.huawei.imaster;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cascade.AbstractAsyncCascadeExtension;
import org.zstack.core.cascade.CascadeAction;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.header.core.Completion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.message.MessageReply;
import org.zstack.utils.CollectionUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.function.Function;
import org.zstack.utils.logging.CLogger;

import java.util.Arrays;
import java.util.List;

/**
 * Cascade extension for Huawei iMaster VPC
 */
public class HuaweiIMasterVpcCascadeExtension extends AbstractAsyncCascadeExtension {
    private static final CLogger logger = Utils.getLogger(HuaweiIMasterVpcCascadeExtension.class);
    private static final String NAME = HuaweiIMasterVpcVO.class.getSimpleName();

    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private CloudBus bus;

    @Override
    public void asyncCascade(CascadeAction action, Completion completion) {
        if (action.isActionCode(CascadeConstant.DELETION_CHECK_CODE)) {
            handleDeletionCheck(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_DELETE_CODE, CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
            handleDeletion(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_CLEANUP_CODE)) {
            handleDeletionCleanup(action, completion);
        } else {
            completion.success();
        }
    }

    private void handleDeletionCleanup(CascadeAction action, Completion completion) {
        dbf.eoCleanup(HuaweiIMasterVpcVO.class);
        completion.success();
    }

    private void handleDeletion(CascadeAction action, final Completion completion) {
        final List<HuaweiIMasterVpcInventory> vpcInvs = vpcFromAction(action);
        if (vpcInvs == null || vpcInvs.isEmpty()) {
            completion.success();
            return;
        }

        List<HuaweiIMasterVpcDeletionMsg> msgs = CollectionUtils.transformToList(vpcInvs,
                new Function<HuaweiIMasterVpcDeletionMsg, HuaweiIMasterVpcInventory>() {
            @Override
            public HuaweiIMasterVpcDeletionMsg call(HuaweiIMasterVpcInventory arg) {
                HuaweiIMasterVpcDeletionMsg msg = new HuaweiIMasterVpcDeletionMsg();
                msg.setVpcUuid(arg.getUuid());
                msg.setSdnControllerUuid(arg.getSdnControllerUuid());
                bus.makeTargetServiceIdByResourceUuid(msg, HuaweiIMasterConstant.SERVICE_ID, arg.getUuid());
                return msg;
            }
        });

        new While<>(msgs).each((msg, compl) -> {
            bus.send(msg, new CloudBusCallBack(compl) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        logger.warn(String.format("failed to delete Huawei iMaster vpc[uuid:%s], %s",
                                msg.getVpcUuid(), reply.getError()));
                    }
                    compl.done();
                }
            });
        }).run(new WhileDoneCompletion(completion) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                completion.success();
            }
        });
    }

    private void handleDeletionCheck(CascadeAction action, Completion completion) {
        completion.success();
    }

    @Override
    public List<String> getEdgeNames() {
        return Arrays.asList(
                HuaweiIMasterTenantVO.class.getSimpleName()
        );
    }

    @Override
    public String getCascadeResourceName() {
        return NAME;
    }

    @Override
    public CascadeAction createActionForChildResource(CascadeAction action) {
        if (CascadeConstant.DELETION_CODES.contains(action.getActionCode())) {
            List<HuaweiIMasterVpcInventory> vpcs = vpcFromAction(action);
            if (vpcs != null) {
                return action.copy().setParentIssuer(NAME).setParentIssuerContext(vpcs);
            }
        }
        return null;
    }

    private List<HuaweiIMasterVpcInventory> vpcFromAction(CascadeAction action) {
        if (HuaweiIMasterTenantVO.class.getSimpleName().equals(action.getParentIssuer())) {
            List<String> tenantUuids = CollectionUtils.transformToList((List<HuaweiIMasterTenantInventory>) action.getParentIssuerContext(), new Function<String, HuaweiIMasterTenantInventory>() {
                @Override
                public String call(HuaweiIMasterTenantInventory arg) {
                    return arg.getUuid();
                }
            });
            if (tenantUuids.isEmpty()) {
                return null;
            }

            List<HuaweiIMasterVpcVO> vpcVOs = Q.New(HuaweiIMasterVpcVO.class).in(HuaweiIMasterVpcVO_.tenantId, tenantUuids).list();
            return HuaweiIMasterVpcInventory.valueOf(vpcVOs);
        } else if (HuaweiIMasterVpcVO.class.getSimpleName().equals(action.getParentIssuer())) {
            return action.getParentIssuerContext();
        }
        return null;
    }
}
