package org.zstack.network.huawei.imaster;

import com.google.gson.annotations.SerializedName;
import org.apache.commons.lang.StringUtils;
import org.zstack.core.db.Q;
import org.zstack.network.hostNetworkInterface.PhysicalSwitchPortInventory;
import org.zstack.network.hostNetworkInterface.PhysicalSwitchPortVO;
import org.zstack.network.hostNetworkInterface.PhysicalSwitchVO;
import org.zstack.network.hostNetworkInterface.PhysicalSwitchVO_;
import org.zstack.sdnController.header.SdnControllerTableState;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class HuaweiIMasterNceFabricCommands {
    private static final CLogger logger = Utils.getLogger(HuaweiIMasterNceFabricCommands.class);

    public static final String HUAWEI_IMASTER_TOKEN_PATH = "/controller/v2/tokens";
    public static final String HUAWEI_IMASTER_TENANT_PATH = "/controller/dc/v3/tenants";
    public static final String HUAWEI_IMASTER_FABRIC_PATH = "/controller/dc/v3/physicalnetwork/fabricresource/fabrics";
    public static final String HUAWEI_IMASTER_SWITCH_PATH = "/acdcn/v3/topoapi/dcntopo/device";
    public static final String HUAWEI_IMASTER_SWITCH_PORT_PATH = "/acdcn/v3/topoapi/dcntopo/getPorts";
    public static final String HUAWEI_IMASTER_VPC_PATH = "/controller/dc/v3/logicnetwork/networks";
    public static final String HUAWEI_IMASTER_LOGICAL_SWITCHES_PATH = "/controller/dc/v3/logicnetwork/switchs";
    public static final String HUAWEI_IMASTER_LOGICAL_SWITCH_PATH = "/controller/dc/v3/logicnetwork/switchs/switch/.*";
    public static final String HUAWEI_IMASTER_LOGICAL_PORTS_PATH = "/controller/dc/v3/logicnetwork/ports";
    public static final String HUAWEI_IMASTER_LOGICAL_PORT_PATH = "/controller/dc/v3/logicnetwork/ports/port/.*";
    public static final String HUAWEI_IMASTER_LOGICAL_ROUTERS_PATH = "/controller/dc/v3/logicnetwork/routers";
    public static final String HUAWEI_IMASTER_LOGICAL_ROUTER_PATH = "/controller/dc/v3/logicnetwork/routers/router/.*";
    public static final String HUAWEI_IMASTER_LOGICAL_LINKS_PATH = "/controller/dc/v3/logicnetwork/links";
    public static final String HUAWEI_IMASTER_LOGICAL_LINK_PATH = "/controller/dc/v3/logicnetwork/links/link/.*";
    public static final String HUAWEI_IMASTER_SUBNETS_PATH = "/controller/dc/v3/logicnetwork/subnets";
    public static final String HUAWEI_IMASTER_SUBNET_PATH = "/controller/dc/v3/logicnetwork/subnets/subnet/.*";
    public static final String HUAWEI_IMASTER_SYSTEM_INFO_PATH = "/restconf/operational/ietf-system:system-state";

    private static final Pattern physSwitchPortNameFilter = Pattern.compile(
            HuaweiIMasterConstant.HUAWEI_IMASTER_SWITCH_PORT_NAME_FILTER, Pattern.CASE_INSENSITIVE);

    public static String getHuaweiIMasterResourcePath(String path, String id) {
        if (path.endsWith(".*")) {
            return path.replace(".*", id);
        } else {
            return path + "/" + id;
        }
    }

    public static class HuaweiSdnAdditional {
        public String producer;
        public String createAt;
        public String updateAt;

        public HuaweiSdnAdditional() {
            this.producer = HuaweiIMasterConstant.HUAWEI_IMASTER_PRODUCER;
        }

        public boolean isCreatedByZStack() {
            return this.producer.startsWith(HuaweiIMasterConstant.HUAWEI_IMASTER_PRODUCER);
        }
    }

    public static class HuaweiSdnCmd {
        public String toString() {
            return JSONObjectUtil.toJsonString(this);
        }
    }

    public static class HuaweiSdnRspAdditional {
        public String producer;
        public String createAt;
        public String updateAt;
    }

    public static class HuaweiSdnRsp {
    }

    public static class LoginCmd {
        public String userName;
        public String password;
    }

    public static class GetTokenCmd extends HuaweiSdnCmd {
        public String userName;
        public String password;
        public LoginCmd login;
    }

    public static class LoginStruct {
        public String token_id;
    }

    public static class LoginRsp extends HuaweiSdnRsp {
        public LoginStruct data;
    }

    public static class DeleteTokenCmd extends HuaweiSdnCmd {
        public String token;
    }

    public static class DeleteTokenRsp extends HuaweiSdnRsp {
    }

    public static class GetTenantCmd extends HuaweiSdnCmd {
    }

    public static class TenantResPoolStruct{
        List<String> externalGatewayIds;
        List<String> fabricIds;
    }
    public static class TenantStruct {
        public String id;
        public String name;
        public String description;
        public TenantResPoolStruct resPool;

        public HuaweiIMasterTenantVO toHuaweiIMasterTenantVO() {
            HuaweiIMasterTenantVO vo = new HuaweiIMasterTenantVO();
            vo.setName(this.name);
            vo.setUuid(HuaweiIMasterHelper.huaweiIMasterIdToZStackUuid(this.id));
            vo.setDescription(this.description);
            if (this.resPool.fabricIds != null && !this.resPool.fabricIds.isEmpty()) {
                List<HuaweiIMasterTenantFabricRefVO> refVOS = new ArrayList<>();
                for (String id : this.resPool.fabricIds) {
                    HuaweiIMasterTenantFabricRefVO refVO = new HuaweiIMasterTenantFabricRefVO();
                    refVO.setFabricUuid(HuaweiIMasterHelper.huaweiIMasterIdToZStackUuid(id));
                    refVO.setTenantUuid(vo.getUuid());
                    refVOS.add(refVO);
                }
                vo.setFabricIds(refVOS.stream().collect(Collectors.toSet()));
            }
            vo.setState(SdnControllerTableState.Enabled);
            return vo;
        }
    }

    /*
    * {'tenant': [
    * {'id': 'dd8c1298-58c2-4d01-963e-18ed920bc708',
    * 'name': 'shixin-t1',
    * 'description': '',
    * 'producer': 'component-ui',
    * 'createAt': '2025-04-28 15:19:42',
    * 'updateAt': None,
    * 'multicastCapability': False,
    * 'quota': {
    *       'logicVasNum': 10,
    *       'logicRouterNum': 10,
    *       'logicSwitchNum': 10},
    * 'multicastQuota': None,
    * 'resPool': {'externalGatewayIds': [],
    * 'fabricIds': ['018a7946-156e-413d-b9ce-4d8a56c87c36'],
    * 'vmmIds': None, 'dhcpGroupIds': None}}],
    * 'totalNum': 1, 'pageIndex': 1, 'pageSize': 1}
     */
    public static class GetTenantRsp extends HuaweiSdnRsp {
        public List<TenantStruct> tenant;
    }

    public static class GetSwitchCmd extends HuaweiSdnCmd {
    }

    public static class SwitchMemberStruct {
        public String type;
        public String mastser;
        public String standby;
        public String leaf;
    }

    public static class SwitchStruct {
        public String id;
        public String name;
        public String ip;
        public String mac;
        public String mode;
        public String softWare;

        public PhysicalSwitchVO toPhysicalSwitchVO() {
            PhysicalSwitchVO vo = new PhysicalSwitchVO();
            vo.setUuid(HuaweiIMasterHelper.huaweiIMasterIdToZStackUuid(this.id));
            vo.setName(this.name);
            vo.setIp(this.ip);
            vo.setMac(this.mac.toLowerCase());
            vo.setMode(this.mode);
            vo.setSoftwareVersion(this.softWare);
            return vo;
        }
    }

    /*
     * {'devices': [
     * {'id': 'b9d708c4-27c6-30b1-b9ff-fe2d989c3a48',
     * 'name': 'huawei_152',
     * 'location': 'Beijing China',
     * 'ip': '************',
     * 'status': 0,
     * 'type': 'SWITCH',
     * 'mac': 'C0:E3:FB:65:AB:D1',
     * 'mode': 'CE6881-48S6CQ',
     * 'vtepIp': '**********',
     * 'stack': False,
     * 'members': {
     *      'type': 'NONE',
     *      'mastser': 'c0:e3:fb:65:ab:d0',
     *      'standby': None, 'leaf': None},
     *  'poolId': '018a7946-156e-413d-b9ce-4d8a56c87c36',
     *  'softWare': 'V200R021C00SPC200',
     *  'cpuRate': 0}], 'pageIndex': 1, 'pageSize': 4000, 'totalNum': 1}*/
    public static class GetSwitchRsp extends HuaweiSdnRsp {
        public List<SwitchStruct> devices;
    }

    public static class GetSwitchPortCmd extends HuaweiSdnCmd {
        List<String> deviceIdList;
    }

    public static class SwitchDevicePortStruct {
        public String portId;
        public String portName;
        public String portType;
        public String ethTrunkName;
    }

    public static class SwitchDevicePort {
        String deviceId;
        List<SwitchDevicePortStruct> portList;
    }

    public static class SwitchDevicePortList {
        public List<SwitchDevicePort> devicePortList;
    }

    // zstack only save port: #1 portType include *GE or ETHTRUNK,
    // #2 portName does not include "."
    /* get ports: {'devicePortList': [{'deviceId': 'b9d708c4-27c6-30b1-b9ff-fe2d989c3a48', 'portList': [
        {'portId': 'a11993d6-ab70-34b9-abae-e05e5468e416',
        'portName': '10GE1/0/11.2002',
        'portStatus': None,
        'ethTrunkName': '10GE1/0/11',
        'portMac': None,
        'portSpeed': '0',
        'portIp': '',
        'portType': 'IFMIPHYTYPE10GE'},
        {'portId': 'd1bb68ff-d48b-3a2a-91b1-731594979a34',
        'portName': 'NULL0',
        'portStatus': 'UP',
        'ethTrunkName': None,
        'portMac': '00-00-00-00-00-00',
        'portSpeed': '0',
        'portIp': '',
         'portType': 'IFMIPHYTYPENULL'},
         {'portId': '16791318-1615-3f07-b173-d18c9cf194f6',
         'portName': 'MEth0/0/0',
         'portStatus': 'DOWN',
         'ethTrunkName': None,
         'portMac': 'C0-E3-FB-65-AB-D0',
         'portSpeed': '10000000',
         'portIp': '***********',
         'portType': 'IFMIPHYTYPEMETH'},
         {'portId': 'db6003d1-8fe3-3990-9a31-863b31846a08',
         'portName': '100GE1/0/1',
         'portStatus': 'DOWN',
         'ethTrunkName': None,
         'portMac': 'C0-E3-FB-65-AB-D1',
         'portSpeed': '100000000000',
         'portIp': '',
         'portType': 'IFMIPHYTYPE100GE'},
          {'portId': 'da3ca265-55e2-3487-9504-4f98c689093d',
          'portName': '10GE1/0/6',
          'portStatus': 'UP',
          'ethTrunkName': 'Eth-Trunk56',
          'portMac': 'C0-E3-FB-65-AB-D1',
          'portSpeed': '10000000000',
          'portIp': '',
          'portType': 'IFMIPHYTYPE10GE'},
           {'portId': '16ec8233-ced0-36a8-bfe1-7dce933287a5',
           'portName': 'Vlanif31',
           'portStatus': 'UP',
           'ethTrunkName': None,
           'portMac': 'C0-E3-FB-65-AB-D5',
           'portSpeed': '1000000000',
           'portIp': '************',
           'portType': 'IFMIPHYTYPEVLANIF'},
           {'portId': '54bd0f91-fb5e-3ead-97e8-8bd2a5af31a7',
           'portName': 'Eth-Trunk1',
           'portStatus': 'UP',
           'ethTrunkName': None,
           'portMac': 'C0-E3-FB-65-AB-D1',
           'portSpeed': '10000000000',
           'portIp': '',
           'portType': 'IFMIPHYTYPEETHTRUNK'},
           {'portId': '424ad323-435e-3091-b9f7-bb94b4adbcc5',
           'portName': 'LoopBack0',
           'portStatus': 'UP',
           'ethTrunkName': None,
           'portMac': '00-00-00-00-00-00',
           'portSpeed': '0',
           'portIp': '**********',
           'portType': 'IFMIPHYTYPELOOPBACK'},
           {'portId': '243c8052-bd26-370c-93e1-3a20f6ddb595',
           'portName': 'Nve1',
           'portStatus': 'UP',
           'ethTrunkName': None,
           'portMac': 'C0-E3-FB-65-AB-D1',
           'portSpeed': '0', 'portIp': '',
           'portType': 'IFMIPHYTYPENVE'},
           {'portId': '3df03288-ffab-30f7-87f1-fe9555979353',
           'portName': 'Vbdif5002',
           'portStatus': 'UP',
           'ethTrunkName': None,
           'portMac': '00-00-5E-00-01-02',
           'portSpeed': '1000000000',
           'portIp': '***********',
           'portType': 'IFMIPHYTYPEVBDIF'},
           {'portId': '480ec9d4-a180-3ce8-a958-6e837b212db5',
           'portName': 'Eth-Trunk1.2003',
           'portStatus': 'UP',
           'ethTrunkName': 'Eth-Trunk1',
           'portMac': 'C0-E3-FB-65-AB-D1',
           'portSpeed': '10000000000',
           'portIp': '',
           'portType': 'IFMIPHYTYPEETHTRUNK'},
           ]}]} */
    public static class GetSwitchPortRsp extends HuaweiSdnRsp {
        public List<SwitchDevicePort> devicePortList;

        public Map<String, List<PhysicalSwitchPortVO>> getDevicePortMap() {
            Map<String, List<PhysicalSwitchPortVO>> portMap = new HashMap<>();
            if (this.devicePortList == null) {
                return portMap;
            }

            for (SwitchDevicePort port : this.devicePortList) {
                String switchId = HuaweiIMasterHelper.huaweiIMasterIdToZStackUuid(port.deviceId);
                portMap.computeIfAbsent(switchId, k -> new ArrayList<>());
                if (port.portList == null) {
                    continue;
                }

                for (SwitchDevicePortStruct struct : port.portList) {
                    try {
                        Matcher matcher = physSwitchPortNameFilter.matcher(struct.portName);
                        if (matcher.find()) {
                            continue;
                        }
                    } catch (Exception e) {
                        logger.debug(String.format("filter physical switch port name[%s] failed because %s",
                                struct.portName, e.getMessage()));
                        continue;
                    }


                    PhysicalSwitchPortVO vo = new PhysicalSwitchPortVO();
                    vo.setUuid(HuaweiIMasterHelper.huaweiIMasterIdToZStackUuid(struct.portId));
                    vo.setName(struct.portName);
                    vo.setPortType(struct.portType);
                    vo.setEthTrunkName(struct.ethTrunkName);
                    vo.setSwitchUuid(switchId);
                    portMap.get(switchId).add(vo);
                }
            }
            return portMap;
        }
    }

    public static class GetVpcCmd extends HuaweiSdnCmd {
    }

    public static class VpcStruct {
        public String id;
        public String name;
        public String description;
        public String tenantId;
        List<String> fabricId;

        public HuaweiIMasterVpcVO toHuaweiIMasterLogicalNetworkVO() {
            HuaweiIMasterVpcVO vo = new HuaweiIMasterVpcVO();
            vo.setUuid(HuaweiIMasterHelper.huaweiIMasterIdToZStackUuid(this.id));
            vo.setName(this.name);
            vo.setDescription(this.description);
            vo.setTenantId(HuaweiIMasterHelper.huaweiIMasterIdToZStackUuid(this.tenantId));
            if (this.fabricId != null && !this.fabricId.isEmpty()) {
                List<String> uuids = new ArrayList<>();
                for (String id : this.fabricId) {
                    uuids.add(HuaweiIMasterHelper.huaweiIMasterIdToZStackUuid(id));
                }
                vo.setFabricId(String.join(HuaweiIMasterConstant.HUAWEI_IMASTER_FABRIC_SPLIT, uuids));
            }
            vo.setState(SdnControllerTableState.Enabled);
            return vo;
        }
    }

    /* get vpcs: {'network': [
        {'id': '414e2ec4-e3bc-40a4-b53d-cdb6bc16edc7',
        'name': 'shixin-vpc',
        'description': '',
        'tenantId': 'dd8c1298-58c2-4d01-963e-18ed920bc708',
        'fabricId': ['018a7946-156e-413d-b9ce-4d8a56c87c36'],
        'multicastCapability': False,
        'type': 'Instance',
        'additional': {
            'producer': 'component-ui',
            'createAt': '2025-04-28 15:20:19',
            'updateAt': '2025-04-28 15:21:01'},
            'isVpcDeployed': True}], 'totalNum': 2, 'pageIndex': 1, 'pageSize': 2}
     * */
    public static class GetVpcRsp extends HuaweiSdnRsp {
        public List<VpcStruct> network;
    }

    public static class GetFabricCmd extends HuaweiSdnCmd {
    }

    /*
    * get fabrics: {'fabric': [
    *   {'id': '018a7946-156e-413d-b9ce-4d8a56c87c36',
    *   'name': 'test',
    *   'description': '',
    *   'networkType': 'Distributed',
    *   'physicalNetworkMode': 'Vxlan',
    *   'bgpEvpnEnable': True,
    *   'extInterfaceType': 'Vbdif',
    *   'arpBroadcastSuppression': False,
    *   'dciSplitGroup': None,
    *   'microSegmentCapability': False,
    *   'multicastCapability': False,
    *   'segmentMasks': [],
    *   'aclModel': 'loose',
    *   'sfcCapability': 'Pbr',
    *   'faultService': False,
    *   'underLay': '',
    *   'management': '',
    *   'localCrossErt': None,
    *   'sdnMode': True,
    *   'sameRdEnable': False}], 'totalNum': '1'}
     */
    public static class FabricStruct {
        public String id;
        public String name;
        public String description;
        public String networkType;
        public String physicalNetworkMode;

        public HuaweiIMasterFabricVO toHuaweiIMasterFabricVO() {
            HuaweiIMasterFabricVO vo = new HuaweiIMasterFabricVO();
            vo.setUuid(HuaweiIMasterHelper.huaweiIMasterIdToZStackUuid(this.id));
            vo.setName(this.name);
            vo.setDescription(this.description);
            vo.setState(SdnControllerTableState.Enabled);
            return vo;
        }
    }

    public static class GetFabricRsp extends HuaweiSdnRsp {
        public List<FabricStruct> fabric;
    }

    /* get switchs: {'switch': [
    {'id': '60ff8b39-4748-4d19-b984-dfaac03c262f',
    'name': 'ls-1',
    'description': '',
    'logicNetworkId': '414e2ec4-e3bc-40a4-b53d-cdb6bc16edc7',
    'vni': 10001, 'bd': 5000,
    'mappingType': 'None',
    'vlan': [],
    'macAddress': '00:00:5E:00:01:02',
    'tenantId': 'dd8c1298-58c2-4d01-963e-18ed920bc708',
    'tenantName': 'shixin-t1',
    'qosId': None,
    'arpBroadcastStormEnable': 'default',
    'arpL2ProxyEnable': False,
    'subnets': ['***********/24', '2025:6:9:1::1/64'],
    'hostInfoCollect': True,
    'gwManagementEnable': True,
    'arpDirectRouteEnable': False,
    'ndDirectRouteEnable': False,
    'vlinkDeviceGroups': None, 'macRouteSuppress': False,
    'rtPrefix': 0,
    'additional': {
        'producer': 'component-ui',
        'createAt': '2025-06-05 17:48:59',
        'updateAt': '2025-06-09 11:19:11'},
    'stormSuppress': {
        'broadcastEnable': False,
        'multicastEnable': False,
        'unicastEnable': False,
        'broadcastCbs': None,
        'broadcastCbsUnit': None,
        'broadcastCir': None,
        'broadcastCirUnit': None,
        'unicastCbs': None,
        'unicastCbsUnit': None,
        'unicastCir': None,
        'unicastCirUnit': None,
        'multicastCbs': None,
        'multicastCbsUnit': None,
        'multicastCir': None,
        'multicastCirUnit': None},
    'importRouteTargets': [],
    'exportRouteTargets': [],
    'qosTemplateId': ''}], 'totalNum': 2, 'pageIndex': 1, 'pageSize': 2}
     */
    public static class GetHuaweiLogicalSwitchCmd extends HuaweiSdnCmd {
    }

    public static class HuaweiLogicalSwitchStruct {
        public String id;
        public String name;
        public String description;
        public String logicNetworkId;
        public Integer vni;
        public List<Integer> vlan;
        public String mappingType;
        public String macAddress;
        public String tenantId;
        public String tenantName;
        public List<String> subnets;
        public HuaweiSdnAdditional additional;
    }

    public static class GetHuaweiLogicalSwitchRsp extends HuaweiSdnRsp {
        @SerializedName("switch")
        public List<HuaweiLogicalSwitchStruct> logicalSwitch;
    }

    public static class CreateHuaweiLogicalSwitchCmd extends HuaweiSdnCmd {
        //id, name, String, logicNetworkId is needed
        @SerializedName("switch")
        public List<HuaweiLogicalSwitchStruct> logicalSwitch;
    }

    public static class CreateHuaweiLogicalSwitchRsp extends HuaweiSdnRsp {
    }

    public static class UpdateHuaweiLogicalSwitchCmd extends HuaweiSdnCmd {
        @SerializedName("switch")
        public List<HuaweiLogicalSwitchStruct> logicalSwitch;
    }

    public static class UpdateHuaweiLogicalSwitchRsp extends HuaweiSdnRsp {
    }

    public static class DeleteHuaweiLogicalSwitchCmd extends HuaweiSdnCmd {
    }

    public static class DeleteHuaweiLogicalSwitchRsp extends HuaweiSdnRsp {
    }

    /* get ports: {
         'port': [
         {'id': '0b144116-6a89-4be1-b3e2-87726ed3afdb',
         'name': 'lsp-2050',
         'qosId': None,
         'description': '',
         'unsharePortVlan': False,
         'status': 'up',
         'logicSwitchId': '934778dd-e617-438e-81d2-54cfcfef0d34',
         'logicSwitchName': None,
         'metaData': None,
         'accessInfo': {
             'mode': 'Uni',
             'type': 'Dot1q',
             'vlan': 2050,
             'egressVlan': None,
             'qinq': {
                 'innerVidBegin': None,
                 'innerVidEnd': None,
                 'outerVidBegin': None,
                 'outerVidEnd': None,
                 'rewriteAction': 'POPDOUBLE'},
             'location': [
                 {'deviceGroupId': 'b9d708c4-27c6-30b1-b9ff-fe2d989c3a48',
                 'deviceGroupName': 'huawei_152',
                 'deviceId': 'b9d708c4-27c6-30b1-b9ff-fe2d989c3a48',
                 'deviceName': 'huawei_152',
                 'portId': '21ce6709-2144-3be5-ba7a-1a028f290718',
                 'portName': '10GE1/0/11',
                 'deviceIp': '************'}],
             'subinterfaceNumber': None},
         'tenantId': 'dd8c1298-58c2-4d01-963e-18ed920bc708',
         'additional': {'producer': 'component-ui', 'createAt': '2025-06-05 15:48:00', 'updateAt': None},
         'fabricId': '018a7946-156e-413d-b9ce-4d8a56c87c36'},
         ], 'totalNum': 4, 'pageIndex': 1, 'pageSize': 4}
      */
    public static class GetHuaweiLogicalSwitchPortCmd extends HuaweiSdnCmd {
    }

    public static class HuaweiLogicalSwitchPortQinQStruct {
        public Integer innerVidBegin;
        public Integer innerVidEnd;
        public Integer outerVidBegin;
        public Integer outerVidEnd;
        public String rewriteAction;
    }

    public static class HuaweiLogicalSwitchPortLocationStruct {
        public String deviceGroupId;
        public String deviceGroupName;
        public String deviceId;
        public String deviceName;
        public String portId;
        public String portName;
        public String deviceIp;

        public HuaweiLogicalSwitchPortLocationStruct() {
        }

        public HuaweiLogicalSwitchPortLocationStruct(PhysicalSwitchPortInventory inv) {
            //PhysicalSwitchVO svo = Q.New(PhysicalSwitchVO.class).eq(PhysicalSwitchVO_.uuid, inv.getSwitchUuid()).find();
            //this.deviceGroupId = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(svo.getUuid());
            //this.deviceGroupName = svo.getName();
            //this.deviceId = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(svo.getUuid());
            //this.deviceName = svo.getName();
            this.portId = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(inv.getUuid());
            this.portName = inv.getName();
            //this.deviceIp = svo.getIp();
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;

            HuaweiLogicalSwitchPortLocationStruct other = (HuaweiLogicalSwitchPortLocationStruct) obj;
            if (!StringUtils.equals(deviceGroupId, other.deviceGroupId)) {
                return false;
            }

            if (!StringUtils.equals(deviceGroupName, other.deviceGroupName)) {
                return false;
            }

            if (!StringUtils.equals(deviceId, other.deviceId)) {
                return false;
            }

            if (!StringUtils.equals(deviceName, other.deviceName)) {
                return false;
            }

            if (!StringUtils.equals(portId, other.portId)) {
                return false;
            }

            if (!StringUtils.equals(portName, other.portName)) {
                return false;
            }

            if (!StringUtils.equals(deviceIp, other.deviceIp)) {
                return false;
            }

            return true;
        }

        @Override
        public int hashCode() {
            return Objects.hash(deviceGroupId, deviceGroupName, deviceId, deviceName, portId, portName, deviceIp);
        }
    }

    public static class HuaweiLogicalSwitchPortAccessInfoStruct {
        public String mode;
        public String type;
        public Integer vlan;
        public Integer egressVlan;
        public String logicSwitchName;
        public HuaweiLogicalSwitchPortQinQStruct qinq;
        public List<HuaweiLogicalSwitchPortLocationStruct> location;
        public String tenantId;
        public String fabricId;
        public HuaweiSdnAdditional additional;

        public HuaweiLogicalSwitchPortAccessInfoStruct() {
        }
    }

    public static class HuaweiLogicalSwitchPortStruct {
        public String id;
        public String name;
        public String description;
        public String logicSwitchId;
        public String logicSwitchName;
        public HuaweiLogicalSwitchPortAccessInfoStruct accessInfo;
        public String tenantId;
        public String fabricId;
        public HuaweiSdnAdditional additional;
    }

    public static class GetHuaweiLogicalSwitchPortRsp extends HuaweiSdnRsp {
        public List<HuaweiLogicalSwitchPortStruct> port;
    }

    public static class CreateHuaweiLogicalSwitchPortCmd extends HuaweiSdnCmd {
        //id, name, String, logicNetworkId, additional is needed
        public List<HuaweiLogicalSwitchPortStruct> port;
    }

    public static class CreateHuaweiLogicalSwitchPortRsp extends HuaweiSdnRsp {
    }

    public static class UpdateHuaweiLogicalSwitchPortCmd extends HuaweiSdnCmd {
        public List<HuaweiLogicalSwitchPortStruct> port;
    }

    public static class UpdateHuaweiLogicalSwitchPortRsp extends HuaweiSdnRsp {
    }

    public static class DeleteHuaweiLogicalSwitchPortCmd extends HuaweiSdnCmd {
    }

    public static class DeleteHuaweiLogicalSwitchPortRsp extends HuaweiSdnRsp {
    }

    /* get routers: {'router': [
     {'id': 'bba372f3-8ce8-4ecf-a914-9a5e37904551',
     'name': 'test2',
     'description': '',
     'qosId': '',
     'logicNetworkId': '414e2ec4-e3bc-40a4-b53d-cdb6bc16edc7',
     'routerLocations': [{
        'fabricId': '018a7946-156e-413d-b9ce-4d8a56c87c36',
        'fabricRole': None,
        'fabricName': 'test',
        'deviceGroup': None}],
     'type': 'Normal',
     'vni': 10006,
     'vrfName': 'test2_10006',
     'connectMode': None,
     'externalCidr': None,
     'internalCidr': None,
     'maxLoadBalance': None,
     'rtPrefix': 0,
     'additional': {
        'producer': 'component-ui',
        'createAt': '2025-04-29 10:06:05',
        'updateAt': '2025-06-09 11:18:45'},
     'tenantId': 'dd8c1298-58c2-4d01-963e-18ed920bc708',
     'tenantName': 'shixin-t1',
     'egressGatewayDevGrpId': None,
     'importRouteTargets': [],
     'exportRouteTargets': [], 'routes': None,
     'subnets': [{
        'id': '184205dc-62e4-4943-9420-672cdda826e3',
        'cidr': '***********/24',
        'gatewayIp': '***********'},
        {'id': '5dd3fbdf-08ba-4b07-93a0-2773fc2ba270',
        'cidr': '2025:6:9:1::/64',
        'gatewayIp': '2025:6:9:1::1'},
        {'id': 'd306c194-0d21-4c5b-b3ec-5650abb3601b',
        'cidr': '***********/24',
        'gatewayIp': '***********'}],
     'bgp': None, 'routerLocation': None}], 'totalNum': 2, 'pageIndex': 1, 'pageSize': 2}
    * */
    public static class GetHuaweiLogicalRouterLocationStruct {
        public String fabricId;
        public String fabricName;
    }

    public static class GetHuaweiSubnetsStruct {
        public String id;
        public String cidr;
        public String gatewayIp;
    }

    public static class HuaweiLogicalRouterStruct {
        public String id;
        public String name;
        public String description;
        public String logicNetworkId;
        public String type;
        public Integer vni;
        public List<GetHuaweiLogicalRouterLocationStruct> routerLocations;
        public String tenantId;
        public HuaweiSdnAdditional additional;
        public List<GetHuaweiSubnetsStruct> subnets;
    }

    public static class CreateHuaweiLogicalRouterCmd extends HuaweiSdnCmd {
        //id, name, String, logicNetworkId, additional is needed
        public HuaweiLogicalRouterStruct router;
    }

    public static class CreateHuaweiLogicalRouterRsp extends HuaweiSdnRsp {
    }

    public static class DeleteHuaweiLogicalRouterCmd extends HuaweiSdnCmd {
    }

    public static class DeleteHuaweiLogicalRouterRsp extends HuaweiSdnRsp {
    }

    public static class GetHuaweiLogicalRouterCmd extends HuaweiSdnCmd {
    }

    public static class GetHuaweiLogicalRouterRsp extends HuaweiSdnRsp {
        public List<HuaweiLogicalRouterStruct> router;
    }

    public static class HuaweiLogicalLinkStruct {
        public String id;
        public String logicSwitchId;
        public String logicRouterId;
        public String logicNetworkId;
        public HuaweiSdnAdditional additional;
    }

    public static class CreateHuaweiLogicalLinkCmd extends HuaweiSdnCmd {
        List<HuaweiLogicalLinkStruct> link;
    }

    public static class CreateHuaweiLogicalLinkRsp extends HuaweiSdnRsp {
    }

    public static class DeleteHuaweiLogicalLinkCmd extends HuaweiSdnCmd {
    }

    public static class DeleteHuaweiLogicalLinkRsp extends HuaweiSdnRsp {
    }

    public static class HuaweiSubnetStruct {
        public String id;
        public String logicSwitchId;
        public String logicRouterId;
        public String tenantId;
        public String cidr;
        public String gatewayIp;
        public Boolean dhcpEnable;
        public String ipv6RaMode;
        public HuaweiSdnAdditional additional;
    }

    public static class CreateHuaweiSubnetCmd extends HuaweiSdnCmd {
        List<HuaweiSubnetStruct> subnet;
    }

    public static class CreateHuaweiSubnetRsp extends HuaweiSdnRsp {
    }

    public static class DeleteHuaweiSubnetCmd extends HuaweiSdnCmd {
    }

    public static class DeleteHuaweiSubnetRsp extends HuaweiSdnRsp {
    }

    public static class GetHuaweiSubnetCmd extends HuaweiSdnCmd {
    }

    public static class GetHuaweiSubnetRsp extends HuaweiSdnRsp {
        List<HuaweiSubnetStruct> subnet;
    }

    // System Information API classes
    public static class GetSystemInfoCmd extends HuaweiSdnCmd {
    }

    public static class SystemPlatformStruct {
        public String machine;
        @SerializedName("os-name")
        public String osName;
        @SerializedName("os-release")
        public String osRelease;
        @SerializedName("os-version")
        public String osVersion;
    }

    public static class SystemInfoStruct {
        public SystemPlatformStruct platform;
        @SerializedName("system-description")
        public String systemDescription;
        @SerializedName("object-id")
        public String objectId;
        @SerializedName("system-up-time")
        public String systemUpTime;
        @SerializedName("device-esn")
        public String deviceEsn;
        @SerializedName("platform-name")
        public String platformName;
        @SerializedName("platform-version")
        public String platformVersion;
        @SerializedName("product-name")
        public String productName;
        @SerializedName("product-version")
        public String productVersion;
        @SerializedName("admin-ip")
        public String adminIp;
        @SerializedName("vendor-name")
        public String vendorName;
    }

    /*
     * Response format based on the API documentation:
     * {
     *   'platform': {
     *     'machine': 'x86_64',
     *     'os-name': 'EulerOS release 2.0 (SP11x86_64)',
     *     'os-release': '5.10.0-**********.h1841.eulerosv2r11.x86_64',
     *     'os-version': '#1 SMP Fri Mar 7 17:31:29 UTC 2025'
     *   },
     *   'system-description': 'Huawei iMaster NCE-Fabric(R) software, Version3.1 ...',
     *   'object-id': '*******.4.1.2011.2.313.3(acDcn)',
     *   'system-up-time': '83 days, 1:51:35.00',
     *   'device-esn': '',
     *   'platform-name': null,
     *   'platform-version': null,
     *   'product-name': 'iMaster NCE-Fabric',
     *   'product-version': 'V100R023C00SPC201',
     *   'admin-ip': '**************',
     *   'vendor-name': 'HUAWEI'
     * }
     */
    public static class GetSystemInfoRsp extends HuaweiSdnRsp {
        public SystemPlatformStruct platform;
        @SerializedName("system-description")
        public String systemDescription;
        @SerializedName("object-id")
        public String objectId;
        @SerializedName("system-up-time")
        public String systemUpTime;
        @SerializedName("device-esn")
        public String deviceEsn;
        @SerializedName("platform-name")
        public String platformName;
        @SerializedName("platform-version")
        public String platformVersion;
        @SerializedName("product-name")
        public String productName;
        @SerializedName("product-version")
        public String productVersion;
        @SerializedName("admin-ip")
        public String adminIp;
        @SerializedName("vendor-name")
        public String vendorName;
    }
}

