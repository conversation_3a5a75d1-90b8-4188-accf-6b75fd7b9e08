package org.zstack.network.huawei.imaster;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.cascade.AbstractAsyncCascadeExtension;
import org.zstack.core.cascade.CascadeAction;
import org.zstack.core.cascade.CascadeConstant;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.header.core.Completion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.message.MessageReply;
import org.zstack.utils.CollectionUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.function.Function;
import org.zstack.utils.logging.CLogger;

import java.util.Arrays;
import java.util.List;

/**
 * Cascade extension for Huawei iMaster VRouter
 */
public class HuaweiIMasterVRouterCascadeExtension extends AbstractAsyncCascadeExtension {
    private static final CLogger logger = Utils.getLogger(HuaweiIMasterVRouterCascadeExtension.class);
    private static final String NAME = HuaweiIMasterVRouterVO.class.getSimpleName();

    @Autowired
    private DatabaseFacade dbf;
    @Autowired
    private CloudBus bus;

    @Override
    public void asyncCascade(CascadeAction action, Completion completion) {
        if (action.isActionCode(CascadeConstant.DELETION_CHECK_CODE)) {
            handleDeletionCheck(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_DELETE_CODE, CascadeConstant.DELETION_FORCE_DELETE_CODE)) {
            handleDeletion(action, completion);
        } else if (action.isActionCode(CascadeConstant.DELETION_CLEANUP_CODE)) {
            handleDeletionCleanup(action, completion);
        } else {
            completion.success();
        }
    }

    private void handleDeletionCleanup(CascadeAction action, Completion completion) {
        completion.success();
    }

    private void handleDeletion(CascadeAction action, final Completion completion) {
        final List<HuaweiIMasterVRouterInventory> vrouterInvs = vrouterFromAction(action);
        if (vrouterInvs == null || vrouterInvs.isEmpty()) {
            completion.success();
            return;
        }

        List<HuaweiIMasterVRouterDeletionMsg> msgs = CollectionUtils.transformToList(vrouterInvs,
                new Function<HuaweiIMasterVRouterDeletionMsg, HuaweiIMasterVRouterInventory>() {
            @Override
            public HuaweiIMasterVRouterDeletionMsg call(HuaweiIMasterVRouterInventory arg) {
                HuaweiIMasterVRouterDeletionMsg msg = new HuaweiIMasterVRouterDeletionMsg();
                msg.setSdnControllerUuid(arg.getSdnControllerUuid());
                msg.setvRouterUuid(arg.getUuid());
                bus.makeTargetServiceIdByResourceUuid(msg, HuaweiIMasterConstant.SERVICE_ID, arg.getUuid());
                return msg;
            }
        });

        new While<>(msgs).each((msg, compl) -> {
            bus.send(msg, new CloudBusCallBack(compl) {
                @Override
                public void run(MessageReply reply) {
                    if (!reply.isSuccess()) {
                        logger.warn(String.format("failed to delete Huawei iMaster VRouter[uuid:%s], %s",
                                msg.getvRouterUuid(), reply.getError()));
                    }
                    compl.done();
                }
            });
        }).run(new WhileDoneCompletion(completion) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                completion.success();
            }
        });
    }

    private void handleDeletionCheck(CascadeAction action, Completion completion) {
        completion.success();
    }

    @Override
    public List<String> getEdgeNames() {
        return Arrays.asList(HuaweiIMasterVpcVO.class.getSimpleName(), HuaweiIMasterFabricVO.class.getSimpleName());
    }

    @Override
    public String getCascadeResourceName() {
        return NAME;
    }

    @Override
    public CascadeAction createActionForChildResource(CascadeAction action) {
        if (CascadeConstant.DELETION_CODES.contains(action.getActionCode())) {
            List<HuaweiIMasterVRouterInventory> vrouters = vrouterFromAction(action);
            if (vrouters != null) {
                return action.copy().setParentIssuer(NAME).setParentIssuerContext(vrouters);
            }
        }
        return null;
    }

    private List<HuaweiIMasterVRouterInventory> vrouterFromAction(CascadeAction action) {
         if (HuaweiIMasterVpcVO.class.getSimpleName().equals(action.getParentIssuer())) {
            List<String> vpcUuids = CollectionUtils.transformToList((List<HuaweiIMasterVpcInventory>) action.getParentIssuerContext(),
                    new Function<String, HuaweiIMasterVpcInventory>() {
                @Override
                public String call(HuaweiIMasterVpcInventory arg) {
                    return arg.getUuid();
                }
            });
            if (vpcUuids.isEmpty()) {
                return null;
            }

            List<HuaweiIMasterVRouterVO> vrouterVOs = Q.New(HuaweiIMasterVRouterVO.class)
                    .in(HuaweiIMasterVRouterVO_.logicalNetworkId, vpcUuids).list();
            return HuaweiIMasterVRouterInventory.valueOf(vrouterVOs);
        } else if (HuaweiIMasterFabricVO.class.getSimpleName().equals(action.getParentIssuer())) {
            List<String> fabricUuids = CollectionUtils.transformToList((List<HuaweiIMasterFabricInventory>) action.getParentIssuerContext(),
                    new Function<String, HuaweiIMasterFabricInventory>() {
                        @Override
                        public String call(HuaweiIMasterFabricInventory arg) {
                            return arg.getUuid();
                        }
                    });
            if (fabricUuids.isEmpty()) {
                return null;
            }

            List<HuaweiIMasterVRouterVO> vrouterVOs = Q.New(HuaweiIMasterVRouterVO.class)
                    .in(HuaweiIMasterVRouterVO_.fabricUuid, fabricUuids).list();
            return HuaweiIMasterVRouterInventory.valueOf(vrouterVOs);
        } else if (HuaweiIMasterVRouterVO.class.getSimpleName().equals(action.getParentIssuer())) {
            return action.getParentIssuerContext();
        }
        return null;
    }
}
