package org.zstack.network.huawei.imaster;

import org.zstack.header.network.l2.L2NetworkVO;
import org.zstack.header.tag.TagDefinition;
import org.zstack.tag.PatternedSystemTag;

/**
 * Huawei iMaster System Tags
 */
@TagDefinition
public class HuaweiIMasterSystemTags {
    public static String L2_NETWORK_HUAWEI_VPC_UUID_TOKEN = "huaweiVpcUuid";
    public static PatternedSystemTag L2_NETWORK_HUAWEI_VPC_UUID = new PatternedSystemTag(String.format("huaweiVpcUuid::{%s}",
            L2_NETWORK_HUAWEI_VPC_UUID_TOKEN), L2NetworkVO.class);

    public static String L2_NETWORK_HUAWEI_TENANT_UUID_TOKEN = "huaweiTenantUuid";
    public static PatternedSystemTag L2_NETWORK_HUAWEI_TENANT_UUID = new PatternedSystemTag(String.format("huaweiTenantUuid::{%s}",
            L2_NETWORK_HUAWEI_TENANT_UUID_TOKEN), L2NetworkVO.class);
}
