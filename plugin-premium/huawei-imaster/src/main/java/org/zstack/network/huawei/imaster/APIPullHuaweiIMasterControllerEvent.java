package org.zstack.network.huawei.imaster;

import org.zstack.header.message.APIEvent;
import org.zstack.header.rest.RestResponse;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by boce.wang on 06/13/2025.
 */

@RestResponse(allTo = "inventories")
public class APIPullHuaweiIMasterControllerEvent extends APIEvent {
    private List<HuaweiIMasterSdnControllerInventory> inventories;

    public List<HuaweiIMasterSdnControllerInventory> getInventories() {
        return inventories;
    }

    public void setInventories(List<HuaweiIMasterSdnControllerInventory> inventories) {
        this.inventories = inventories;
    }

    public APIPullHuaweiIMasterControllerEvent(){
    }

    public APIPullHuaweiIMasterControllerEvent(String apiId) {
        super(apiId);
    }

    public static APIPullHuaweiIMasterControllerEvent __example__() {
        APIPullHuaweiIMasterControllerEvent event = new APIPullHuaweiIMasterControllerEvent();
        List<HuaweiIMasterSdnControllerInventory> inventories = new ArrayList<>();
        event.setInventories(inventories);
        return event;
    }
}