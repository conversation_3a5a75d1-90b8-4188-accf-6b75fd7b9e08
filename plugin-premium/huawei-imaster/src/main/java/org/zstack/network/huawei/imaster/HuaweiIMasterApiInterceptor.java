package org.zstack.network.huawei.imaster;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.ApiMessageInterceptor;
import org.zstack.header.apimediator.GlobalApiMessageInterceptor;
import org.zstack.header.exception.CloudRuntimeException;
import org.zstack.header.host.HostVO;
import org.zstack.header.host.HostVO_;
import org.zstack.header.message.APIMessage;
import org.zstack.header.network.l2.APIAttachL2NetworkToClusterMsg;
import org.zstack.header.network.l3.*;
import org.zstack.network.hostNetworkInterface.HostNetworkBondingVO;
import org.zstack.network.hostNetworkInterface.HostNetworkBondingVO_;
import org.zstack.network.hostNetworkInterface.HostNetworkInterfaceVO;
import org.zstack.network.hostNetworkInterface.HostNetworkInterfaceVO_;
import org.zstack.network.l2.vxlan.vxlanNetwork.VxlanNetworkConstant;
import org.zstack.network.l2.vxlan.vxlanNetwork.VxlanNetworkHelper;
import org.zstack.network.l2.vxlan.vxlanNetworkPool.APICreateVniRangeMsg;
import org.zstack.network.l3.L3NetworkHelper;
import org.zstack.network.l3.L3NetworkSystemTags;
import org.zstack.sdnController.SdnControllerSystemTags;
import org.zstack.sdnController.header.*;
import org.zstack.utils.CollectionDSL;
import org.zstack.utils.Utils;
import org.zstack.utils.gson.JSONObjectUtil;
import org.zstack.utils.logging.CLogger;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.zstack.core.Platform.argerr;
import static org.zstack.utils.CollectionDSL.map;


/**
 * Created by shixin.ruan on 06/16/2025
 */
public class HuaweiIMasterApiInterceptor implements ApiMessageInterceptor, GlobalApiMessageInterceptor {
    private static final CLogger logger = Utils.getLogger(HuaweiIMasterApiInterceptor.class);

    @Autowired
    protected DatabaseFacade dbf;
    @Autowired
    protected CloudBus bus;

    private HuaweiIMasterHelper helper = new HuaweiIMasterHelper();

    private void setServiceId(APIMessage msg) {
        if (msg instanceof SdnControllerMessage) {
            SdnControllerMessage smsg = (SdnControllerMessage) msg;
            bus.makeTargetServiceIdByResourceUuid(msg, HuaweiIMasterConstant.SERVICE_ID, smsg.getSdnControllerUuid());
        }
    }

    @Override
    public List<Class> getMessageClassToIntercept() {
        List<Class> ret = new ArrayList<>();
        ret.add(APICreateL3NetworkMsg.class);
        ret.add(APIAddSdnControllerMsg.class);
        ret.add(APICreateVniRangeMsg.class);
        ret.add(APICreateL2HardwareVxlanNetworkMsg.class);
        ret.add(APICreateL2HardwareVxlanNetworkPoolMsg.class);
        ret.add(APIAttachL2NetworkToClusterMsg.class);
        return ret;
    }

    @Override
    public InterceptorPosition getPosition() {
        return InterceptorPosition.END;
    }

    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        if (msg instanceof APICreateL3NetworkMsg) {
            validate((APICreateL3NetworkMsg) msg);
        } else if (msg instanceof APIAddSdnControllerMsg) {
            validate((APIAddSdnControllerMsg) msg);
        } else if (msg instanceof APICreateVniRangeMsg) {
            validate((APICreateVniRangeMsg) msg);
        } else if (msg instanceof APICreateL2HardwareVxlanNetworkMsg) {
            validate((APICreateL2HardwareVxlanNetworkMsg) msg);
        } else if (msg instanceof APIAttachL2NetworkToClusterMsg) {
            validate((APIAttachL2NetworkToClusterMsg) msg);
        } else if (msg instanceof APIDeleteHuaweiIMasterVRouterMsg) {
            validate((APIDeleteHuaweiIMasterVRouterMsg) msg);
        } else if (msg instanceof APIDeleteHuaweiIMasterTenantMsg) {
            validate((APIDeleteHuaweiIMasterTenantMsg) msg);
        } else if (msg instanceof APICreateHuaweiIMasterVRouterMsg) {
            validate((APICreateHuaweiIMasterVRouterMsg) msg);
        } else if (msg instanceof APICreateL2HardwareVxlanNetworkPoolMsg) {
            validate((APICreateL2HardwareVxlanNetworkPoolMsg) msg);
        } else if (msg instanceof APIDeleteHuaweiIMasterVpcMsg) {
            validate((APIDeleteHuaweiIMasterVpcMsg) msg);
        } else if (msg instanceof APIDeleteHuaweiIMasterFabricMsg) {
            validate((APIDeleteHuaweiIMasterFabricMsg) msg);
        }

        if (msg instanceof SdnControllerMessage) {
            setServiceId(msg);
        }

        return msg;
    }

    private void validate(APIAttachL2NetworkToClusterMsg msg) {
        HardwareL2VxlanNetworkPoolVO poolVO = dbf.findByUuid(msg.getL2NetworkUuid(), HardwareL2VxlanNetworkPoolVO.class);
        if (poolVO == null) {
            return;
        }

        SdnControllerVO vo = dbf.findByUuid(poolVO.getSdnControllerUuid(), SdnControllerVO.class);
        if (!vo.getVendorType().equals(HuaweiIMasterConstant.HUAWEI_IMASTER_CONTROLLER_TYPE)) {
            return;
        }

        List<String> hostUuids = Q.New(HostVO.class).select(HostVO_.uuid)
                .eq(HostVO_.clusterUuid, msg.getClusterUuid()).listValues();
        for (String hostUuid : hostUuids) {
            HostNetworkInterfaceVO interfaceVO = Q.New(HostNetworkInterfaceVO.class)
                    .eq(HostNetworkInterfaceVO_.hostUuid, hostUuid)
                    .eq(HostNetworkInterfaceVO_.interfaceName, poolVO.getPhysicalInterface()).find();
            HostNetworkBondingVO bondingVO = Q.New(HostNetworkBondingVO.class)
                    .eq(HostNetworkBondingVO_.hostUuid, hostUuid)
                    .eq(HostNetworkBondingVO_.bondingName, poolVO.getPhysicalInterface()).find();
            if (interfaceVO == null && bondingVO == null) {
                throw new ApiMessageInterceptionException(argerr("could not attach l2 network to cluster, " +
                        "because host[uuid:%s] has no interface[name:%s]", hostUuid, poolVO.getPhysicalInterface()));
            }
        }
    }

    private void validate(APICreateHuaweiIMasterVRouterMsg msg) {
        HuaweiIMasterVpcVO vpcVO = dbf.findByUuid(msg.getHuaweiVpcUuid(), HuaweiIMasterVpcVO.class);
        msg.setSdnControllerUuid(vpcVO.getSdnControllerUuid());
    }

    private void validate(APICreateL2HardwareVxlanNetworkPoolMsg msg) {
        SdnControllerVO sdnController = dbf.findByUuid(msg.getSdnControllerUuid(), SdnControllerVO.class);
        if (sdnController == null || !sdnController.getVendorType().equals(HuaweiIMasterConstant.HUAWEI_IMASTER_CONTROLLER_TYPE)) {
            return;
        }
    }

    private void validate(APICreateL2HardwareVxlanNetworkMsg msg) {
        HardwareL2VxlanNetworkPoolVO poolVO = dbf.findByUuid(msg.getPoolUuid(), HardwareL2VxlanNetworkPoolVO.class);
        SdnControllerVO controllerVO = dbf.findByUuid(poolVO.getSdnControllerUuid(), SdnControllerVO.class);
        if (!controllerVO.getVendorType().equals(HuaweiIMasterConstant.HUAWEI_IMASTER_CONTROLLER_TYPE)) {
            return;
        }

        if (msg.getVlan() == null) {
            throw new ApiMessageInterceptionException(
                    argerr("could not create l2 hardware vxlan network without vlan id"));
        }

        List<SdnVlanRange> vlanRanges = HuaweiIMasterHelper.getSdnVlanRange(poolVO.getSdnControllerUuid());
        boolean found = false;
        for (SdnVlanRange range : vlanRanges) {
            if (msg.getVlan() >= range.startVlan && msg.getVlan() <= range.endVlan) {
                found = true;
                break;
            }
        }
        if (!found) {
            throw new ApiMessageInterceptionException(
                    argerr("could not create huawei iMaster sdn vxlan network, because vlan id [%s] is out of range: %s",
                            msg.getVlan(), JSONObjectUtil.toJsonString(vlanRanges)));
        }

        if (msg.getSystemTags() == null || msg.getSystemTags().isEmpty()) {
            throw new ApiMessageInterceptionException(
                    argerr("could not create huawei iMaster sdn vxlan network without vpcId, tenantId"));
        }

        String vpcId = null;
        String tenantId = null;
        for (String tag : msg.getSystemTags()) {
            if (HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID.isMatch(tag)) {
                vpcId = HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID.getTokenByTag(
                        tag, HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID_TOKEN);
            }

            if (HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_TENANT_UUID.isMatch(tag)) {
                tenantId = HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_TENANT_UUID.getTokenByTag(
                        tag, HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_TENANT_UUID_TOKEN);
            }
        }

        if (vpcId == null) {
            throw new ApiMessageInterceptionException(
                    argerr("could not create huawei iMaster sdn vxlan network without vpcId"));
        }
        if (tenantId == null) {
            HuaweiIMasterVpcVO vpcVO = dbf.findByUuid(vpcId, HuaweiIMasterVpcVO.class);
            msg.getSystemTags().add(HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_TENANT_UUID.instantiateTag(
                    map(CollectionDSL.e(HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_TENANT_UUID_TOKEN, vpcVO.getTenantId()))
            ));
        }
    }

    private void validate(APICreateVniRangeMsg msg) {
        HardwareL2VxlanNetworkPoolVO poolVO = dbf.findByUuid(msg.getL2NetworkUuid(), HardwareL2VxlanNetworkPoolVO.class);
        if (poolVO == null) {
            return;
        }

        SdnControllerVO vo = dbf.findByUuid(poolVO.getSdnControllerUuid(), SdnControllerVO.class);
        if (!vo.getVendorType().equals(HuaweiIMasterConstant.HUAWEI_IMASTER_CONTROLLER_TYPE)) {
            return;
        }

        List<Map<String, String>> tokenList = SdnControllerSystemTags.VNI_RANGE.getTokensOfTagsByResourceUuid(
                poolVO.getSdnControllerUuid());
        boolean found = false;
        for (Map<String, String> tokens : tokenList) {
            int startVni = Integer.valueOf(tokens.get(SdnControllerSystemTags.START_VNI_TOKEN));
            int endVni = Integer.valueOf(tokens.get(SdnControllerSystemTags.END_VNI_TOKEN));

            if (msg.getStartVni() >= startVni && msg.getEndVni() <= endVni) {
                found = true;
                break;
            }
        }

        if (!found) {
            throw new ApiMessageInterceptionException(
                    argerr("could not add vni range because vni range[%d-%d] is not in the vni range",
                            msg.getStartVni(), msg.getEndVni()));
        }
    }

    private void validate(APIAddSdnControllerMsg msg) {
        if (!msg.getVendorType().equals(HuaweiIMasterConstant.HUAWEI_IMASTER_CONTROLLER_TYPE)) {
            return;
        }

        if (msg.getSystemTags() == null || msg.getSystemTags().isEmpty()) {
            throw new ApiMessageInterceptionException(
                    argerr("could not add huawei iMaster sdn controller without vni range"));
        }

        boolean validVniRange = false;
        boolean validVlanRange = false;
        for (String tag : msg.getSystemTags()) {
            if (SdnControllerSystemTags.VNI_RANGE.isMatch(tag)) {
                Map<String, String> vniMap = SdnControllerSystemTags.VNI_RANGE.getTokensByTag(tag);
                String startVni = vniMap.get(SdnControllerSystemTags.START_VNI_TOKEN);
                String endVni = vniMap.get(SdnControllerSystemTags.END_VNI_TOKEN);
                if (startVni == null || startVni.isEmpty() || endVni == null || endVni.isEmpty()) {
                    throw new ApiMessageInterceptionException(
                            argerr("could not add huawei iMaster sdn controller without vni range"));
                }

                int startVniInt = Integer.parseInt(startVni);
                int endVniInt = Integer.parseInt(endVni);
                if (!VxlanNetworkHelper.isValidVniRange(startVniInt, endVniInt)) {
                    throw new ApiMessageInterceptionException(
                            argerr("could not add huawei iMaster sdn controller with invalid vni range[%s, %s]",
                                    startVni, endVni));
                }

                validVniRange = true;
            }

            if (SdnControllerSystemTags.VLAN_RANGE.isMatch(tag)) {
                Map<String, String> vniMap = SdnControllerSystemTags.VLAN_RANGE.getTokensByTag(tag);
                String startVlan = vniMap.get(SdnControllerSystemTags.START_VLAN_TOKEN);
                String endVlan = vniMap.get(SdnControllerSystemTags.END_VLAN_TOKEN);
                if (startVlan == null || startVlan.isEmpty() || endVlan == null || endVlan.isEmpty()) {
                    throw new ApiMessageInterceptionException(
                            argerr("could not add huawei iMaster sdn controller without vlan range"));
                }

                int startVlanInt = Integer.parseInt(startVlan);
                int endVlanInt = Integer.parseInt(endVlan);
                if (!VxlanNetworkHelper.isValidVlanRange(startVlanInt, endVlanInt)) {
                    throw new ApiMessageInterceptionException(
                            argerr("could not add huawei iMaster sdn controller with invalid vlan range[%s, %s]",
                                    startVlanInt, endVlanInt));
                }

                validVlanRange = true;
            }
        }

        if (!validVlanRange) {
            throw new ApiMessageInterceptionException(
                    argerr("could not add huawei iMaster sdn controller without vni range or vlan range"));
        }

        if (!validVniRange) {
            msg.getSystemTags().add(SdnControllerSystemTags.VNI_RANGE.instantiateTag(
                    map(CollectionDSL.e(SdnControllerSystemTags.START_VNI_TOKEN, VxlanNetworkConstant.MIN_VNI),
                            CollectionDSL.e(SdnControllerSystemTags.END_VNI_TOKEN, VxlanNetworkConstant.MAX_VNI))
            ));
        }
    }

    private void validate(APICreateL3NetworkMsg msg) {
        String sdnControllerUuid = L3NetworkHelper.getSdnControllerUuidFromL2Uuid(msg.getL2NetworkUuid());
        if (sdnControllerUuid == null) {
            return;
        }

        SdnControllerVO sdnControllerVO = dbf.findByUuid(sdnControllerUuid, SdnControllerVO.class);
        if (!sdnControllerVO.getVendorType().equals(HuaweiIMasterConstant.HUAWEI_IMASTER_CONTROLLER_TYPE)) {
            return;
        }

        if (msg.getSystemTags() == null || msg.getSystemTags().isEmpty()) {
            throw new ApiMessageInterceptionException(
                    argerr("could not create huawei iMaster L3 network without logical router UUID"));
        }

        String lRouterUuid = null;
        for (String tag : msg.getSystemTags()) {
            if (L3NetworkSystemTags.L3_NETWORK_HUAWEI_LOGICAL_ROUTER.isMatch(tag)) {
                lRouterUuid = L3NetworkSystemTags.L3_NETWORK_HUAWEI_LOGICAL_ROUTER.getTokenByTag(tag,
                        L3NetworkSystemTags.L3_NETWORK_HUAWEI_LOGICAL_ROUTER_TOKEN);
            }
        }

        if (lRouterUuid == null || lRouterUuid.isEmpty()) {
            throw new ApiMessageInterceptionException(
                    argerr("could not create huawei iMaster L3 network without logical router UUID"));
        }

        // can not create 2 huawei iMaster L3 networks with the same l2 network
        boolean exist = Q.New(L3NetworkVO.class).eq(L3NetworkVO_.l2NetworkUuid, msg.getL2NetworkUuid()).isExists();
        if (exist) {
            throw new ApiMessageInterceptionException(
                    argerr("could not create huawei iMaster L3 network with l2 network[uuid:%s] because it already exists",
                            msg.getL2NetworkUuid()));
        }
    }

    private void validate(APIDeleteHuaweiIMasterVRouterMsg msg) {
        List<String> huaweiL3Uuids = Q.New(L3NetworkVO.class)
                .select(L3NetworkVO_.uuid)
                .listValues();

        if (!huaweiL3Uuids.isEmpty()) {
            for (String l3Uuid : huaweiL3Uuids) {
                String lRouterUuid = HuaweiIMasterHelper.getL3NetworkLogicalRouterId(l3Uuid);
                if (lRouterUuid == null) {
                    continue;
                }

                if (lRouterUuid.equals(msg.getUuid())) {
                    throw new ApiMessageInterceptionException(
                            argerr("could not delete huawei logical router[uuid:%s] because l3 network[uuid:%s] is attached",
                                    msg.getUuid(), l3Uuid));
                }
            }
        }

        HuaweiIMasterVRouterVO vo = dbf.findByUuid(msg.getUuid(), HuaweiIMasterVRouterVO.class);
        msg.setSdnControllerUuid(vo.getSdnControllerUuid());
    }

    private void validate(APIDeleteHuaweiIMasterTenantMsg msg) {
        HuaweiIMasterTenantVO vo = dbf.findByUuid(msg.getUuid(), HuaweiIMasterTenantVO.class);
        msg.setSdnControllerUuid(vo.getSdnControllerUuid());
    }

    private void validate(APIDeleteHuaweiIMasterVpcMsg msg) {
        HuaweiIMasterVpcVO vo = dbf.findByUuid(msg.getUuid(), HuaweiIMasterVpcVO.class);
        msg.setSdnControllerUuid(vo.getSdnControllerUuid());
    }

    private void validate(APIDeleteHuaweiIMasterFabricMsg msg) {
        HuaweiIMasterFabricVO vo = dbf.findByUuid(msg.getUuid(), HuaweiIMasterFabricVO.class);
        msg.setSdnControllerUuid(vo.getSdnControllerUuid());
    }

}
