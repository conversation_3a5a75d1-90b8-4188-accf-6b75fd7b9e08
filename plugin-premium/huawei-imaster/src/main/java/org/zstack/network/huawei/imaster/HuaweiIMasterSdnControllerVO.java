package org.zstack.network.huawei.imaster;

import org.zstack.header.tag.AutoDeleteTag;
import org.zstack.header.vo.NoView;
import org.zstack.header.network.sdncontroller.SdnControllerVO;

import javax.persistence.*;
import java.util.*;

/**
 * Created by shixin.ruan on 07/29/2025.
 */
@Entity
@Table
@PrimaryKeyJoinColumn(name="uuid", referencedColumnName="uuid")
@AutoDeleteTag
public class HuaweiIMasterSdnControllerVO extends SdnControllerVO {
    @OneToMany(fetch=FetchType.EAGER)
    @JoinColumn(name="sdnControllerUuid", insertable=false, updatable=false)
    @NoView
    private Set<HuaweiIMasterFabricVO> fabrics = new HashSet<HuaweiIMasterFabricVO>();

    @OneToMany(fetch=FetchType.EAGER)
    @JoinColumn(name="sdnControllerUuid", insertable=false, updatable=false)
    @NoView
    private Set<HuaweiIMasterTenantVO> tenants = new HashSet<HuaweiIMasterTenantVO>();

    @OneToMany(fetch=FetchType.EAGER)
    @JoinColumn(name="sdnControllerUuid", insertable=false, updatable=false)
    @NoView
    private Set<HuaweiIMasterVpcVO> vpcs = new HashSet<HuaweiIMasterVpcVO>();

    @OneToMany(fetch=FetchType.EAGER)
    @JoinColumn(name="sdnControllerUuid", insertable=false, updatable=false)
    @NoView
    private Set<HuaweiIMasterVRouterVO> vrouters = new HashSet<HuaweiIMasterVRouterVO>();

    public HuaweiIMasterSdnControllerVO() {
    }

    public HuaweiIMasterSdnControllerVO(SdnControllerVO vo) {
        super();
        if (vo != null) {
            this.setUuid(vo.getUuid());
            this.setName(vo.getName());
            this.setDescription(vo.getDescription());
            this.setVendorType(vo.getVendorType());
            this.setVendorVersion(vo.getVendorVersion());
            this.setIp(vo.getIp());
            this.setUsername(vo.getUsername());
            this.setPassword(vo.getPassword());
            this.setStatus(vo.getStatus());
            this.setCreateDate(vo.getCreateDate());
            this.setLastOpDate(vo.getLastOpDate());
            this.setAccountUuid(vo.getAccountUuid());
        }
    }

    public Set<HuaweiIMasterFabricVO> getFabrics() {
        return fabrics;
    }

    public void setFabrics(Set<HuaweiIMasterFabricVO> fabrics) {
        this.fabrics = fabrics;
    }

    public Set<HuaweiIMasterTenantVO> getTenants() {
        return tenants;
    }

    public void setTenants(Set<HuaweiIMasterTenantVO> tenants) {
        this.tenants = tenants;
    }

    public Set<HuaweiIMasterVpcVO> getVpcs() {
        return vpcs;
    }

    public void setVpcs(Set<HuaweiIMasterVpcVO> vpcs) {
        this.vpcs = vpcs;
    }

    public Set<HuaweiIMasterVRouterVO> getVrouters() {
        return vrouters;
    }

    public void setVrouters(Set<HuaweiIMasterVRouterVO> vrouters) {
        this.vrouters = vrouters;
    }
}
