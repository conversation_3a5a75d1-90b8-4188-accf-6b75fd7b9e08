package org.zstack.network.ovn;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.header.core.Completion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.core.workflow.FlowTrigger;
import org.zstack.header.core.workflow.NoRollbackFlow;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.network.l2.L2NetworkInventory;
import org.zstack.header.network.l2.L2NetworkVO;
import org.zstack.header.network.l2.L2NetworkVO_;
import org.zstack.header.network.sdncontroller.SdnControllerVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.zstack.sdnController.header.SdnControllerFlowDataParam.*;

@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class OvnSyncL2NetworkFlow extends NoRollbackFlow {

    @Autowired
    DatabaseFacade dbf;

    @Override
    public void run(FlowTrigger trigger, Map data) {
        String controllerUuid = (String)data.get(SDN_CONTROLLER_UUID);
        SdnControllerVO vo = dbf.findByUuid(controllerUuid, SdnControllerVO.class);
        OvnController controller = new OvnController(vo);

        List<String> l2Uuids = controller.getL2NetworkOfSdnController();

        if (l2Uuids.isEmpty()) {
            controller.syncL2Network(new ArrayList<>(), new Completion(trigger) {
                @Override
                public void success() {
                    trigger.next();
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    trigger.fail(errorCode);
                }
            });
            return;
        }

        int step = 100;
        int size = l2Uuids.size();
        int count = size / 100;
        if (size - count * 100 > 0) {
            count++;
        }

        List<List<String>> batchL2Uuids = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            int end = (i + 1) * step;
            List<String> vos = l2Uuids.subList(i * step, Math.min(end, l2Uuids.size()));
            batchL2Uuids.add(vos);
        }

        new While<>(batchL2Uuids).each((uuids, wc) -> {
            List<L2NetworkVO> vos = Q.New(L2NetworkVO.class)
                    .in(L2NetworkVO_.uuid, uuids).list();
            controller.syncL2Network(L2NetworkInventory.valueOf(vos), new Completion(wc) {
                @Override
                public void success() {
                    wc.done();
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    wc.addError(errorCode);
                    wc.allDone();
                }
            });
        }).run(new WhileDoneCompletion(trigger) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (!errorCodeList.getCauses().isEmpty()) {
                    trigger.fail(errorCodeList.getCauses().get(0));
                    return;
                }

                trigger.next();
            }
        });
    }
}
