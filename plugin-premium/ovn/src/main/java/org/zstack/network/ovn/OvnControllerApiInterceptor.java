package org.zstack.network.ovn;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.ApiMessageInterceptor;
import org.zstack.header.apimediator.GlobalApiMessageInterceptor;
import org.zstack.header.message.APIMessage;
import org.zstack.header.network.l2.APICreateL2NoVlanNetworkMsg;
import org.zstack.header.network.l2.APICreateL2VlanNetworkMsg;
import org.zstack.header.network.l2.L2NetworkConstant;
import org.zstack.network.securitygroup.APIAttachSecurityGroupToL3NetworkMsg;
import org.zstack.network.securitygroup.SecurityGroupHelper;
import org.zstack.header.network.sdncontroller.SdnControllerStatus;
import org.zstack.header.network.sdncontroller.SdnControllerVO;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.ArrayList;
import java.util.List;

import static org.zstack.core.Platform.argerr;

/**
 * Created by shixin.ruan on 01/03/2025
 */
public class OvnControllerApiInterceptor implements ApiMessageInterceptor, GlobalApiMessageInterceptor {
    private static final CLogger logger = Utils.getLogger(OvnControllerApiInterceptor.class);

    @Autowired
    protected DatabaseFacade dbf;
    @Autowired
    protected CloudBus bus;

    private void setServiceId(APIMessage msg) {
    }

    public List<Class> getMessageClassToIntercept() {
        List<Class> ret = new ArrayList<>();
        ret.add(APICreateL2NoVlanNetworkMsg.class);
        ret.add(APICreateL2VlanNetworkMsg.class);
        ret.add(APIAttachSecurityGroupToL3NetworkMsg.class);

        return ret;
    }

    public InterceptorPosition getPosition() {
        return InterceptorPosition.END;
    }

    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        if (msg instanceof APICreateL2NoVlanNetworkMsg) {
            validate((APICreateL2NoVlanNetworkMsg) msg);
        } else if (msg instanceof APICreateL2VlanNetworkMsg) {
            validate((APICreateL2VlanNetworkMsg) msg);
        } else if (msg instanceof APIAttachSecurityGroupToL3NetworkMsg) {
            validate((APIAttachSecurityGroupToL3NetworkMsg) msg);
        }

        return msg;
    }

    private void validate(APICreateL2NoVlanNetworkMsg msg) {
        if (!msg.getvSwitchType().equals(L2NetworkConstant.VSWITCH_TYPE_OVN_DPDK)) {
            return;
        }

        /* ovn network doesn't need this field, but db L2NetworkEO need this filed */
        if (msg.getPhysicalInterface() == null) {
            msg.setPhysicalInterface("");
        }
    }

    private void validate(APICreateL2VlanNetworkMsg msg) {
        if (!msg.getvSwitchType().equals(L2NetworkConstant.VSWITCH_TYPE_OVN_DPDK)) {
            return;
        }

        /* ovn network doesn't need this field, but db L2NetworkEO need this filed */
        if (msg.getPhysicalInterface() == null) {
            msg.setPhysicalInterface("");
        }
    }

    private void validate(APIAttachSecurityGroupToL3NetworkMsg msg) {
        String sdnControllerUuid = SecurityGroupHelper.getSdnControllerUuid(msg.getSecurityGroupUuid());
        if (sdnControllerUuid == null) {
            return;
        }

        SdnControllerVO vo = dbf.findByUuid(sdnControllerUuid, SdnControllerVO.class);
        if (vo == null) {
            throw new ApiMessageInterceptionException(argerr("could not attach l3 network to securityGroup, " +
                    "because sdn controller[uuid:%s] is not found", sdnControllerUuid));
        }

        if (!vo.getVendorType().equals(OvnControllerConstant.OVN_CONTROLLER_TYPE)) {
            return;
        }

        if (vo.getStatus() != SdnControllerStatus.Connected) {
            throw new ApiMessageInterceptionException(argerr("could not attach l3 network to securityGroup, " +
                    "because sdn controller[uuid:%s] is not connected[status:%s]",
                    sdnControllerUuid, vo.getStatus().toString()));
        }
    }
}
