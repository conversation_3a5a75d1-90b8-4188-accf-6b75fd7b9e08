package org.zstack.network.ovn;

import org.zstack.header.tag.TagDefinition;
import org.zstack.header.network.sdncontroller.SdnControllerVO;
import org.zstack.tag.PatternedSystemTag;

@TagDefinition
public class OvnControllerSystemTags {
    public static String OVN_NORTHD_PORT_TOKEN = "northdPort";
    public static PatternedSystemTag OVN_NORTHD_PORT = new PatternedSystemTag(String.format("northdPort::{%s}", OVN_NORTHD_PORT_TOKEN), SdnControllerVO.class);

    public static String OVN_NORTHD_PROTO_TOKEN = "northdProto";
    public static PatternedSystemTag OVN_NORTHD_PROTO = new PatternedSystemTag(String.format("northdProto::{%s}", OVN_NORTHD_PROTO_TOKEN), SdnControllerVO.class);

    public static String OVN_ENCAP_TYPE_TOKEN = "ovnEncapType";
    public static PatternedSystemTag OVN_ENCAP_TYPE = new PatternedSystemTag(String.format("ovnEncapType::{%s}", OVN_ENCAP_TYPE_TOKEN), SdnControllerVO.class);
}
