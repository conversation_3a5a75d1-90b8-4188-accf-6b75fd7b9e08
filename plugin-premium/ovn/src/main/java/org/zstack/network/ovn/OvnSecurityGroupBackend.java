package org.zstack.network.ovn;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.http.HttpMethod;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.header.core.Completion;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.message.MessageReply;
import org.zstack.header.network.l2.L2NetworkConstant;
import org.zstack.header.network.l2.L2NetworkVO;
import org.zstack.header.vm.VmInstanceConstant;
import org.zstack.header.vm.VmInstanceState;
import org.zstack.header.vm.VmNicVO;
import org.zstack.identity.AccountManager;
import org.zstack.network.l2.L2NetworkSystemTags;
import org.zstack.network.ovn.OvnControllerCommands.*;
import org.zstack.network.securitygroup.*;
import org.zstack.header.network.sdncontroller.SdnControllerVO;
import org.zstack.utils.TagUtils;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.ArrayList;
import java.util.List;

import static org.zstack.utils.CollectionDSL.*;

@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class OvnSecurityGroupBackend implements SecurityGroupSdnBackend {
    private static final CLogger logger = Utils.getLogger(OvnSecurityGroupBackend.class);

    @Autowired
    CloudBus bus;
    @Autowired
    DatabaseFacade dbf;
    @Autowired
    SecurityGroupManager sgMgr;
    @Autowired
    private AccountManager acntMgr;

    private SdnControllerVO self;
    private OvnControllerHelper ovnHelper = new OvnControllerHelper();

    public OvnSecurityGroupBackend(SdnControllerVO self) {
        this.self = self;
    }

    @Override
    public void createSecurityGroup(SecurityGroupInventory sg, Completion completion) {
        completion.success();
    }

    @Override
    public void updateSecurityGroup(VmNicSecurityGroupTo to, Completion completion) {
        refreshVmNicSecurityGroup(to, completion);
    }

    @Override
    public List<VmNicVO> getCandidateVmNic(String sgId, String accountUuid) {
        List<String> nicUuidsToInclude = acntMgr.getResourceUuidsCanAccessByAccount(accountUuid, VmNicVO.class);
        if (nicUuidsToInclude != null && nicUuidsToInclude.isEmpty()) {
            return new ArrayList<VmNicVO>();
        }

        List<String> nicUuidsToExclued = Q.New(VmNicSecurityGroupRefVO.class).select(VmNicSecurityGroupRefVO_.vmNicUuid).eq(VmNicSecurityGroupRefVO_.securityGroupUuid, sgId).listValues();

        List<VmNicVO> candidateNics = new ArrayList<>();
        List<VmNicVO> allNics = SQL.New("select nic from VmNicVO nic, " +
                        " VmInstanceVO vm, L3NetworkVO l3, L2NetworkVO l2, SystemTagVO stag" +
                        " where nic.vmInstanceUuid = vm.uuid" +
                        " and nic.l3NetworkUuid = l3.uuid " +
                        " and l3.l2NetworkUuid = l2.uuid " +
                        " and l2.vSwitchType = :vswitchType " +
                        " and stag.resourceType=:ttype and stag.tag=:tag and stag.resourceUuid=l2.uuid" +
                        " and vm.type = :vmType" +
                        " and vm.state in (:vmStates)", VmNicVO.class)
                .param("vmType", VmInstanceConstant.USER_VM_TYPE)
                .param("vswitchType", L2NetworkConstant.VSWITCH_TYPE_OVN_DPDK)
                .param("ttype", L2NetworkVO.class.getSimpleName())
                .param("tag", TagUtils.tagPatternToSqlPattern(L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID.instantiateTag(
                        map(e(L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID_TOKEN, self.getUuid())))))
                .param("vmStates", list(VmInstanceState.Running, VmInstanceState.Stopped))
                .list();

        if (allNics.isEmpty()) {
            return allNics;
        }

        if (!nicUuidsToExclued.isEmpty()) {
            if (nicUuidsToInclude != null && !nicUuidsToInclude.isEmpty()) {
                // accessed by a normal account
                allNics.stream().forEach(nic -> {
                    if (!nicUuidsToExclued.contains(nic.getUuid()) && nicUuidsToInclude.contains(nic.getUuid())) {
                        candidateNics.add(nic);
                    }
                });
            } else {
                // accessed by an admin
                allNics.stream().forEach(nic -> {
                    if (!nicUuidsToExclued.contains(nic.getUuid())) {
                        candidateNics.add(nic);
                    }
                });
            }
        } else {
            if (nicUuidsToInclude != null && !nicUuidsToInclude.isEmpty()) {
                // accessed by a normal account
                allNics.stream().forEach(nic -> {
                    if (nicUuidsToInclude.contains(nic.getUuid())) {
                        candidateNics.add(nic);
                    }
                });
            } else {
                // accessed by an admin
                return allNics;
            }
        }

        return candidateNics;
    }


    private void refreshVmNicSecurityGroup(VmNicSecurityGroupTo groupTo,  Completion completion) {
        VmNicRefreshSecurityGroupCmd cmd = new VmNicRefreshSecurityGroupCmd(self);
        cmd.setVmNicTOs(groupTo.getVmNics());
        cmd.setGroups(groupTo.getGroups());
        cmd.setOvnMaxPriority(SecurityGroupGlobalConfig.OVN_ACL_MAX_PRIORITY.value(Integer.class));
        cmd.setSecurityGroupRulesMaxNum(SecurityGroupGlobalConfig.SECURITY_GROUP_RULES_NUM_LIMIT.value(Integer.class));

        OvnControllerAsyncHttpCallMsg cmsg = new OvnControllerAsyncHttpCallMsg();
        cmsg.setCommand(cmd);
        cmsg.setMethod(HttpMethod.POST);
        cmsg.setPath(OvnControllerCommands.OVN_SECURITY_GROUP_PATH);
        cmsg.setOvnControllerUuid(self.getUuid());
        cmsg.setCheckStatus(true);
        bus.makeTargetServiceIdByResourceUuid(cmsg, OvnControllerConstant.SERVICE_ID, self.getUuid());
        bus.send(cmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    completion.fail(reply.getError());
                    return;
                }

                OvnControllerAsyncHttpCallReply re = reply.castReply();
                VmNicRefreshSecurityGroupRsp rsp = re.toResponse(VmNicRefreshSecurityGroupRsp.class);
                if (rsp.isSuccess()) {
                    logger.debug(String.format("successfully refresh security group rules for ovn controller[uuid:%s, ip:%s]",
                            self.getUuid(), self.getIp()));
                    completion.success();
                } else {
                    completion.fail(ErrorCode.fromString(String.format("failed to refresh security group rules for ovn controller[uuid:%s, ip:%s], because %s",
                            self.getUuid(), self.getIp(), rsp.getError())));
                }
            }
        });
    }
}
