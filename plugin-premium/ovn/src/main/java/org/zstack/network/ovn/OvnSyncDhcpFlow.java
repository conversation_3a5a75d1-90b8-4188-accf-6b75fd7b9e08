package org.zstack.network.ovn;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.header.core.Completion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.core.workflow.FlowTrigger;
import org.zstack.header.core.workflow.NoRollbackFlow;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.network.l2.L2NetworkConstant;
import org.zstack.header.network.l2.L2NetworkVO;
import org.zstack.header.network.l3.L3NetworkInventory;
import org.zstack.header.network.l3.L3NetworkVO;
import org.zstack.network.l2.L2NetworkSystemTags;
import org.zstack.header.network.sdncontroller.SdnControllerVO;
import org.zstack.utils.TagUtils;

import javax.persistence.TypedQuery;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.zstack.sdnController.header.SdnControllerFlowDataParam.SDN_CONTROLLER_UUID;
import static org.zstack.utils.CollectionDSL.e;
import static org.zstack.utils.CollectionDSL.map;

@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class OvnSyncDhcpFlow extends NoRollbackFlow {

    @Autowired
    DatabaseFacade dbf;

    @Override
    public void run(FlowTrigger trigger, Map data) {
        String controllerUuid = (String)data.get(SDN_CONTROLLER_UUID);

        String sql = "select l3 from L3NetworkVO l3, L2NetworkVO l2, SystemTagVO stag where " +
                "l3.l2NetworkUuid=l2.uuid and l2.vSwitchType=:vSwitchType" +
                " and stag.resourceType=:ttype and stag.tag=:tag and stag.resourceUuid=l2.uuid";
        TypedQuery<L3NetworkVO> q = dbf.getEntityManager().createQuery(sql, L3NetworkVO.class);
        q.setParameter("vSwitchType", L2NetworkConstant.VSWITCH_TYPE_OVN_DPDK);
        q.setParameter("ttype", L2NetworkVO.class.getSimpleName());
        q.setParameter("tag", TagUtils.tagPatternToSqlPattern(L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID.instantiateTag(
                map(e(L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID_TOKEN, controllerUuid)))));

        List<L3NetworkVO> l3Vos = q.getResultList();
        l3Vos = l3Vos.stream().filter(L3NetworkVO::enableIpAddressAllocation).collect(Collectors.toList());

        SdnControllerVO vo = dbf.findByUuid(controllerUuid, SdnControllerVO.class);
        OvnControllerDhcp controller = new OvnControllerDhcp(vo);

        if (l3Vos.isEmpty()) {
            controller.enableDhcp(new ArrayList<>(), true, new Completion(trigger) {
                @Override
                public void success() {
                    trigger.next();
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    trigger.fail(errorCode);
                }
            });

            return;
        }

        int step = 100;
        int size = l3Vos.size();
        int count = size / 100;
        if (size - count * 100 > 0) {
            count++;
        }

        List<List<L3NetworkVO>> batchL3Vos = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            int end = (i + 1) * step;
            List<L3NetworkVO> vos = l3Vos.subList(i * step, Math.min(end, l3Vos.size()));
            batchL3Vos.add(vos);
        }

        new While<>(batchL3Vos).each((vos, wc) -> {
            controller.enableDhcp(L3NetworkInventory.valueOf(vos), true, new Completion(wc) {
                @Override
                public void success() {
                    wc.done();
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    wc.addError(errorCode);
                    wc.allDone();
                }
            });
        }).run(new WhileDoneCompletion(trigger) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (!errorCodeList.getCauses().isEmpty()) {
                    trigger.fail(errorCodeList.getCauses().get(0));
                    return;
                }

                trigger.next();
            }
        });
    }
}
