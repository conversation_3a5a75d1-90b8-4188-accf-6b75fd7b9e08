package org.zstack.network.ovn;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.header.apimediator.ApiMessageInterceptionException;
import org.zstack.header.apimediator.ApiMessageInterceptor;
import org.zstack.header.apimediator.GlobalApiMessageInterceptor;
import org.zstack.header.host.*;
import org.zstack.header.message.APIMessage;
import org.zstack.header.network.l2.*;
import org.zstack.header.network.l3.APICreateL3NetworkMsg;
import org.zstack.header.network.l3.L3NetworkCategory;
import org.zstack.header.network.l3.L3NetworkConstant;
import org.zstack.header.network.l3.L3NetworkVO;
import org.zstack.header.network.sdncontroller.SdnControllerHostRefVO;
import org.zstack.header.network.sdncontroller.SdnControllerVO;
import org.zstack.header.network.sdncontroller.SdnControllerVO_;
import org.zstack.header.network.service.APIAttachNetworkServiceToL3NetworkMsg;
import org.zstack.header.network.service.NetworkServiceProviderVO;
import org.zstack.header.network.service.NetworkServiceProviderVO_;
import org.zstack.header.vm.APIAttachL3NetworkToVmMsg;
import org.zstack.header.vm.VmInstanceVO;
import org.zstack.header.vm.VmNicVO;
import org.zstack.kvm.KVMConstant;
import org.zstack.network.hostNetworkInterface.HostNetworkInterfaceVO;
import org.zstack.network.hostNetworkInterface.HostNetworkInterfaceVO_;
import org.zstack.network.l2.L2NetworkSystemTags;
import org.zstack.network.l2.vxlan.vtep.VtepVO;
import org.zstack.network.l2.vxlan.vtep.VtepVO_;
import org.zstack.network.l3.L3NetworkHelper;
import org.zstack.network.securitygroup.*;
import org.zstack.pciDevice.virtual.PciDeviceVirtStatus;
import org.zstack.sdnController.header.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.zstack.core.Platform.argerr;
import static org.zstack.core.Platform.operr;

public class OvnApiInterceptor implements ApiMessageInterceptor, GlobalApiMessageInterceptor {
    @Autowired
    DatabaseFacade dbf;
    @Autowired
    CloudBus bus;
    @Autowired
    private PluginRegistry pluginRgty;

    @Override
    public List<Class> getMessageClassToIntercept() {
        List<Class> ret = new ArrayList<Class>();
        ret.add(APIAttachL3NetworkToVmMsg.class);
        ret.add(APICreateOvnControllerVmMsg.class);
        ret.add(APICreateOvnControllerOfferingMsg.class);
        ret.add(APISdnControllerAddHostMsg.class);
        ret.add(APIAddSdnControllerMsg.class);
        ret.add(APIRemoveSdnControllerMsg.class);
        ret.add(APISdnControllerChangeHostMsg.class);
        ret.add(APICreateL2NetworkMsg.class);
        ret.add(APICreateL3NetworkMsg.class);
        ret.add(APIAttachNetworkServiceToL3NetworkMsg.class);
        ret.add(APICreateSecurityGroupMsg.class);
        ret.add(APIDeleteHostMsg.class);
        ret.add(APIAttachSecurityGroupToL3NetworkMsg.class);
        ret.add(APIAddVmNicToSecurityGroupMsg.class);
        return ret;
    }

    @Override
    public InterceptorPosition getPosition() {
        return InterceptorPosition.END;
    }

    private void setServiceId(APIMessage msg) {
        if (msg instanceof SdnControllerMessage) {
            SdnControllerMessage smsg = (SdnControllerMessage) msg;
            bus.makeTargetServiceIdByResourceUuid(msg, OvnControllerConstant.SERVICE_ID, smsg.getSdnControllerUuid());
        }
    }

    @Override
    public APIMessage intercept(APIMessage msg) throws ApiMessageInterceptionException {
        if (msg instanceof APICreateOvnControllerVmMsg) {
            validate((APICreateOvnControllerVmMsg)msg);
        } else if (msg instanceof APICreateOvnControllerOfferingMsg) {
            validate((APICreateOvnControllerOfferingMsg)msg);
        } else if (msg instanceof APIAttachL3NetworkToVmMsg) {
            validate((APIAttachL3NetworkToVmMsg)msg);
        } else if (msg instanceof APISdnControllerAddHostMsg) {
            validate((APISdnControllerAddHostMsg) msg);
        } else if (msg instanceof APIAddSdnControllerMsg) {
            validate((APIAddSdnControllerMsg) msg);
        } else if (msg instanceof APIRemoveSdnControllerMsg) {
            validate((APIRemoveSdnControllerMsg) msg);
        } else if (msg instanceof APISdnControllerChangeHostMsg) {
            validate((APISdnControllerChangeHostMsg) msg);
        } else if (msg instanceof APICreateL2NetworkMsg) {
            validate((APICreateL2NetworkMsg) msg);
        } else if (msg instanceof APIAttachNetworkServiceToL3NetworkMsg) {
            validate((APIAttachNetworkServiceToL3NetworkMsg) msg);
        } else if (msg instanceof APICreateSecurityGroupMsg) {
            validate((APICreateSecurityGroupMsg) msg);
        } else if (msg instanceof APIDeleteHostMsg) {
            validate((APIDeleteHostMsg) msg);
        } else if (msg instanceof APICreateL3NetworkMsg) {
            validate((APICreateL3NetworkMsg) msg);
        } else if (msg instanceof APIAttachSecurityGroupToL3NetworkMsg) {
            validate((APIAttachSecurityGroupToL3NetworkMsg)msg);
        } else if (msg instanceof APIAddVmNicToSecurityGroupMsg) {
            validate((APIAddVmNicToSecurityGroupMsg) msg);
        }

        return msg;
    }

    /**
     * 验证两个SDN控制器是否都是ovn控制器
     *
     * 业务规则：
     * 1. 两者都为null → true
     * 2. 一个为null，一个不为null且是OVN控制器 → fasle
     * 3. 两个都不为null，且都不是OVN控制器 → ture
     * 4. 两个都不为null，一个是OVN一个不是 → false
     * 5. 两个都不为null，都是OVN但UUID不同 → false
     *
     * @param controllerUuid1 第一个SDN控制器
     * @param controllerUuid2 第二个SDN控制器
     */
    private boolean isSameOvnControllers(String controllerUuid1, String controllerUuid2) {
        // 规则1：两者都为null
        if (controllerUuid1 == null && controllerUuid2 == null) {
            return true;
        }

        // 处理一个为null的情况
        if (controllerUuid1 == null || controllerUuid2 == null) {
            String nonNullUuid = controllerUuid1 != null ? controllerUuid1 : controllerUuid2;
            // 规则2
            if (isOvnController(nonNullUuid)) {
                return false;
            } else {
                return true;
            }
        }

        // 以下都是两个控制器都不为null的情况
        boolean isController1Ovn = isOvnController(controllerUuid1);
        boolean isController2Ovn = isOvnController(controllerUuid2);

        // 规则3：如果都不是OVN控制器，直接返回
        if (!isController1Ovn && !isController2Ovn) {
            return true;
        }

        // 规则4：如果一个是OVN一个不是
        if (isController1Ovn != isController2Ovn) { // 规则3已经排除了两个都为false的情况
            return false;
        }

        // 规则5：两个都是OVN但UUID不同
        return controllerUuid1.equals(controllerUuid2);
    }


    private boolean isOvnController(String controllerUuid) {
        SdnControllerVO vo = dbf.findByUuid(controllerUuid, SdnControllerVO.class);
        return vo != null && OvnControllerConstant.OVN_CONTROLLER_TYPE.equals(vo.getVendorType());
    }

    private void validate(APIAttachSecurityGroupToL3NetworkMsg msg) {
        String l3SdnControllerUuid = L3NetworkHelper.getSdnControllerUuidFromL3Uuid(msg.getL3NetworkUuid());
        String sgSdnControllerUuid = SecurityGroupHelper.getSdnControllerUuid(msg.getSecurityGroupUuid());

        if (!isSameOvnControllers(l3SdnControllerUuid, sgSdnControllerUuid)) {
            throw new ApiMessageInterceptionException(argerr("could not attach l3 network to securityGroup, " +
                            "because l3 network sdn [uuid:%s] is different from security group sdn [uuid:%s]",
                    l3SdnControllerUuid, sgSdnControllerUuid));
        }
    }

    private void validate(APIAddVmNicToSecurityGroupMsg msg) {
        String sgSdnControllerUuid = SecurityGroupHelper.getSdnControllerUuid(msg.getSecurityGroupUuid());
        for (String uuid : msg.getVmNicUuids()) {
            VmNicVO nicVO = dbf.findByUuid(uuid, VmNicVO.class);
            String l3SdnControllerUuid = L3NetworkHelper.getSdnControllerUuidFromL3Uuid(nicVO.getL3NetworkUuid());

            if (!isSameOvnControllers(sgSdnControllerUuid, l3SdnControllerUuid)) {
                throw new ApiMessageInterceptionException(argerr("could not add vm nic to securityGroup, " +
                                "because vm nic sdn [uuid:%s] is different from security group sdn [uuid:%s]",
                        l3SdnControllerUuid, sgSdnControllerUuid));
            }

        }
    }

    private void validate(APICreateL3NetworkMsg msg) {
        // TODO just for ZSTAC-74439
        String sdnControllerUuid = L3NetworkHelper.getSdnControllerUuidFromL2Uuid(msg.getL2NetworkUuid());
        if (sdnControllerUuid == null) {
            return;
        }

        SdnControllerVO sdnControllerVO = dbf.findByUuid(sdnControllerUuid, SdnControllerVO.class);
        if (!sdnControllerVO.getVendorType().equals(OvnControllerConstant.OVN_CONTROLLER_TYPE)) {
            return;
        }

        // ovn only has flat network
        if (msg.getCategory().equals(L3NetworkCategory.Private.toString())
                && msg.getType().equals(L3NetworkConstant.L3_BASIC_NETWORK_TYPE)) {
            return;
        }

        throw new ApiMessageInterceptionException(argerr("can not create l3 network" +
                "because ovn does not support l3[type:%s, category:%s]", msg.getType(), msg.getCategory()));
    }

    private void validate(APIDeleteHostMsg msg) {
        long count = Q.New(OvnControllerVmInstanceVO.class)
                .eq(OvnControllerVmInstanceVO_.hostUuid, msg.getHostUuid()).count();
        if (count > 0) {
            throw new ApiMessageInterceptionException(argerr("can not delete host" +
                    "because there are %d ovn controller instances on the host", count));
        }
    }

    private void validate(APICreateSecurityGroupMsg msg) {
        if (msg.getvSwitchType() == null || !msg.getvSwitchType().equals(L2NetworkConstant.VSWITCH_TYPE_OVN_DPDK)) {
            return;
        }

        String ovnControllerUuid = null;
        if (msg.getSystemTags() != null) {
            for (String systag : msg.getSystemTags()) {
                if (SecurityGroupSystemTags.SDN_CONTROLLER_UUID.isMatch(systag)) {
                    ovnControllerUuid = SecurityGroupSystemTags.SDN_CONTROLLER_UUID.getTokenByTag(
                            systag, SecurityGroupSystemTags.SDN_CONTROLLER_UUID_TOKEN);
                }
            }
        }

        if (ovnControllerUuid == null) {
            throw new ApiMessageInterceptionException(argerr("can not create security group " +
                    "because ovn controller is specified"));
        }

        SdnControllerVO ovnController = Q.New(SdnControllerVO.class)
                .eq(SdnControllerVO_.uuid, ovnControllerUuid)
                .eq(SdnControllerVO_.vendorType, OvnControllerConstant.OVN_CONTROLLER_TYPE)
                .find();
        if (ovnController == null) {
            throw new ApiMessageInterceptionException(argerr("can not create security group " +
                    "because ovn controller[uuid:%s, type: %s] is not found", ovnControllerUuid, OvnControllerConstant.OVN_CONTROLLER_TYPE));
        }
    }

    private void validate(APIAttachNetworkServiceToL3NetworkMsg msg) {
        L3NetworkVO l3Vo = dbf.findByUuid(msg.getL3NetworkUuid(), L3NetworkVO.class);
        L2NetworkVO l2Vo = dbf.findByUuid(l3Vo.getL2NetworkUuid(), L2NetworkVO.class);
        if (!l2Vo.getvSwitchType().equals(L2NetworkConstant.VSWITCH_TYPE_OVN_DPDK)) {
            for (Map.Entry<String, List<String>> entry : msg.getNetworkServices().entrySet()) {
                String serviceType = Q.New(NetworkServiceProviderVO.class)
                        .select(NetworkServiceProviderVO_.type)
                        .eq(NetworkServiceProviderVO_.uuid, entry.getKey()).findValue();
                if (serviceType.equals(OvnControllerConstant.OVN_NETWORK_SERVICE_TYPE_STRING)) {
                    throw new ApiMessageInterceptionException(argerr("can not attach ovn network service " +
                            "to network with vSwitchType[%s]", l2Vo.getvSwitchType()));
                }
            }

            return;
        }

        for (Map.Entry<String, List<String>> entry : msg.getNetworkServices().entrySet()) {
            String serviceType = Q.New(NetworkServiceProviderVO.class)
                    .select(NetworkServiceProviderVO_.type)
                    .eq(NetworkServiceProviderVO_.uuid, entry.getKey()).findValue();
            if (!serviceType.equals(OvnControllerConstant.OVN_NETWORK_SERVICE_TYPE_STRING)) {
                throw new ApiMessageInterceptionException(argerr("can not attach network service[%s] " +
                        "to ovn network", serviceType));
            }
        }
    }

    private void validate(APICreateL2NetworkMsg msg) {
        if (!msg.getvSwitchType().equals(L2NetworkConstant.VSWITCH_TYPE_OVN_DPDK)) {
            return;
        }

        String sdnControllerUuid = null;
        for (String systag : msg.getSystemTags()) {
            if (L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID.isMatch(systag)) {
                sdnControllerUuid = L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID.getTokenByTag(
                        systag, L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID_TOKEN);
            }
        }

        if (sdnControllerUuid == null) {
            throw new ApiMessageInterceptionException(argerr("can not create sdn l2 network, " +
                    "because it doesn't have sdn controller uuid"));
        }
    }

    private void validate(APIAddSdnControllerMsg msg) {
        if (!msg.getVendorType().equals(OvnControllerConstant.OVN_CONTROLLER_TYPE)) {
            return;
        }

        if (msg.getSystemTags() == null) {
            msg.setSystemTags(new ArrayList<>());
        }

        if (msg.getUserName() == null) {
            msg.setUserName(OvnControllerConstant.OVN_CONTROLLER_DEFAULT_USER);
        }

        if (msg.getPassword() == null) {
            msg.setPassword(OvnControllerConstant.OVN_CONTROLLER_DEFAULT_PASSWORD);
        }
    }

    private void validate(APIRemoveSdnControllerMsg msg) {
    }

    private void validateSdnHostPhysicalNic(List<String> nicNames,
                                            String hostUuid,
                                            String bondMode) {
        HostVO hostVO = dbf.findByUuid(hostUuid, HostVO.class);
        List<String> attachedL2Networks = Q.New(L2NetworkClusterRefVO.class)
                .eq(L2NetworkClusterRefVO_.clusterUuid, hostVO.getClusterUuid())
                .select(L2NetworkClusterRefVO_.l2NetworkUuid).listValues();

        for (String nicName : nicNames) {
            if (StringUtils.isEmpty(nicName)) {
                continue;
            }

            HostNetworkInterfaceVO phyNic = Q.New(HostNetworkInterfaceVO.class)
                    .eq(HostNetworkInterfaceVO_.interfaceName, nicName)
                    .eq(HostNetworkInterfaceVO_.hostUuid, hostUuid)
                    .find();
            if (phyNic == null) {
                throw new ApiMessageInterceptionException(argerr("can not add sdn host, " +
                        "because nic[%s] is not found", nicName));
            }

            if (phyNic.getInterfaceType().equals(NetworkInterfaceType.bondingSlave.toString())) {
                throw new ApiMessageInterceptionException(argerr("can not add sdn host, " +
                        "because nic[%s]'s type [%s] is used for linux bond", nicName));
            }

            if (phyNic.getVirtStatus().equals(PciDeviceVirtStatus.SRIOV_VIRTUALIZED.toString()) ) {
                throw new ApiMessageInterceptionException(argerr("can not add sdn host, " +
                        "because nic[%s] is in sriov virtualized state", nicName));
            }

            if (!StringUtils.isEmpty(phyNic.getIpAddresses())) {
                throw new ApiMessageInterceptionException(argerr("can not add sdn host, " +
                        "because nic[%s] has Ip address[%s]", nicName));
            }

            if (!attachedL2Networks.isEmpty()) {
                /* its can not be used for other vSwitchType */
                String l2Name = Q.New(L2NetworkVO.class).eq(L2NetworkVO_.physicalInterface, nicName)
                        .notEq(L2NetworkVO_.vSwitchType, L2NetworkConstant.VSWITCH_TYPE_OVN_DPDK)
                        .in(L2NetworkVO_.uuid, attachedL2Networks)
                        .select(L2NetworkVO_.name).limit(1).findValue();
                if (l2Name != null) {
                    throw new ApiMessageInterceptionException(argerr("can not add sdn host, " +
                            "because nic[%s] has been attached to l2 network[name:%s]", nicName, l2Name));
                }

                if (Q.New(VtepVO.class).eq(VtepVO_.hostUuid, hostUuid)
                        .eq(VtepVO_.physicalInterface, nicName).isExists()) {
                    throw new ApiMessageInterceptionException(argerr("can not add sdn host, " +
                            "because nic[%s] is used for vxlan vtep interface", nicName));
                }
            }
        }

        if (nicNames.size() > 1 && bondMode != null) {
            if (!bondMode.equals(L2NetworkConstant.BONDING_MODE_AB)
                    && !bondMode.equals(L2NetworkConstant.BONDING_MODE_SLB)
                    && !bondMode.equals(L2NetworkConstant.BONDING_MODE_TCP)) {
                throw new ApiMessageInterceptionException(argerr("can not add sdn host, " +
                        "because bond mode[%s] is not supported by ovn", bondMode));
            }
        }
    }

    private void validate(APISdnControllerAddHostMsg msg) {
        SdnControllerVO sdnControllerVO = dbf.findByUuid(msg.getSdnControllerUuid(), SdnControllerVO.class);
        if (!sdnControllerVO.getVendorType().equals(OvnControllerConstant.OVN_CONTROLLER_TYPE)) {
            return;
        }

        HostVO hostVO = Q.New(HostVO.class).eq(HostVO_.uuid, msg.getHostUuid()).find();
        if (!hostVO.getHypervisorType().equals(KVMConstant.KVM_HYPERVISOR_TYPE)) {
            throw new ApiMessageInterceptionException(argerr("can not add sdn host, " +
                    "because only kvm host support ovs dpdk"));
        }

        if (StringUtils.isEmpty(msg.getVtepIp())) {
            throw new ApiMessageInterceptionException(argerr("can not add sdn host, " +
                    "because vtepip is not set"));
        }

        if (StringUtils.isEmpty(msg.getNetmask())) {
            throw new ApiMessageInterceptionException(argerr("can not add sdn host, " +
                    "because netmask is not set"));
        }

        validateSdnHostPhysicalNic(msg.getNicNames(), msg.getHostUuid(), msg.getBondMode());

        for(HostHugepageExtensionPoint extp : pluginRgty.getExtensionList(HostHugepageExtensionPoint.class)) {
            if (!extp.checkHugepageSupport(HostInventory.valueOf(hostVO))) {
                throw new ApiMessageInterceptionException(argerr("the host[uuid:%s] which in cluster[uuid:%s] does not enable hugepage," +
                        " it is not allowed to add to sdn controller", msg.getHostUuid(), hostVO.getClusterUuid()));
            }
        }
    }

    private void validate(APISdnControllerChangeHostMsg msg) {
        SdnControllerVO sdnControllerVO = dbf.findByUuid(msg.getSdnControllerUuid(), SdnControllerVO.class);
        if (!sdnControllerVO.getVendorType().equals(OvnControllerConstant.OVN_CONTROLLER_TYPE)) {
            return;
        }

        if (msg.getNicNames() != null) {
            validateSdnHostPhysicalNic(msg.getNicNames(), msg.getHostUuid(), msg.getBondMode());
        }
    }

    private void validate(APICreateOvnControllerOfferingMsg msg) {
        msg.setType(OvnControllerConstant.OVN_CONTROLLER_APPLIANCE_VM_TYPE);
    }

    private void validate(APICreateOvnControllerVmMsg msg) {
    }

    private void validate(APIAttachL3NetworkToVmMsg msg) {
        String sdnControlerUuid = L3NetworkHelper.getSdnControllerUuidFromL3Uuid(msg.getL3NetworkUuid());
        if (sdnControlerUuid == null) {
            return;
        }

        SdnControllerVO controllerVO = dbf.findByUuid(sdnControlerUuid, SdnControllerVO.class);
        if (controllerVO == null) {
            throw new ApiMessageInterceptionException(argerr("could not attach l3network to vm, " +
                    "because sdn controller[uuid:%s] is not find", sdnControlerUuid));
        }

        if (!controllerVO.getVendorType().equals(OvnControllerConstant.OVN_CONTROLLER_TYPE)) {
            return;
        }

        VmInstanceVO vmVo = dbf.findByUuid(msg.getVmInstanceUuid(), VmInstanceVO.class);
        boolean found = false;
        for (SdnControllerHostRefVO ref : controllerVO.getHostRefVOS()) {
            if (ref.getHostUuid().equals(vmVo.getHostUuid())) {
                found = true;
                break;
            }
        }
        if (!found) {
            throw new ApiMessageInterceptionException(argerr("could not attach l3network to vm, " +
                            "because host[uuid:%s] of vm is not attached to sdn controller[uuid:%s]",
                    vmVo.getHostUuid(), sdnControlerUuid));
        }
    }
}
