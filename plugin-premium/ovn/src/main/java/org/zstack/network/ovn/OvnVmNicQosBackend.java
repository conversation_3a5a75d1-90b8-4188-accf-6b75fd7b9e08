package org.zstack.network.ovn;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.http.HttpMethod;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.cloudbus.CloudBusCallBack;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.header.core.Completion;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.host.GetNicQosOnKVMHostMsg;
import org.zstack.header.host.GetNicQosOnKVMHostReply;
import org.zstack.header.host.SetNicQosOnKVMHostMsg;
import org.zstack.header.message.MessageReply;
import org.zstack.header.network.l2.L2NetworkVO;
import org.zstack.header.network.l3.L3NetworkVO;
import org.zstack.header.vm.VmNicVO;
import org.zstack.header.vm.VmNicVO_;
import org.zstack.header.vm.VmOvsNicConstant;
import org.zstack.network.l3.L3NetworkHelper;
import org.zstack.network.service.nicqos.VmNicQosInterface;
import org.zstack.header.network.sdncontroller.SdnControllerVO;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;
import org.zstack.network.ovn.OvnControllerCommands.*;

import java.util.ArrayList;
import java.util.List;

import static org.zstack.core.Platform.operr;

@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class OvnVmNicQosBackend implements VmNicQosInterface {
    private static final CLogger logger = Utils.getLogger(OvnVmNicQosBackend.class);

    @Autowired
    DatabaseFacade dbf;
    @Autowired
    CloudBus bus;

    @Override
    public String getVmNicType() {
        return VmOvsNicConstant.ACCEL_TYPE_VHOST_USER_SPACE;
    }

    @Override
    public void setNicQos(SetNicQosOnKVMHostMsg msg, Completion completion) {
        VmNicVO nicVO = Q.New(VmNicVO.class)
                .eq(VmNicVO_.vmInstanceUuid, msg.getVmUuid())
                .eq(VmNicVO_.internalName, msg.getInternalName()).find();
        if (nicVO == null) {
            completion.success();
            return;
        }

        if ((msg.getInboundBandwidth() == null || msg.getInboundBandwidth() == -1) &&
                (msg.getOutboundBandwidth() == null || msg.getOutboundBandwidth() == -1)) {
            completion.success();
            return;
        }

        L3NetworkVO l3VO = dbf.findByUuid(nicVO.getL3NetworkUuid(), L3NetworkVO.class);
        L2NetworkVO l2Vo = dbf.findByUuid(l3VO.getL2NetworkUuid(), L2NetworkVO.class);
        String sdnControllerUuid = L3NetworkHelper.getSdnControllerUuidFromL2Uuid(l2Vo.getUuid());
        if (sdnControllerUuid == null) {
            completion.success();
            return;
        }

        SdnControllerVO sdnControllerVO = dbf.findByUuid(sdnControllerUuid, SdnControllerVO.class);
        if (sdnControllerVO == null) {
            completion.success();
            return;
        }

        VmNicQosTo to = new VmNicQosTo();
        to.setL2Uuid(l2Vo.getUuid());
        to.setVmNicUuid(nicVO.getUuid());
        to.setVmNicName(nicVO.getInternalName());
        if (msg.getInboundBandwidth() == null) {
            to.setInboundBandwidth(-1L);
        } else {
            to.setInboundBandwidth(msg.getInboundBandwidth());
        }
        if (msg.getOutboundBandwidth() == null) {
            to.setOutboundBandwidth(-1L);
        } else {
            to.setOutboundBandwidth(msg.getOutboundBandwidth());
        }
        List<VmNicQosTo> qos = new ArrayList<>();
        qos.add(to);

        VmNicQosCmd cmd = new VmNicQosCmd(sdnControllerVO);
        cmd.setNicQos(qos);
        cmd.setSync(false);

        OvnControllerAsyncHttpCallMsg cmsg = new OvnControllerAsyncHttpCallMsg();
        cmsg.setCommand(cmd);
        cmsg.setMethod(HttpMethod.POST);
        cmsg.setPath(OvnControllerCommands.OVN_VM_NICS_QOS_PATH);
        cmsg.setOvnControllerUuid(sdnControllerVO.getUuid());
        cmsg.setCheckStatus(true);
        bus.makeTargetServiceIdByResourceUuid(cmsg, OvnControllerConstant.SERVICE_ID, sdnControllerVO.getUuid());
        bus.send(cmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    completion.fail(reply.getError());
                    return;
                }

                OvnControllerAsyncHttpCallReply re = reply.castReply();
                OvnControllerCommands.VmNicQosRsp rsp = re.toResponse(OvnControllerCommands.VmNicQosRsp.class);
                if (rsp.isSuccess()) {
                    logger.debug(String.format("successfully set nic qos to ovn controller[uuid:%s, ip:%s]",
                            sdnControllerVO.getUuid(), sdnControllerVO.getIp()));
                    completion.success();
                } else {
                    ErrorCode err = operr("failed to set nic qos to  ovn controller[uuid:%s, ip:%s], because %s",
                            sdnControllerVO.getUuid(), sdnControllerVO.getIp(), rsp.getError());
                    completion.fail(err);
                }
            }
        });
    }

    @Override
    public void getNicQos(GetNicQosOnKVMHostMsg msg, ReturnValueCompletion<GetNicQosOnKVMHostReply> completion) {
        GetNicQosOnKVMHostReply qosreply = new GetNicQosOnKVMHostReply();
        VmNicVO nicVO = Q.New(VmNicVO.class)
                .eq(VmNicVO_.vmInstanceUuid, msg.getVmUuid())
                .eq(VmNicVO_.internalName, msg.getInternalName()).find();
        if (nicVO == null) {
            completion.success(qosreply);
            return;
        }

        L3NetworkVO l3VO = dbf.findByUuid(nicVO.getL3NetworkUuid(), L3NetworkVO.class);
        L2NetworkVO l2Vo = dbf.findByUuid(l3VO.getL2NetworkUuid(), L2NetworkVO.class);
        String sdnControllerUuid = L3NetworkHelper.getSdnControllerUuidFromL2Uuid(l2Vo.getUuid());
        if (sdnControllerUuid == null) {
            completion.success(qosreply);
            return;
        }

        SdnControllerVO sdnControllerVO = dbf.findByUuid(sdnControllerUuid, SdnControllerVO.class);
        if (sdnControllerVO == null) {
            completion.success(qosreply);
            return;
        }

        VmNicQosTo to = new VmNicQosTo();
        to.setL2Uuid(l2Vo.getUuid());
        to.setVmNicUuid(nicVO.getUuid());
        to.setVmNicName(nicVO.getInternalName());
        List<VmNicQosTo> qos = new ArrayList<>();
        qos.add(to);

        VmNicQosCmd cmd = new VmNicQosCmd(sdnControllerVO);
        cmd.setNicQos(qos);
        cmd.setSync(false);

        OvnControllerAsyncHttpCallMsg cmsg = new OvnControllerAsyncHttpCallMsg();
        cmsg.getQueryParams().put(VmNicQosCmd.OVN_QOS_NIC_UUID, nicVO.getUuid());
        cmsg.getQueryParams().put(VmNicQosCmd.OVN_QOS_NIC_NAME, nicVO.getInternalName());
        cmsg.setCommand(cmd);
        cmsg.setMethod(HttpMethod.GET);
        cmsg.setPath(OvnControllerCommands.OVN_VM_NICS_QOS_PATH);
        cmsg.setOvnControllerUuid(sdnControllerVO.getUuid());
        cmsg.setCheckStatus(true);
        bus.makeTargetServiceIdByResourceUuid(cmsg, OvnControllerConstant.SERVICE_ID, sdnControllerVO.getUuid());
        bus.send(cmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    completion.fail(reply.getError());
                    return;
                }

                OvnControllerAsyncHttpCallReply re = reply.castReply();
                VmNicQosRsp rsp = re.toResponse(VmNicQosRsp.class);
                if (rsp.isSuccess()) {
                    logger.debug(String.format("successfully get nic qos to ovn controller[uuid:%s, ip:%s]",
                            sdnControllerVO.getUuid(), sdnControllerVO.getIp()));
                    if (rsp.getNicQos() != null && !rsp.getNicQos().isEmpty()) {
                        qosreply.setInbound(rsp.getNicQos().get(0).getInboundBandwidth());
                        qosreply.setOutbound(rsp.getNicQos().get(0).getOutboundBandwidth());
                    }
                    completion.success(qosreply);
                } else {
                    ErrorCode err = operr("failed to get nic qos to  ovn controller[uuid:%s, ip:%s], because %s",
                            sdnControllerVO.getUuid(), sdnControllerVO.getIp(), rsp.getError());
                    completion.fail(err);
                }
            }
        });
    }

    public void syncNicQos(SdnControllerVO vo, List<SetNicQosOnKVMHostMsg> msgs, Completion completion) {
        VmNicQosCmd cmd = new VmNicQosCmd(vo);
        cmd.setSync(true);
        cmd.setNicQos(new ArrayList<>());

        for (SetNicQosOnKVMHostMsg msg : msgs) {
            VmNicQosTo to = new VmNicQosTo();
            to.setVmNicUuid(msg.getVmNicUuid());
            to.setL2Uuid(msg.getL2Uuid());
            to.setVmNicName(msg.getInternalName());
            to.setInboundBandwidth(msg.getInboundBandwidth());
            to.setOutboundBandwidth(msg.getOutboundBandwidth());
            cmd.getNicQos().add(to);
        }

        OvnControllerAsyncHttpCallMsg cmsg = new OvnControllerAsyncHttpCallMsg();
        cmsg.setCommand(cmd);
        cmsg.setMethod(HttpMethod.POST);
        cmsg.setPath(OvnControllerCommands.OVN_VM_NICS_QOS_PATH);
        cmsg.setOvnControllerUuid(vo.getUuid());
        cmsg.setCheckStatus(false);
        bus.makeTargetServiceIdByResourceUuid(cmsg, OvnControllerConstant.SERVICE_ID, vo.getUuid());
        bus.send(cmsg, new CloudBusCallBack(completion) {
            @Override
            public void run(MessageReply reply) {
                if (!reply.isSuccess()) {
                    completion.fail(reply.getError());
                    return;
                }

                OvnControllerAsyncHttpCallReply re = reply.castReply();
                OvnControllerCommands.VmNicQosRsp rsp = re.toResponse(OvnControllerCommands.VmNicQosRsp.class);
                if (rsp.isSuccess()) {
                    logger.debug(String.format("successfully sync nic qos to ovn controller[uuid:%s, ip:%s]",
                            vo.getUuid(), vo.getIp()));
                    completion.success();
                } else {
                    ErrorCode err = operr("failed to sync nic qos to  ovn controller[uuid:%s, ip:%s], because %s",
                            vo.getUuid(), vo.getIp(), rsp.getError());
                    completion.fail(err);
                }
            }
        });
    }
}
