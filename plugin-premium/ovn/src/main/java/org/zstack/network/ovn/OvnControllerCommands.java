package org.zstack.network.ovn;

import org.zstack.core.CoreGlobalProperty;
import org.zstack.core.Platform;
import org.zstack.network.securitygroup.VmNicSecurityTO;
import org.zstack.network.securitygroup.SecurityGroupTo;
import org.zstack.network.service.NetworkServiceHelper;
import org.zstack.header.network.sdncontroller.SdnControllerVO;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class OvnControllerCommands {
	public static String OVN_PING_PATH = "/ovn/ping";
	public static String OVN_NETWORKS_PATH = "/ovn/logical_switches";
	public static String OVN_VM_NICS_PATH = "/ovn/logical_ports";
	public static String OVN_VM_NICS_QOS_PATH = "/ovn/logical_ports/qos";
	public static String OVN_DHCP_OPTIONS_PATH = "/ovn/dhcp_options";

	public static String OVN_SECURITY_GROUP_PATH = "/ovn/logical_ports/security_group";
	public static String OVN_ADDRESS_SET_PATH = "/ovn/acl/address_set";

	public static class OvnAgentCommand implements Serializable {
		private String controllerUuid;
		private String controllerName;
		private String managerIp;
		private boolean sync;
		private String version = "********";

		public OvnAgentCommand(SdnControllerVO vo) {
			this.controllerUuid = vo.getUuid();
			this.controllerName = vo.getName();
			if (CoreGlobalProperty.UNIT_TEST_ON) {
				this.managerIp = "127.0.0.1";
			} else {
				this.managerIp = Platform.getManagementServerIp();
			}
		}

		public String getControllerUuid() {
			return controllerUuid;
		}

		public void setControllerUuid(String controllerUuid) {
			this.controllerUuid = controllerUuid;
		}

		public String getControllerName() {
			return controllerName;
		}

		public void setControllerName(String controllerName) {
			this.controllerName = controllerName;
		}

		public String getManagerIp() {
			return managerIp;
		}

		public void setManagerIp(String managerIp) {
			this.managerIp = managerIp;
		}

		public boolean isSync() {
			return sync;
		}

		public void setSync(boolean sync) {
			this.sync = sync;
		}

		public String getVersion() {
			return version;
		}

		public void setVersion(String version) {
			this.version = version;
		}
	}
	
	public static class OvnAgentResponse {
		public boolean success = true;
		public String error;
		public boolean isSuccess() {
			return success;
		}
		public void setSuccess(boolean success) {
			this.success = success;
		}
		public String getError() {
			return error;
		}
		public void setError(String error) {
			this.error = error;
		}
	}


	public static class PingCmd extends OvnAgentCommand {
		private String uuid;

		public PingCmd(SdnControllerVO vo) {
			super(vo);
		}

		public String getUuid() {
			return uuid;
		}

		public void setUuid(String uuid) {
			this.uuid = uuid;
		}
	}

	public static class PingRsp extends OvnAgentResponse {
		private String uuid;

		public String getUuid() {
			return uuid;
		}

		public void setUuid(String uuid) {
			this.uuid = uuid;
		}
	}

	public static class LogicalSwitchTo {
		private String uuid;
		private String name;
		private String type;
		private Integer vni;

		public String getUuid() {
			return uuid;
		}

		public void setUuid(String uuid) {
			this.uuid = uuid;
		}

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}

		public String getType() {
			return type;
		}

		public void setType(String type) {
			this.type = type;
		}

		public Integer getVni() {
			return vni;
		}

		public void setVni(Integer vni) {
			this.vni = vni;
		}
	}

	public static class LogicalSwitchCmd extends OvnAgentCommand {
		private List<LogicalSwitchTo> logicalSwitches;

		public LogicalSwitchCmd(SdnControllerVO vo) {
			super(vo);
			logicalSwitches = new ArrayList<>();
		}

		public List<LogicalSwitchTo> getLogicalSwitches() {
			return logicalSwitches;
		}

		public void setLogicalSwitches(List<LogicalSwitchTo> logicalSwitches) {
			this.logicalSwitches = logicalSwitches;
		}
	}

	public static class LogicalSwitchRet {
		private String logicalSwitchUuid;
		private String logicalSwitchName;

		public String getLogicalSwitchUuid() {
			return logicalSwitchUuid;
		}

		public void setLogicalSwitchUuid(String logicalSwitchUuid) {
			this.logicalSwitchUuid = logicalSwitchUuid;
		}

		public String getLogicalSwitchName() {
			return logicalSwitchName;
		}

		public void setLogicalSwitchName(String logicalSwitchName) {
			this.logicalSwitchName = logicalSwitchName;
		}
	}

	public static class LogicalSwitchRsp extends OvnAgentResponse {
		private List<LogicalSwitchRet> rets = new ArrayList<>();

		public List<LogicalSwitchRet> getRets() {
			return rets;
		}

		public void setRets(List<LogicalSwitchRet> rets) {
			this.rets = rets;
		}
	}

	public static class LogicalSwitchPortTo {
		private String uuid;
		private String logicalSwitchUuid;
		private String name;
		private String mac;
		private String ip;
		private String ip6;
		private Integer vni;
		private String requestedChassis;
		private String activationStrategy;
		private String ifaceIdVer;
		private String hostname;
		private String dhcpv4Options; // id is l3Uuid_4
		private String dhcpv6Options; // id is l3Uuid_6
		private Long inboundBandwidth;
		private Long outboundBandwidth;

		public String getLogicalSwitchUuid() {
			return logicalSwitchUuid;
		}

		public void setLogicalSwitchUuid(String logicalSwitchUuid) {
			this.logicalSwitchUuid = logicalSwitchUuid;
		}

		public String getIp6() {
			return ip6;
		}

		public void setIp6(String ip6) {
			this.ip6 = ip6;
		}

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}

		public String getIp() {
			return ip;
		}

		public void setIp(String ip) {
			this.ip = ip;
		}

		public String getMac() {
			return mac;
		}

		public void setMac(String mac) {
			this.mac = mac;
		}

		public Integer getVni() {
			return vni;
		}

		public void setVni(Integer vni) {
			this.vni = vni;
		}

		public String getUuid() {
			return uuid;
		}

		public void setUuid(String uuid) {
			this.uuid = uuid;
		}

		public String getRequestedChassis() {
			return requestedChassis;
		}

		public void setRequestedChassis(String requestedChassis) {
			this.requestedChassis = requestedChassis;
		}

		public String getActivationStrategy() {
			return activationStrategy;
		}

		public void setActivationStrategy(String activationStrategy) {
			this.activationStrategy = activationStrategy;
		}

		public String getIfaceIdVer() {
			return ifaceIdVer;
		}

		public void setIfaceIdVer(String ifaceIdVer) {
			this.ifaceIdVer = ifaceIdVer;
		}

		public String getHostname() {
			return hostname;
		}

		public void setHostname(String hostname) {
			this.hostname = hostname;
		}

		public String getDhcpv4Options() {
			return dhcpv4Options;
		}

		public void setDhcpv4Options(String dhcpv4Options) {
			this.dhcpv4Options = dhcpv4Options;
		}

		public String getDhcpv6Options() {
			return dhcpv6Options;
		}

		public void setDhcpv6Options(String dhcpv6Options) {
			this.dhcpv6Options = dhcpv6Options;
		}

		public Long getInboundBandwidth() {
			return inboundBandwidth;
		}

		public void setInboundBandwidth(Long inboundBandwidth) {
			this.inboundBandwidth = inboundBandwidth;
		}

		public Long getOutboundBandwidth() {
			return outboundBandwidth;
		}

		public void setOutboundBandwidth(Long outboundBandwidth) {
			this.outboundBandwidth = outboundBandwidth;
		}
	}

	public static class LogicalSwitchPortCmd extends OvnAgentCommand {
		private List<LogicalSwitchPortTo> nics;

		public LogicalSwitchPortCmd(SdnControllerVO vo) {
			super(vo);
			nics = new ArrayList<>();
		}

		public List<LogicalSwitchPortTo> getNics() {
			return nics;
		}

		public void setNics(List<LogicalSwitchPortTo> nics) {
			this.nics = nics;
		}
	}

	public static class LogicalSwitchPortRet {
		private String logicalPortUuid;
		private String logicalPortName;

		public String getLogicalPortUuid() {
			return logicalPortUuid;
		}

		public void setLogicalPortUuid(String logicalPortUuid) {
			this.logicalPortUuid = logicalPortUuid;
		}

		public String getLogicalPortName() {
			return logicalPortName;
		}

		public void setLogicalPortName(String logicalPortName) {
			this.logicalPortName = logicalPortName;
		}
	}

	public static class LogicalSwitchPortRsp extends OvnAgentResponse {
		private List<LogicalSwitchPortRet> nics = new ArrayList<>();

		public List<LogicalSwitchPortRet> getNics() {
			return nics;
		}

		public void setNics(List<LogicalSwitchPortRet> nics) {
			this.nics = nics;
		}
	}


	public static class Dhcp4OptionTo {
		private String id;
		private Integer mtu;
		private String cidr;
		private String dhcpServerIp;
		private String dhcpServerMac;
		private Integer leaseTime;
		private String router;
		private String netmask;
		private List<String> dnsServers = new ArrayList<>();
		private String dnsDomain;
		private List<NetworkServiceHelper.HostRouteInfo> hostRoutes = new ArrayList<>();

		public String getId() {
			return id;
		}

		public void setId(String id) {
			this.id = id;
		}

		public Integer getMtu() {
			return mtu;
		}

		public void setMtu(Integer mtu) {
			this.mtu = mtu;
		}

		public String getCidr() {
			return cidr;
		}

		public void setCidr(String cidr) {
			this.cidr = cidr;
		}

		public String getDhcpServerIp() {
			return dhcpServerIp;
		}

		public void setDhcpServerIp(String dhcpServerIp) {
			this.dhcpServerIp = dhcpServerIp;
		}

		public String getDhcpServerMac() {
			return dhcpServerMac;
		}

		public void setDhcpServerMac(String dhcpServerMac) {
			this.dhcpServerMac = dhcpServerMac;
		}

		public Integer getLeaseTime() {
			return leaseTime;
		}

		public void setLeaseTime(Integer leaseTime) {
			this.leaseTime = leaseTime;
		}

		public String getRouter() {
			return router;
		}

		public void setRouter(String router) {
			this.router = router;
		}

		public String getNetmask() {
			return netmask;
		}

		public void setNetmask(String netmask) {
			this.netmask = netmask;
		}

		public List<String> getDnsServers() {
			return dnsServers;
		}

		public void setDnsServers(List<String> dnsServers) {
			this.dnsServers = dnsServers;
		}

		public String getDnsDomain() {
			return dnsDomain;
		}

		public void setDnsDomain(String dnsDomain) {
			this.dnsDomain = dnsDomain;
		}

		public List<NetworkServiceHelper.HostRouteInfo> getHostRoutes() {
			return hostRoutes;
		}

		public void setHostRoutes(List<NetworkServiceHelper.HostRouteInfo> hostRoutes) {
			this.hostRoutes = hostRoutes;
		}
	}

	public static class Dhcp6OptionTo {
		private String id;
		private String cidr;
		private String dhcpServerMac;
		private String dhcpServerIp;
		private String domainSearch;
		private List<String> dnsServers = new ArrayList<>();
		private Boolean dhcp6Stateless;

		public String getId() {
			return id;
		}

		public void setId(String id) {
			this.id = id;
		}

		public String getCidr() {
			return cidr;
		}

		public void setCidr(String cidr) {
			this.cidr = cidr;
		}

		public String getDhcpServerIp() {
			return dhcpServerIp;
		}

		public void setDhcpServerIp(String dhcpServerIp) {
			this.dhcpServerIp = dhcpServerIp;
		}

		public List<String> getDnsServers() {
			return dnsServers;
		}

		public void setDnsServers(List<String> dnsServers) {
			this.dnsServers = dnsServers;
		}

		public String getDomainSearch() {
			return domainSearch;
		}

		public void setDomainSearch(String domainSearch) {
			this.domainSearch = domainSearch;
		}

		public Boolean getDhcp6Stateless() {
			return dhcp6Stateless;
		}

		public void setDhcp6Stateless(Boolean dhcp6Stateless) {
			this.dhcp6Stateless = dhcp6Stateless;
		}

		public String getDhcpServerMac() {
			return dhcpServerMac;
		}

		public void setDhcpServerMac(String dhcpServerMac) {
			this.dhcpServerMac = dhcpServerMac;
		}
	}

	public static class DhcpOptionsCmd extends OvnAgentCommand {
		private List<Dhcp4OptionTo> dhcp4;
		private List<Dhcp6OptionTo> dhcp6;

		public DhcpOptionsCmd(SdnControllerVO vo, List<Dhcp4OptionTo> dhcp4, List<Dhcp6OptionTo> dhcp6, boolean sync) {
			super(vo);
			this.dhcp4 = dhcp4;
			this.dhcp6 = dhcp6;
		}

		public List<Dhcp4OptionTo> getDhcp4() {
			return dhcp4;
		}

		public void setDhcp4(List<Dhcp4OptionTo> dhcp4) {
			this.dhcp4 = dhcp4;
		}

		public List<Dhcp6OptionTo> getDhcp6() {
			return dhcp6;
		}

		public void setDhcp6(List<Dhcp6OptionTo> dhcp6) {
			this.dhcp6 = dhcp6;
		}
	}

	public static class DhcpOptionsRsp extends OvnAgentResponse {

	}

	public static class VmNicQosTo {
		String l2Uuid;
		String vmNicUuid;
		String vmNicName;
		Long inboundBandwidth;
		Long outboundBandwidth;

		public String getL2Uuid() {
			return l2Uuid;
		}

		public void setL2Uuid(String l2Uuid) {
			this.l2Uuid = l2Uuid;
		}

		public String getVmNicUuid() {
			return vmNicUuid;
		}

		public void setVmNicUuid(String vmNicUuid) {
			this.vmNicUuid = vmNicUuid;
		}

		public String getVmNicName() {
			return vmNicName;
		}

		public void setVmNicName(String vmNicName) {
			this.vmNicName = vmNicName;
		}

		public Long getInboundBandwidth() {
			return inboundBandwidth;
		}

		public void setInboundBandwidth(Long inboundBandwidth) {
			this.inboundBandwidth = inboundBandwidth;
		}

		public Long getOutboundBandwidth() {
			return outboundBandwidth;
		}

		public void setOutboundBandwidth(Long outboundBandwidth) {
			this.outboundBandwidth = outboundBandwidth;
		}
	}

	public static class VmNicQosCmd extends OvnAgentCommand {
		public static String OVN_QOS_NIC_UUID = "vmNicUuid";
		public static String OVN_QOS_NIC_NAME = "vmNicName";

		private List<VmNicQosTo> nicQos;

		public VmNicQosCmd(SdnControllerVO vo) {
			super(vo);
			nicQos = new ArrayList<>();
		}

		public List<VmNicQosTo> getNicQos() {
			return nicQos;
		}

		public void setNicQos(List<VmNicQosTo> nicQos) {
			this.nicQos = nicQos;
		}
	}

	public static class VmNicQosRsp extends OvnAgentResponse {
		private List<VmNicQosTo> nicQos; //use for getNicQos

		public List<VmNicQosTo> getNicQos() {
			return nicQos;
		}

		public void setNicQos(List<VmNicQosTo> nicQos) {
			this.nicQos = nicQos;
		}
	}

	public static class VmNicRefreshSecurityGroupCmd extends OvnAgentCommand {
		private List<VmNicSecurityTO> vmNicTOs = new ArrayList<>();
		private List<SecurityGroupTo> groups = new ArrayList<>();
		Integer securityGroupRulesMaxNum;
		Integer ovnMaxPriority;

		public VmNicRefreshSecurityGroupCmd(SdnControllerVO vo) {
			super(vo);
		}

		public List<VmNicSecurityTO> getVmNicTOs() {
			return vmNicTOs;
		}

		public void setVmNicTOs(List<VmNicSecurityTO> vmNicTOs) {
			this.vmNicTOs = vmNicTOs;
		}

		public List<SecurityGroupTo> getGroups() {
			return groups;
		}

		public void setGroups(List<SecurityGroupTo> groups) {
			this.groups = groups;
		}

		public Integer getSecurityGroupRulesMaxNum() {
			return securityGroupRulesMaxNum;
		}

		public void setSecurityGroupRulesMaxNum(Integer securityGroupRulesMaxNum) {
			this.securityGroupRulesMaxNum = securityGroupRulesMaxNum;
		}

		public Integer getOvnMaxPriority() {
			return ovnMaxPriority;
		}

		public void setOvnMaxPriority(Integer ovnMaxPriority) {
			this.ovnMaxPriority = ovnMaxPriority;
		}
	}

	public static class VmNicRefreshSecurityGroupRsp extends OvnAgentResponse {
	}
}
