package org.zstack.network.ovn;

import org.springframework.beans.factory.annotation.Autowire;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.annotation.Qualifier;
import org.zstack.compute.vm.MevocoVmNicQosConfigBackend;
import org.zstack.compute.vm.VmNicQosStruct;
import org.zstack.core.asyncbatch.While;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.header.core.Completion;
import org.zstack.header.core.WhileDoneCompletion;
import org.zstack.header.core.workflow.FlowTrigger;
import org.zstack.header.core.workflow.NoRollbackFlow;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.ErrorCodeList;
import org.zstack.header.host.SetNicQosOnKVMHostMsg;
import org.zstack.header.network.l2.L2NetworkConstant;
import org.zstack.header.network.l2.L2NetworkVO;
import org.zstack.header.vm.VmInstanceState;
import org.zstack.header.vm.VmOvsNicConstant;
import org.zstack.network.l2.L2NetworkSystemTags;
import org.zstack.header.network.sdncontroller.SdnControllerVO;
import org.zstack.utils.TagUtils;

import javax.persistence.Tuple;
import javax.persistence.TypedQuery;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.zstack.sdnController.header.SdnControllerFlowDataParam.SDN_CONTROLLER_UUID;
import static org.zstack.utils.CollectionDSL.*;

@Configurable(preConstruction = true, autowire = Autowire.BY_TYPE)
public class OvnSyncVmNicQosFlow extends NoRollbackFlow {

    @Autowired
    DatabaseFacade dbf;
    @Autowired
    OvnVmNicQosBackend qosBackend;
    @Autowired
    @Qualifier("MevocoVmNicQosConfigBackend")
    MevocoVmNicQosConfigBackend qosConfigBackend;

    @Override
    public void run(FlowTrigger trigger, Map data) {
        String controllerUuid = (String)data.get(SDN_CONTROLLER_UUID);

        String sql = "select nic.uuid, nic.vmInstanceUuid, l2.uuid from " +
                " VmNicVO nic, VmInstanceVO vm, L3NetworkVO l3, L2NetworkVO l2, SystemTagVO stag" +
                " where nic.vmInstanceUuid=vm.uuid " +
                " and vm.state not in (:states)" +
                " and nic.type = :type" +
                " and l3.l2NetworkUuid=l2.uuid " +
                " and l2.vSwitchType=:vSwitchType " +
                " and nic.l3NetworkUuid=l3.uuid " +
                " and stag.resourceType=:ttype and stag.tag=:tag and stag.resourceUuid=l2.uuid";
        TypedQuery<Tuple> q = dbf.getEntityManager().createQuery(sql, Tuple.class);
        q.setParameter("type", VmOvsNicConstant.ACCEL_TYPE_VHOST_USER_SPACE);
        q.setParameter("states", list(
                VmInstanceState.Destroyed,
                VmInstanceState.Destroying));
        q.setParameter("vSwitchType", L2NetworkConstant.VSWITCH_TYPE_OVN_DPDK);
        q.setParameter("ttype", L2NetworkVO.class.getSimpleName());
        q.setParameter("tag", TagUtils.tagPatternToSqlPattern(L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID.instantiateTag(
                map(e(L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID_TOKEN, controllerUuid)))));

        List<Tuple> tuples = q.getResultList();
        List<SetNicQosOnKVMHostMsg> qosMsgs = new ArrayList<>();
        List<VmNicQosStruct> structs = new ArrayList<>();
        for (Tuple t : tuples) {
            VmNicQosStruct struct = qosConfigBackend.getNicQos(t.get(1, String.class),
                    t.get(0, String.class));
            if (struct.inboundBandwidth == -1 && struct.outboundBandwidth == -1) {
                continue;
            }
            struct.l2Uuid = t.get(2, String.class);
            structs.add(struct);
        }

        SdnControllerVO vo = dbf.findByUuid(controllerUuid, SdnControllerVO.class);

        if (structs.isEmpty()) {
            qosBackend.syncNicQos(vo, qosMsgs, new Completion(trigger) {
                @Override
                public void success() {
                    trigger.next();
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    trigger.fail(errorCode);
                }
            });

            return;
        }

        int step = 100;
        int size = structs.size();
        int count = size / 100;
        if (size - count * 100 > 0) {
            count++;
        }

        List<List<SetNicQosOnKVMHostMsg>> batchQos = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            int end = (i + 1) * step;
            List<SetNicQosOnKVMHostMsg> qos = structs.subList(i * step, Math.min(end, structs.size()))
                    .stream().map(s -> {
                        SetNicQosOnKVMHostMsg msg = new SetNicQosOnKVMHostMsg();
                        msg.setVmUuid(s.vmUuid);
                        msg.setInternalName(s.internalName);
                        msg.setVmNicUuid(s.vmNicUuid);
                        msg.setL2Uuid(s.l2Uuid);
                        if (s.inboundBandwidth != -1) {
                            msg.setInboundBandwidth(s.inboundBandwidth);
                        }
                        if (s.outboundBandwidth != -1) {
                            msg.setOutboundBandwidth(s.outboundBandwidth);
                        }
                        return msg;
                    })
                    .collect(Collectors.toList());
            batchQos.add(qos);
        }

        new While<>(batchQos).each((vos, wc) -> {
            qosBackend.syncNicQos(vo, vos, new Completion(wc) {
                @Override
                public void success() {
                    wc.done();
                }

                @Override
                public void fail(ErrorCode errorCode) {
                    wc.addError(errorCode);
                    wc.allDone();
                }
            });
        }).run(new WhileDoneCompletion(trigger) {
            @Override
            public void done(ErrorCodeList errorCodeList) {
                if (!errorCodeList.getCauses().isEmpty()) {
                    trigger.fail(errorCodeList.getCauses().get(0));
                    return;
                }

                trigger.next();
            }
        });
    }
}
