package org.zstack.network.ovn;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.appliancevm.*;
import org.zstack.appliancevm.kvm.KvmApplianceVmSubTypeFactory;
import org.zstack.compute.vm.VmSystemTags;
import org.zstack.core.cloudbus.EventFacade;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.header.core.Completion;
import org.zstack.header.vm.*;
import org.zstack.resourceconfig.ResourceConfigFacade;
import org.zstack.tag.SystemTagCreator;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import static org.zstack.utils.CollectionDSL.e;
import static org.zstack.utils.CollectionDSL.map;


public class OvnControllerVmFactory  implements KvmApplianceVmSubTypeFactory, ApplianceVmSubTypeFactory, PreVmInstantiateResourceExtensionPoint {
    private static final CLogger logger = Utils.getLogger(OvnControllerVmFactory.class);
    public static ApplianceVmType applianceVmType = new ApplianceVmType(OvnControllerConstant.OVN_CONTROLLER_APPLIANCE_VM_TYPE);
    @Autowired
    DatabaseFacade dbf;
    @Autowired
    ResourceConfigFacade rcf;
    @Autowired
    EventFacade evtf;

    @Override
    public ApplianceVmType getApplianceVmType() {
        return applianceVmType;
    }

    @Override
    public ApplianceVm getSubApplianceVm(ApplianceVmVO apvm) {
        OvnControllerVmInstanceVO ovn = dbf.findByUuid(apvm.getUuid(), OvnControllerVmInstanceVO.class);
        return new OvnControllerVm(ovn);
    }

    @Override
    public ApplianceVmVO persistApplianceVm(ApplianceVmSpec spec, ApplianceVmVO apvm) {
        OvnControllerVmInstanceVO ovnVmInstanceVO = new OvnControllerVmInstanceVO(apvm);
        dbf.getEntityManager().persist(ovnVmInstanceVO);
        return ovnVmInstanceVO;
    }

    @Override
    public void removeApplianceVm(ApplianceVmSpec spec, ApplianceVmVO apvm) {
        dbf.removeByPrimaryKey(apvm.getUuid(), OvnControllerVmInstanceVO.class);
    }


    @Override
    public void createHypervisorBasedConfigurations(VmInstanceSpec spec) {
        return;
    }

    @Override
    public void preBeforeInstantiateVmResource(VmInstanceSpec spec) throws VmInstantiateResourceException {

    }

    @Override
    public void preInstantiateVmResource(VmInstanceSpec spec, Completion completion) {
        // factory only requested by new create vm
        if (spec.getCurrentVmOperation() != VmInstanceConstant.VmOperation.NewCreate) {
            completion.success();
            return;
        }

        VmInstanceInventory vm = spec.getVmInventory();
        if (!Q.New(OvnControllerVmInstanceVO.class).eq(OvnControllerVmInstanceVO_.uuid, vm.getUuid()).isExists()) {
            completion.success();
            return;
        }

        SystemTagCreator creator = VmSystemTags.SYNC_PORTS.newSystemTagCreator(vm.getUuid());
        creator.recreate = true;
        creator.setTagByTokens(map(e(VmSystemTags.SYNC_PORTS_TOKEN, vm.getUuid())));
        creator.create();

        completion.success();
    }

    @Override
    public void preReleaseVmResource(VmInstanceSpec spec, Completion completion) {
        completion.success();
    }
}
