package org.zstack.network.ovn;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.appliancevm.*;
import org.zstack.compute.allocator.AttachedL2NetworkAllocatorExtensionPoint;
import org.zstack.compute.host.PostHostConnectExtensionPoint;
import org.zstack.compute.sriov.SriovSystemTags;
import org.zstack.compute.vm.MevocoVmSystemTags;
import org.zstack.compute.vm.VmSystemTags;
import org.zstack.core.Platform;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.db.Q;
import org.zstack.core.db.SQL;
import org.zstack.core.db.SimpleQuery;
import org.zstack.header.core.Completion;
import org.zstack.header.core.ReturnValueCompletion;
import org.zstack.header.core.workflow.Flow;
import org.zstack.header.core.workflow.FlowTrigger;
import org.zstack.header.core.workflow.NoRollbackFlow;
import org.zstack.header.errorcode.ErrorCode;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.host.HostInventory;
import org.zstack.header.image.ImageBootMode;
import org.zstack.header.image.ImageInventory;
import org.zstack.header.image.ImageVO;
import org.zstack.header.managementnode.PrepareDbInitialValueExtensionPoint;
import org.zstack.header.network.NetworkException;
import org.zstack.header.network.l2.*;
import org.zstack.core.componentloader.PluginRegistry;
import org.zstack.header.AbstractService;
import org.zstack.header.message.Message;
import org.zstack.header.network.l3.L3NetworkInventory;
import org.zstack.header.network.l3.L3NetworkVO;
import org.zstack.header.network.sdncontroller.SdnControllerHostRefVO;
import org.zstack.header.network.sdncontroller.SdnControllerHostRefVO_;
import org.zstack.header.network.sdncontroller.SdnControllerVO;
import org.zstack.header.network.service.*;
import org.zstack.header.vipQos.VipQosConstants;
import org.zstack.header.vm.*;
import org.zstack.image.ImageSystemTags;
import org.zstack.ipsec.IPsecConstants;
import org.zstack.kvm.KVMConstant;
import org.zstack.kvm.KVMHostConnectExtensionPoint;
import org.zstack.kvm.KVMHostConnectedContext;
import org.zstack.network.l2.L2NetworkSystemTags;
import org.zstack.network.l3.L3NetworkHelper;
import org.zstack.network.securitygroup.SecurityGroupConstant;
import org.zstack.network.service.eip.EipConstant;
import org.zstack.network.service.lb.LoadBalancerConstants;
import org.zstack.network.service.userdata.UserdataConstant;
import org.zstack.network.service.virtualrouter.VirtualRouterGlobalConfig;
import org.zstack.sdnController.*;
import org.zstack.sdnController.header.*;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.*;
import java.util.stream.Collectors;

import static org.zstack.compute.vm.VmSystemTags.MACHINE_TYPE_TOKEN;
import static org.zstack.core.Platform.operr;
import static org.zstack.utils.CollectionDSL.*;

public class OvnControllerManagerImpl extends AbstractService implements
        OvnControllerManager, AttachedL2NetworkAllocatorExtensionPoint,
        KVMHostConnectExtensionPoint, PostHostConnectExtensionPoint, PrepareDbInitialValueExtensionPoint,
        L2NetworkCreateExtensionPoint, FilterAttachableL3NetworkExtensionPoint {
    private static final CLogger logger = Utils.getLogger(OvnControllerManagerImpl.class);

    @Autowired
    private PluginRegistry pluginRgty;
    @Autowired
    private OvnControllerFactory factory;
    @Autowired
    CloudBus bus;
    @Autowired
    DatabaseFacade dbf;
    @Autowired
    private ApplianceVmFacade apvmf;
    @Autowired
    private OvsVSwitchBackend ovsBackend;
    private NetworkServiceProviderVO ovnProviderVO;

    @Override
    public boolean start() {
        return true;
    }

    @Override
    public boolean stop() {
        return true;
    }

    @Override
    public void handleMessage(Message msg) {
        if (msg instanceof SdnControllerMessage) {
            handleOvnControllerMessage((SdnControllerMessage) msg);
        } else if (msg instanceof APICreateOvnControllerVmMsg) {
            handle((APICreateOvnControllerVmMsg) msg);
        } else {
            bus.dealWithUnknownMessage(msg);
        }
    }

    void handleOvnControllerMessage(SdnControllerMessage msg) {
        SdnControllerVO vo = dbf.findByUuid(msg.getSdnControllerUuid(), SdnControllerVO.class);
        SdnController controller = factory.getSdnController(vo);
        controller.handleMessage(msg);
    }

    private void handle(APICreateOvnControllerVmMsg msg) {
        APICreateOvnControllerVmEvent event = new APICreateOvnControllerVmEvent(msg.getId());

        createOvnControllerVm(msg, new ReturnValueCompletion<OvnControllerVmInstanceInventory>(msg) {
            @Override
            public void success(OvnControllerVmInstanceInventory returnValue) {
                event.setInventory(returnValue);
                bus.publish(event);
            }

            @Override
            public void fail(ErrorCode errorCode) {
                event.setError(errorCode);
                event.setSuccess(false);
                bus.publish(event);
            }
        });
    }

    private void createOvnControllerVm(APICreateOvnControllerVmMsg msg, ReturnValueCompletion<OvnControllerVmInstanceInventory> completion) {
        final OvnControllerVmOfferingInventory offering = OvnControllerVmOfferingInventory.valueOf(
                dbf.findByUuid(msg.getInstanceOfferingUuid(), OvnControllerVmOfferingVO.class));
        final L3NetworkInventory mgtNetwork = L3NetworkInventory.valueOf(
                dbf.findByUuid(offering.getManagementNetworkUuid(), L3NetworkVO.class));
        final String accountUuid = msg.getSession().getAccountUuid();

        class newSlbInstanceJob {
            private void failAndReply(ErrorCode err) {
                completion.fail(err);
            }

            private void create() {

                ImageVO imgvo = dbf.findByUuid(offering.getImageUuid(), ImageVO.class);

                final ApplianceVmSpec aspec = new ApplianceVmSpec();
                aspec.setUuid(msg.getResourceUuid());
                aspec.setSyncCreate(false);
                aspec.setTemplate(ImageInventory.valueOf(imgvo));
                aspec.setApplianceVmType(ApplianceVmType.valueOf(OvnControllerConstant.OVN_CONTROLLER_APPLIANCE_VM_TYPE));
                aspec.setInstanceOffering(offering);

                aspec.setName(msg.getName());
                aspec.setDescription(msg.getDescription());
                aspec.setRequiredZoneUuid(msg.getZoneUuid());
                aspec.setRequiredClusterUuid(msg.getClusterUuid());
                aspec.setRequiredHostUuid(msg.getHostUuid());
                aspec.setSshUsername(VirtualRouterGlobalConfig.SSH_USERNAME.value());
                aspec.setSshPort(VirtualRouterGlobalConfig.SSH_PORT.value(Integer.class));
                aspec.setAccountUuid(accountUuid);

                String imgBootMode = ImageSystemTags.BOOT_MODE.getTokenByResourceUuid(imgvo.getUuid(), ImageSystemTags.BOOT_MODE_TOKEN);
                if (ImageBootMode.UEFI.toString().equals(imgBootMode)) {
                    aspec.setInherentSystemTags(Arrays.asList(ImageSystemTags.BOOT_MODE.getTag(imgvo.getUuid()), VmSystemTags.MACHINE_TYPE.instantiateTag(map(e(MACHINE_TYPE_TOKEN, VmMachineType.q35.toString())))));
                }

                if (msg.getSystemTags() != null) {
                    List<String> tags = new ArrayList<>();
                    for (String sysTag : msg.getSystemTags()) {
                        if (MevocoVmSystemTags.VM_CPU_PINNING.isMatch(sysTag)) {
                            tags.add(sysTag);
                        }
                        if(SriovSystemTags.L3_ENABLE_SRIOV.isMatch(sysTag)){
                            tags.add(sysTag);
                        }
                    }

                    if (!tags.isEmpty()) {
                        aspec.setNonInherentSystemTags(tags);
                    }
                    aspec.setStaticVip(ApplianceVmOperator.parseStaticVipSystemTag(msg.getSystemTags()));
                    aspec.setStaticIp(ApplianceVmOperator.parseStaticIpSystemTag(msg.getSystemTags()));
                }

                ApplianceVmNicSpec mgmtNicSpec = new ApplianceVmNicSpec();
                mgmtNicSpec.setL3NetworkUuid(mgtNetwork.getUuid());
                if (aspec.getStaticIp().containsKey(mgtNetwork.getUuid())) {
                    mgmtNicSpec.setStaticIp(aspec.getStaticIp().get(mgtNetwork.getUuid()));
                }
                aspec.setManagementNic(mgmtNicSpec);
                aspec.setDefaultL3Network(mgtNetwork);
                aspec.putExtensionData(ApplianceVmConstant.APPLIANCE_VM_CPUMODE, KVMConstant.CPU_MODE_HOST_PASSTHROUGH);

                apvmf.createApplianceVm(aspec, new ReturnValueCompletion<ApplianceVmInventory>(completion) {
                    @Override
                    public void success(ApplianceVmInventory apvm) {
                        completion.success(OvnControllerVmInstanceInventory.valueOf(dbf.findByUuid(apvm.getUuid(), OvnControllerVmInstanceVO.class)));
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        failAndReply(errorCode);
                    }
                });
            }
        }

        new newSlbInstanceJob().create();
    }

    @Override
    public String getId() {
        return bus.makeLocalServiceId(OvnControllerConstant.SERVICE_ID);
    }

    @Override
    public List<String> filter(List<String> hostUuids, List<String> l2Uuids) {
        if (hostUuids.isEmpty()) {
            return hostUuids;
        }

        if (l2Uuids.isEmpty()) {
            return hostUuids;
        }

        Set<String> controllerUuids = new HashSet<>();
        for (String uuid : l2Uuids) {
            L2NetworkVO l2Vo = dbf.findByUuid(uuid, L2NetworkVO.class);
            if (!l2Vo.getvSwitchType().equals(L2NetworkConstant.VSWITCH_TYPE_OVN_DPDK)) {
                continue;
            }

            String controllerUuid = L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID.getTokenByResourceUuid(uuid, L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID_TOKEN);
            if (controllerUuid != null) {
                controllerUuids.add(controllerUuid);
            } else {
                throw new OperationFailureException(operr("l2 network[uuid:%s] is not attached ovn controller", uuid));
            }
        }

        if (controllerUuids.isEmpty()) {
            return hostUuids;
        }

        for (String controllerUuid : controllerUuids) {
            hostUuids = Q.New(SdnControllerHostRefVO.class)
                    .eq(SdnControllerHostRefVO_.sdnControllerUuid, controllerUuid)
                    .in(SdnControllerHostRefVO_.hostUuid, hostUuids)
                    .select(SdnControllerHostRefVO_.hostUuid).listValues();
            if (hostUuids.isEmpty()) {
                return hostUuids;
            }
        }

        return hostUuids;
    }

    @Override
    public Flow createKvmHostConnectingFlow(KVMHostConnectedContext context) {
        return new NoRollbackFlow() {
            String __name__ = "start-ovn-service-on-host-" + context.getInventory().getUuid();

            @Override
            public void run(final FlowTrigger trigger, Map data) {
                HostInventory host = context.getInventory();
                if (!host.getHypervisorType().equals(KVMConstant.KVM_HYPERVISOR_TYPE)) {
                    trigger.next();
                    return;
                }

                SdnControllerHostRefVO refVO = Q.New(SdnControllerHostRefVO.class)
                        .eq(SdnControllerHostRefVO_.hostUuid, host.getUuid()).limit(1).find();
                if (refVO == null) {
                    trigger.next();
                    return;
                }

                SdnControllerVO sdnControllerVO = dbf.findByUuid(refVO.getSdnControllerUuid(), SdnControllerVO.class);

                VSwitchOvsConfigStruct struct = new OvnController(sdnControllerVO).getVSwitchOvsConfigStruct(refVO);
                struct.setHostStatusCheck(false);
                ovsBackend.startService(host.getUuid(), struct, new Completion(trigger) {
                    @Override
                    public void success() {
                        trigger.next();
                    }

                    @Override
                    public void fail(ErrorCode errorCode) {
                        trigger.fail(errorCode);
                    }
                });
            }
        };
    }

    @Override
    public Flow createPostHostConnectFlow(HostInventory host) {
        return new NoRollbackFlow() {
            String __name__ = String.format("sync-ovn-nic-to-host-%s", host.getUuid());

            @Override
            public void run(FlowTrigger trigger, Map data) {
                if (!host.getHypervisorType().equals(KVMConstant.KVM_HYPERVISOR_TYPE)) {
                    trigger.next();
                    return;
                }

                SdnControllerHostRefVO refVO = Q.New(SdnControllerHostRefVO.class)
                        .eq(SdnControllerHostRefVO_.hostUuid, host.getUuid()).limit(1).find();
                if (refVO == null) {
                    trigger.next();
                    return;
                }

                List<VmNicVO> vmNics = new ArrayList<>();

                /* get all ovn dpdk network */
                List<String> l3Uuids = SQL.New("select distinct l3.uuid from L3NetworkVO l3, L2NetworkVO l2, L2NetworkClusterRefVO ref " +
                                " where l3.l2NetworkUuid = l2.uuid" +
                                " and l2.uuid = ref.l2NetworkUuid" +
                                " and l2.vSwitchType = :vSwitchType" +
                                " and ref.clusterUuid = :clusterUuid")
                        .param("vSwitchType", L2NetworkConstant.VSWITCH_TYPE_OVN_DPDK)
                        .param("clusterUuid", host.getClusterUuid())
                        .list();
                if (!l3Uuids.isEmpty()) {
                    /* get all ovn dpdk nics */
                    vmNics = SQL.New("select distinct nic from VmNicVO nic, VmInstanceVO vm " +
                                    " where nic.vmInstanceUuid = vm.uuid " +
                                    " and vm.state not in (:states)" +
                                    " and vm.hostUuid = :hostUuid " +
                                    " and nic.l3NetworkUuid in (:l3Uuids)")
                            .param("states", list(VmInstanceState.Destroyed, VmInstanceState.Destroying))
                            .param("hostUuid", host.getUuid())
                            .param("l3Uuids", l3Uuids)
                            .list();
                }

                ovsBackend.addOvsPort(host.getUuid(), L2NetworkConstant.VSWITCH_TYPE_OVN_DPDK,
                        true, false, VmNicInventory.valueOf(vmNics), new Completion(trigger) {
                            @Override
                            public void success() {
                                trigger.next();
                            }

                            @Override
                            public void fail(ErrorCode errorCode) {
                                logger.warn(String.format("sync ovn nic to host[uuid:%s] failed, %s",
                                        host.getUuid(), errorCode.getDetails()));
                                trigger.next();
                            }
                        });
            }
        };
    }

    @Override
    public void prepareDbInitialValue() {
        OvnControllerConstant.OVN_NETWORK_SERVICE_TYPE.setCreateDhcpNameSpace(false);
        OvnControllerConstant.OVN_NETWORK_SERVICE_TYPE.setAllocateDhcpServerIp(true);

        SimpleQuery<NetworkServiceProviderVO> query = dbf.createQuery(NetworkServiceProviderVO.class);
        query.add(NetworkServiceProviderVO_.type, SimpleQuery.Op.EQ, OvnControllerConstant.OVN_CONTROLLER_TYPE);
        ovnProviderVO = query.find();
        if (ovnProviderVO != null) {
            ovnProviderVO.getNetworkServiceTypes().add(NetworkServiceType.DHCP.toString());
            ovnProviderVO.getNetworkServiceTypes().add(NetworkServiceType.DNS.toString());
            ovnProviderVO.getNetworkServiceTypes().add(NetworkServiceType.SNAT.toString());
            ovnProviderVO.getNetworkServiceTypes().add(NetworkServiceType.PortForwarding.toString());
            ovnProviderVO.getNetworkServiceTypes().add(EipConstant.EIP_NETWORK_SERVICE_TYPE);
            ovnProviderVO.getNetworkServiceTypes().add(LoadBalancerConstants.LB_NETWORK_SERVICE_TYPE_STRING);
            ovnProviderVO.getNetworkServiceTypes().add(IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString());
            ovnProviderVO.getNetworkServiceTypes().add(SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE);
            ovnProviderVO.getNetworkServiceTypes().add(UserdataConstant.USERDATA_TYPE_STRING);
            ovnProviderVO.getNetworkServiceTypes().add(VipQosConstants.VIPQOS_NETWORK_SERVICE_TYPE.toString());
            ovnProviderVO.getNetworkServiceTypes().add(NetworkServiceType.HostRoute.toString());

            ovnProviderVO = dbf.updateAndRefresh(ovnProviderVO);

            return;
        }

        ovnProviderVO = new NetworkServiceProviderVO();
        ovnProviderVO.setUuid(Platform.getUuid());
        ovnProviderVO.setName("OVN Network Service Provider");
        ovnProviderVO.setDescription("OVN Network Service Provider");
        ovnProviderVO.getNetworkServiceTypes().add(NetworkServiceType.DHCP.toString());
        ovnProviderVO.getNetworkServiceTypes().add(NetworkServiceType.DNS.toString());
        ovnProviderVO.getNetworkServiceTypes().add(NetworkServiceType.SNAT.toString());
        ovnProviderVO.getNetworkServiceTypes().add(NetworkServiceType.PortForwarding.toString());
        ovnProviderVO.getNetworkServiceTypes().add(EipConstant.EIP_NETWORK_SERVICE_TYPE);
        ovnProviderVO.getNetworkServiceTypes().add(LoadBalancerConstants.LB_NETWORK_SERVICE_TYPE_STRING);
        ovnProviderVO.getNetworkServiceTypes().add(IPsecConstants.IPSEC_NETWORK_SERVICE_TYPE.toString());
        ovnProviderVO.getNetworkServiceTypes().add(SecurityGroupConstant.SECURITY_GROUP_NETWORK_SERVICE_TYPE);
        ovnProviderVO.getNetworkServiceTypes().add(UserdataConstant.USERDATA_TYPE_STRING);
        ovnProviderVO.getNetworkServiceTypes().add(VipQosConstants.VIPQOS_NETWORK_SERVICE_TYPE.toString());
        ovnProviderVO.getNetworkServiceTypes().add(NetworkServiceType.HostRoute.toString());
        ovnProviderVO.setType(OvnControllerConstant.OVN_CONTROLLER_TYPE);
        ovnProviderVO = dbf.persistAndRefresh(ovnProviderVO);
        logger.info("Success create OVN Network Service Provider");
    }

    @Override
    public void beforeCreateL2Network(APICreateL2NetworkMsg msg) throws NetworkException {

    }

    @Override
    public void afterCreateL2Network(L2NetworkInventory l2Network) {
        if (!l2Network.getvSwitchType().equals(L2NetworkConstant.VSWITCH_TYPE_OVN_DPDK)) {
            return;
        }

        NetworkServiceProviderL2NetworkRefVO ref = new NetworkServiceProviderL2NetworkRefVO();
        ref.setL2NetworkUuid(l2Network.getUuid());
        ref.setNetworkServiceProviderUuid(ovnProviderVO.getUuid());
        dbf.persist(ref);
        logger.debug(String.format("successfully attach ovn network service provider[uuid:%s] to the L2 network[uuid:%s, name:%s]",
                ovnProviderVO.getUuid(), l2Network.getUuid(), l2Network.getName()));
    }

    @Override
    public List<L3NetworkInventory> filterAttachableL3Network(VmInstanceInventory vm, List<L3NetworkInventory> l3s) {
        List<String> l3Uuids = vm.getVmNics().stream().map(VmNicInventory::getL3NetworkUuid).collect(Collectors.toList());
        if (l3s.isEmpty()) {
            return l3s;
        }

        List<L3NetworkInventory> ret = new ArrayList<>();
        String sdncontrollerUuid = null;
        for (String l3Uuid : l3Uuids) {
            sdncontrollerUuid = L3NetworkHelper.getSdnControllerUuidFromL3Uuid(l3Uuid);
            if (sdncontrollerUuid != null) {
                break;
            }
        }
        if (sdncontrollerUuid == null) {
            //no sdn controller l3 attached, if new l3 is ovn l3, ovn must has beed attached host where vm is running
            for (L3NetworkInventory l3 : l3s) {
                String newControllerUuid = L3NetworkHelper.getSdnControllerUuidFromL3Uuid(l3.getUuid());
                if (newControllerUuid == null) {
                    ret.add(l3);
                } else {
                    if (Q.New(SdnControllerHostRefVO.class)
                            .eq(SdnControllerHostRefVO_.sdnControllerUuid, newControllerUuid)
                            .eq(SdnControllerHostRefVO_.hostUuid, vm.getHostUuid()).isExists()) {
                        ret.add(l3);
                    } //else {
                        //filter the l3
                    //}
                }
            }
            return ret;
        }

        for (L3NetworkInventory l3 : l3s) {
            String controllerUuid = L3NetworkHelper.getSdnControllerUuidFromL3Uuid(l3.getUuid());
            if (controllerUuid == null || controllerUuid.equals(sdncontrollerUuid)) {
                ret.add(l3);
            }
        }

        return ret;
    }
}
