package org.zstack.network.ovn;

import org.springframework.beans.factory.annotation.Autowired;
import org.zstack.core.cloudbus.CloudBus;
import org.zstack.core.db.DatabaseFacade;
import org.zstack.core.workflow.FlowChainBuilder;
import org.zstack.header.Component;
import org.zstack.header.core.workflow.FlowChain;
import org.zstack.header.errorcode.OperationFailureException;
import org.zstack.header.network.service.SdnControllerDhcp;
import org.zstack.network.l2.L2NetworkSystemTags;
import org.zstack.network.securitygroup.SecurityGroupSdnBackend;
import org.zstack.network.service.NetworkServiceManager;
import org.zstack.sdnController.*;
import org.zstack.header.network.sdncontroller.SdnControllerVO;
import org.zstack.utils.Utils;
import org.zstack.utils.logging.CLogger;

import java.util.List;

import static org.zstack.core.Platform.operr;

public class OvnControllerFactory implements SdnControllerFactory, Component {
    private static final CLogger logger = Utils.getLogger(OvnControllerFactory.class);
    SdnControllerType sdnControllerType = new SdnControllerType(OvnControllerConstant.OVN_CONTROLLER_TYPE);

    @Autowired
    CloudBus bus;
    @Autowired
    DatabaseFacade dbf;
    @Autowired
    private NetworkServiceManager nsMgr;

    private List<String> ovnControllerSyncFlows;

    private FlowChainBuilder ovnSyncFlowsBuilder;

    @Override
    public SdnControllerType getVendorType() {
        return sdnControllerType;
    }

    @Override
    public SdnControllerVO persistSdnController(SdnControllerVO vo) {
        vo = dbf.persistAndRefresh(vo);
        return vo;
    }

    @Override
    public SdnController getSdnController(SdnControllerVO vo) {
        return new OvnController(vo);
    }

    @Override
    public SdnController getSdnController(String l2NetworkUuid) {
        String ovnControllerUuid = L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID.getTokenByResourceUuid(l2NetworkUuid, L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID_TOKEN);
        if (ovnControllerUuid != null) {
            SdnControllerVO vo = dbf.findByUuid(ovnControllerUuid, SdnControllerVO.class);
            return new OvnController(vo);
        }

        throw new OperationFailureException(operr("l2 network[uuid:%s] is not attached sdn controller", l2NetworkUuid));
    }

    @Override
    public SdnControllerL2 getSdnControllerL2(SdnControllerVO vo) {
        return new OvnController(vo);
    }

    @Override
    public SdnControllerL2 getSdnControllerL2(String l2NetworkUuid) {
        String ovnControllerUuid = L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID.getTokenByResourceUuid(l2NetworkUuid, L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID_TOKEN);
        if (ovnControllerUuid != null) {
            SdnControllerVO vo = dbf.findByUuid(ovnControllerUuid, SdnControllerVO.class);
            return new OvnController(vo);
        }

        throw new OperationFailureException(operr("l2 network[uuid:%s] is not attached sdn controller", l2NetworkUuid));
    }

    @Override
    public SdnControllerDhcp getSdnControllerDhcp(SdnControllerVO vo) {
        return new OvnControllerDhcp(vo);
    }

    @Override
    public SdnControllerDhcp getSdnControllerDhcp(String l2NetworkUuid) {
        String ovnControllerUuid = L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID.getTokenByResourceUuid(l2NetworkUuid, L2NetworkSystemTags.L2_NETWORK_SDN_CONTROLLER_UUID_TOKEN);
        if (ovnControllerUuid != null) {
            SdnControllerVO vo = dbf.findByUuid(ovnControllerUuid, SdnControllerVO.class);
            return new OvnControllerDhcp(vo);
        }

        throw new OperationFailureException(operr("l2 network[uuid:%s] is not attached sdn controller", l2NetworkUuid));
    }

    @Override
    public SecurityGroupSdnBackend getSdnControllerSecurityGroup(SdnControllerVO vo) {
        return new OvnSecurityGroupBackend(vo);
    }

    public List<String> getOvnControllerSyncFlows() {
        return ovnControllerSyncFlows;
    }

    public void setOvnControllerSyncFlows(List<String> ovnControllerSyncFlows) {
        this.ovnControllerSyncFlows = ovnControllerSyncFlows;
    }

    protected void buildWorkFlowBuilder() {
        ovnSyncFlowsBuilder = FlowChainBuilder.newBuilder().setFlowClassNames(ovnControllerSyncFlows).construct();
    }

    @Override
    public FlowChain getSyncChain() {
        FlowChain c = ovnSyncFlowsBuilder.build();
        return c;
    }

    @Override
    public boolean start() {
        buildWorkFlowBuilder();
        return true;
    }

    @Override
    public boolean stop() {
        return true;
    }
}
