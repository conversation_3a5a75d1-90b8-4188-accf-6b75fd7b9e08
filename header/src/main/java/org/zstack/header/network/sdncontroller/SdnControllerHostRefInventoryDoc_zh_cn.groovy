package org.zstack.header.network.sdncontroller



doc {

	title "在这里输入结构的名称"

	field {
		name "sdnControllerUuid"
		desc ""
		type "String"
		since "5.3.28"
	}
	field {
		name "hostUuid"
		desc "物理机UUID"
		type "String"
		since "5.3.28"
	}
	field {
		name "vSwitchType"
		desc ""
		type "String"
		since "5.3.28"
	}
	field {
		name "vtepIp"
		desc ""
		type "String"
		since "5.3.28"
	}
	field {
		name "nicPciAddresses"
		desc ""
		type "String"
		since "5.3.28"
	}
	field {
		name "nicDrivers"
		desc ""
		type "String"
		since "5.3.28"
	}
	field {
		name "netmask"
		desc ""
		type "String"
		since "5.3.28"
	}
	field {
		name "bondMode"
		desc ""
		type "String"
		since "5.3.28"
	}
	field {
		name "lacpMode"
		desc ""
		type "String"
		since "5.3.28"
	}
}
