package org.zstack.testlib.premium

import org.springframework.http.HttpEntity
import org.zstack.ai.BentoBasedModelStorageBackend
import org.zstack.compute.host.MevocoKVMAgentCommands
import org.zstack.compute.host.MevocoKVMConstant
import org.zstack.core.Platform
import org.zstack.core.db.Q
import org.zstack.ha.HaKvmHostSiblingChecker
import org.zstack.ha.SelfFencerKvmBackend
import org.zstack.header.volume.VolumeType
import org.zstack.header.volume.VolumeVO
import org.zstack.header.volume.VolumeVO_
import org.zstack.header.vpc.VpcConstants
import org.zstack.kvm.KVMAgentCommands
import org.zstack.network.huawei.imaster.HuaweiIMasterHelper
import org.zstack.network.huawei.imaster.HuaweiIMasterNceFabricCommands
import org.zstack.network.ovn.OvnControllerCommands
import org.zstack.network.service.vipQos.flat.FlatVipQosBackend
import org.zstack.network.service.vipQos.vyos.VyosVipQosBackend
import org.zstack.sdk.AttachAppBuildSystemToZoneAction
import org.zstack.storage.primary.ceph.CephHostHeartbeatChecker
import org.zstack.storage.primary.filesystem.AbstractFileSystemHostHeartbeatChecker
import org.zstack.storage.primary.imagestore.ceph.CephPrimaryStorageImageStoreBackend
import org.zstack.storage.primary.imagestore.local.LocalStorageImageStoreKvmBackend
import org.zstack.storage.primary.imagestore.nfs.NfsPrimaryStorageImageStoreBackend
import org.zstack.storage.primary.sharedblock.HaSanlockHostChecker
import org.zstack.storage.primary.smp.KvmBackend
import org.zstack.testlib.*
import org.zstack.testlib.premium.appcenter.AppBuildSystemSpec
import org.zstack.testlib.premium.baremetal.BaremetalClusterSpec
import org.zstack.testlib.premium.baremetal.BaremetalPxeServerSpec
import org.zstack.testlib.premium.baremetal2.BareMetal2ClusterSpec
import org.zstack.testlib.premium.baremetal2.BareMetal2ProvisionNetworkSpec
import org.zstack.testlib.premium.crypto.InfoSecSecretResourcePoolSpec
import org.zstack.testlib.premium.vmware.VCenterSpec
import org.zstack.testlib.premium.plugin.block.BlockPrimaryStorageSpec
import org.zstack.utils.gson.JSONObjectUtil
import org.zstack.vpc.VpcRouterCommands

/**
 * Created by xing5 on 2017/2/20.
 */
class ZonePremiumSpec extends ZoneSpec {
    List<BaremetalPxeServerSpec> pxeServers = []
    List<BareMetal2ClusterSpec> bareMetal2Clusters = []
    List<BareMetal2ProvisionNetworkSpec> bareMetal2ProvisionNetworks = []
    List<VpcHaGroupSpec> vpcHaGroups = []
    List<VpcVRouterSpec> vpcVRouters = []
    List<String> buildSystemToAttach = []
    List<VCenterSpec> vCenters = []

    ZonePremiumSpec(EnvSpec envSpec) {
        super(envSpec)
    }

    @Override
    void doPost(sessionId) {
        buildSystemToAttach.each { String bsName ->
            AppBuildSystemSpec bs = findSpec(bsName, AppBuildSystemSpec.class) as AppBuildSystemSpec
            def a = new AttachAppBuildSystemToZoneAction()
            a.zoneUuid = inventory.uuid
            a.buildSystemUuid = bs.inventory.uuid
            a.sessionId = sessionId
            def res = a.call()
            assert res.error == null : "AttachAppBuildSystemToZoneAction failure: ${JSONObjectUtil.toJsonString(res.error)}"
        }

        super.doPost(sessionId)
    }

    IPsecSpec ipsec(@DelegatesTo(strategy = Closure.DELEGATE_FIRST, value = IPsecSpec.class) Closure cl) {
        def spec = new IPsecSpec(envSpec)
        cl.delegate = spec
        cl.resolveStrategy = Closure.DELEGATE_FIRST
        cl()
        addChild(spec)
        return spec
    }

    VCenterSpec vCenter(@DelegatesTo(strategy = Closure.DELEGATE_FIRST, value = VCenterSpec.class) Closure c) {
        def vSpec = new VCenterSpec(envSpec)
        c.delegate = vSpec
        c.resolveStrategy = Closure.DELEGATE_FIRST
        c()
        addChild(vSpec)
        vCenters.add(vSpec)
        return vSpec
    }

    class Simulators implements Simulator {
        @Override
        void registerSimulators(EnvSpec xspec) {
            def simulator = { arg1, arg2 ->
                xspec.simulator(arg1, arg2)
            }

            simulator(SelfFencerKvmBackend.SETUP_SELF_FENCER_PATH) { HttpEntity<String> e ->
                def rsp = new SelfFencerKvmBackend.AgentRsp()
                rsp.success = true
                return rsp
            }

            simulator(HaKvmHostSiblingChecker.SCAN_HOST_PATH) { HttpEntity<String> e ->
                def rsp = new HaKvmHostSiblingChecker.ScanRsp()
                rsp.result = HaKvmHostSiblingChecker.RET_SUCCESS
                return rsp
            }

            simulator(HaSanlockHostChecker.SANLOCK_SCAN_HOST_PATH) { HttpEntity<String> e ->
                def rsp = new HaSanlockHostChecker.ScanRsp()
                rsp.result = Collections.emptyMap() as HashMap<String, Boolean>
                return rsp
            }

            simulator(CephHostHeartbeatChecker.CEPH_HOST_HEARTBEAT_CHECK_PATH) { HttpEntity<String> e ->
                CephHostHeartbeatChecker.CheckHostHeartbeatCmd cmd = JSONObjectUtil.toObject(e.body, CephHostHeartbeatChecker.CheckHostHeartbeatCmd.class)
                def rsp = new CephHostHeartbeatChecker.CheckHostHeartbeatRsp()
                rsp.result = new HashMap<>()
                rsp.result.put(cmd.poolNames.get(0), Boolean.FALSE)
                return rsp
            }

            simulator(AbstractFileSystemHostHeartbeatChecker.FILESYSTEM_CHECK_VMSTATE_PATH) { HttpEntity<String> e ->
                AbstractFileSystemHostHeartbeatChecker.CheckFileSystemVmStateCmd cmd = JSONObjectUtil.toObject(e.body, AbstractFileSystemHostHeartbeatChecker.CheckFileSystemVmStateCmd.class)
                def rsp = new AbstractFileSystemHostHeartbeatChecker.CheckFileSystemVmStateRsp()
                rsp.result = new HashMap<>()
                rsp.result.put(cmd.primaryStorageUuid, Boolean.TRUE)
                return rsp
            }


            simulator(MevocoKVMConstant.KVM_VM_CHANGE_PASSWORD_PATH) { HttpEntity<String> e ->
                def cmd = JSONObjectUtil.toObject(e.body, MevocoKVMAgentCommands.ChangeVmPasswordCmd.class)
                def rsp = new MevocoKVMAgentCommands.ChangeVmPasswordResponse()
                rsp.success = true
                rsp.vmAccountPerference = cmd.accountPerference
                return rsp
            }

            simulator(MevocoKVMConstant.KVM_VM_RECOVER_VOLUMES_PATH) {
                return new KVMAgentCommands.AgentResponse()
            }

            simulator(LocalStorageImageStoreKvmBackend.RESIZE_VOLUME_PATH) {
                return new LocalStorageImageStoreKvmBackend.ResizeVolumeRsp()
            }

            simulator(LocalStorageImageStoreKvmBackend.COMMIT_PATH) {
                def rsp = new LocalStorageImageStoreKvmBackend.CommitVolumeAsImageRsp()
                rsp.backupStorageInstallPath = "zstore://test-image/" + Platform.getUuid()
                return rsp
            }

            simulator(NfsPrimaryStorageImageStoreBackend.RESIZE_VOLUME_PATH) {
                return new NfsPrimaryStorageImageStoreBackend.ResizeVolumeRsp()
            }

            simulator(KvmBackend.RESIZE_VOLUME_PATH) {
                return new KvmBackend.ResizeVolumeRsp()
            }

            simulator(CephPrimaryStorageImageStoreBackend.RESIZE_VOLUME_PATH) {
                return new CephPrimaryStorageImageStoreBackend.ResizeVolumeRsp()
            }

            simulator(MevocoKVMConstant.RESIZE_VOLUME) {
                return new MevocoKVMAgentCommands.ResizeVolumeResponse()
            }

            simulator(MevocoKVMConstant.KVM_VM_CHECK_VOLUME_PATH) { HttpEntity<String> e ->
                def cmd = JSONObjectUtil.toObject(e.body, MevocoKVMAgentCommands.CheckVmVolumesCmd.class)
                List<String> usingVolsUuids = Q.New(VolumeVO.class).select(VolumeVO_.uuid)
                        .in(VolumeVO_.type, Arrays.asList(VolumeType.Root, VolumeType.Data))
                        .eq(VolumeVO_.vmInstanceUuid, cmd.uuid)
                        .listValues()
                assert cmd.volumes.size() == usingVolsUuids.size()
                assert cmd.volumes.stream().allMatch({vol -> usingVolsUuids.contains(vol.volumeUuid)})
                return new KVMAgentCommands.AgentResponse()
            }

            simulator(VyosVipQosBackend.VR_SET_VIP_QOS) {
                return new VyosVipQosBackend.SetVipQosRsp()
            }

            simulator(VyosVipQosBackend.VR_DELETE_VIP_QOS) {
                return new VyosVipQosBackend.DeleteVipQosRsp()
            }

            simulator(VyosVipQosBackend.VR_DELETE_VIPALL_QOS) {
                return new VyosVipQosBackend.DeleteVipAllQosRsp()
            }

            simulator(FlatVipQosBackend.FLAT_SET_VIP_QOS) {
                return new FlatVipQosBackend.SetVipQosRsp()
            }

            simulator(FlatVipQosBackend.FLAT_DELETE_VIP_QOS) {
                return new FlatVipQosBackend.DeleteVipQosRsp()
            }

            simulator(FlatVipQosBackend.FLAT_DELETE_VIPALL_QOS) {
                return new FlatVipQosBackend.DeleteVipAllQosRsp()
            }

            simulator(VpcConstants.VR_SET_VPCDNS_PATH) {
                return new VpcRouterCommands.VpcRouterSetDnsRsp()
            }

            simulator(BentoBasedModelStorageBackend.DELETE_DATASET) {
                return new BentoBasedModelStorageBackend.DeleteDatasetResponse()
            }

            simulator(BentoBasedModelStorageBackend.DELETE_MODEL_SERVICE) {
                return new BentoBasedModelStorageBackend.Response()
            }

            simulator(BentoBasedModelStorageBackend.CREATE_NGINX_RULE) {
                def rsp = new BentoBasedModelStorageBackend.Response()
                rsp.success = true
                return rsp
            }

            simulator(BentoBasedModelStorageBackend.DELETE_NGINX_RULE) {
                def rsp = new BentoBasedModelStorageBackend.Response()
                rsp.success = true
                return rsp
            }

            simulator(OvnControllerCommands.OVN_PING_PATH) {
                return new OvnControllerCommands.PingRsp()
            }

            simulator(OvnControllerCommands.OVN_NETWORKS_PATH) { HttpEntity<String> e ->
                OvnControllerCommands.LogicalSwitchCmd cmd =
                        JSONObjectUtil.toObject(e.getBody(), OvnControllerCommands.LogicalSwitchCmd.class)

                def rsp = new OvnControllerCommands.LogicalSwitchRsp()
                rsp.setSuccess(true)
                for (OvnControllerCommands.LogicalSwitchTo to : cmd.logicalSwitches) {
                    OvnControllerCommands.LogicalSwitchRet ret = new OvnControllerCommands.LogicalSwitchRet()
                    ret.setLogicalSwitchUuid(to.uuid)
                    ret.setLogicalSwitchName(to.name)
                    rsp.getRets().add(ret)
                }

                return rsp
            }

            simulator(OvnControllerCommands.OVN_VM_NICS_PATH) {HttpEntity<String> e ->
                OvnControllerCommands.LogicalSwitchPortCmd cmd =
                        JSONObjectUtil.toObject(e.getBody(), OvnControllerCommands.LogicalSwitchPortCmd.class)

                def rsp = new OvnControllerCommands.LogicalSwitchPortRsp()
                rsp.success = true
                rsp.nics = new ArrayList<>()

                for (OvnControllerCommands.LogicalSwitchPortTo to : cmd.nics) {
                    OvnControllerCommands.LogicalSwitchPortRet ret = new OvnControllerCommands.LogicalSwitchPortRet()
                    ret.logicalPortName = cmd.nics.get(0).name
                    ret.logicalPortUuid = cmd.nics.get(0).uuid
                    rsp.nics.add(ret)
                }


                return rsp
            }

            simulator(OvnControllerCommands.OVN_DHCP_OPTIONS_PATH) {HttpEntity<String> e ->
                OvnControllerCommands.DhcpOptionsCmd cmd =
                        JSONObjectUtil.toObject(e.getBody(), OvnControllerCommands.DhcpOptionsCmd.class)

                def rsp = new OvnControllerCommands.DhcpOptionsRsp()
                rsp.success = true
                
                return rsp
            }

            simulator(OvnControllerCommands.OVN_VM_NICS_QOS_PATH) {HttpEntity<String> e ->
                OvnControllerCommands.VmNicQosCmd cmd =
                        JSONObjectUtil.toObject(e.getBody(), OvnControllerCommands.VmNicQosCmd.class)

                def rsp = new OvnControllerCommands.VmNicQosRsp()
                rsp.success = true

                return rsp
            }

            simulator(OvnControllerCommands.OVN_SECURITY_GROUP_PATH) { HttpEntity<String> e ->
                OvnControllerCommands.VmNicRefreshSecurityGroupCmd cmd =
                        JSONObjectUtil.toObject(e.getBody(), OvnControllerCommands.VmNicRefreshSecurityGroupCmd.class)

                def rsp = new OvnControllerCommands.VmNicRefreshSecurityGroupRsp()
                rsp.success = true

                return rsp
            }

            String fabricId = "018a7946-156e-413d-b9ce-4d8a56c87c36";
            String tenantId = "dd8c1298-58c2-4d01-963e-18ed920bc708";
            String portId1 = "a11993d6-ab70-34b9-abae-e05e5468e411";
            String portId2 = "a11993d6-ab70-34b9-abae-e05e5468e412";
            String portId3 = "a11993d6-ab70-34b9-abae-e05e5468e413";
            String portId4 = "a11993d6-ab70-34b9-abae-e05e5468e414";
            String portId5 = "a11993d6-ab70-34b9-abae-e05e5468e415";
            String portId6 = "a11993d6-ab70-34b9-abae-e05e5468e416";
            String portId7 = "a11993d6-ab70-34b9-abae-e05e5468e417";
            String portId8 = "a11993d6-ab70-34b9-abae-e05e5468e418";
            String switchDeviceId = "b9d708c4-27c6-30b1-b9ff-fe2d989c3a48";
            String vpcId = "414e2ec4-e3bc-40a4-b53d-cdb6bc16edc7"

            simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_TOKEN_PATH) { HttpEntity<String> e ->
                    def rsp = new HuaweiIMasterNceFabricCommands.LoginRsp()
                    rsp.data = new HuaweiIMasterNceFabricCommands.LoginStruct()
                    rsp.data.token_id = "token"

                    return rsp
            }

            simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_TENANT_PATH) { HttpEntity<String> e ->
                def rsp = new HuaweiIMasterNceFabricCommands.GetTenantRsp()
                def tenant = new HuaweiIMasterNceFabricCommands.TenantStruct()
                def res = new HuaweiIMasterNceFabricCommands.TenantResPoolStruct()
                res.fabricIds = [fabricId]

                tenant.id = tenantId
                tenant.name = "user1"
                tenant.resPool = res
                tenant.description = "test tenant"

                rsp.tenant = [tenant]

                return rsp
            }

            simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_FABRIC_PATH) { HttpEntity<String> e ->
                def rsp = new HuaweiIMasterNceFabricCommands.GetFabricRsp()
                def fabric = new HuaweiIMasterNceFabricCommands.FabricStruct()
                fabric.id = fabricId
                fabric.name = "fabric1"
                fabric.description = "test fabric"

                rsp.fabric = [fabric]

                return rsp
            }

            simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_SWITCH_PATH) { HttpEntity<String> e ->
                def rsp = new HuaweiIMasterNceFabricCommands.GetSwitchRsp()
                def hswitch = new HuaweiIMasterNceFabricCommands.SwitchStruct()
                def member = new HuaweiIMasterNceFabricCommands.SwitchMemberStruct()

                member.type = "NONE"

                hswitch.id = switchDeviceId
                hswitch.name = "huawei_152"
                hswitch.ip = "************"
                hswitch.mac = "00:00:00:00:00:01"
                hswitch.mode = "CE6881-48S6CQ"
                hswitch.softWare = "V200R021C00SPC200"

                rsp.devices = [hswitch]

                return rsp
            }

            simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_SWITCH_PORT_PATH) { HttpEntity<String> e ->
                def rsp = new HuaweiIMasterNceFabricCommands.GetSwitchPortRsp()
                def switchDevicePort = new HuaweiIMasterNceFabricCommands.SwitchDevicePort()

                // simulate 6 port:
                // port1 ---> host1 eth1
                // port2 ---> host1 eth2, eth2 and eth3 are ab bond
                // port3 ---> host1 eth3
                // port4 ---> host2 eth1
                // port5 ---> host2 eth2, eth2 and eth3 are lacp bond
                // port6 ---> host2 eth3
                // port7 ---> unlink
                // port8 ---> unlink
                def port1 = new HuaweiIMasterNceFabricCommands.SwitchDevicePortStruct()
                port1.portId = portId1
                port1.portName = "10GE1/0/1"
                port1.portType = "IFMIPHYTYPE10GE"

                def port2 = new HuaweiIMasterNceFabricCommands.SwitchDevicePortStruct()
                port2.portId = portId2
                port2.portName = "10GE1/0/2"
                port2.portType = "IFMIPHYTYPE10GE"

                def port3 = new HuaweiIMasterNceFabricCommands.SwitchDevicePortStruct()
                port3.portId = portId3
                port3.portName = "10GE1/0/3"
                port3.portType = "IFMIPHYTYPE10GE"

                def port4 = new HuaweiIMasterNceFabricCommands.SwitchDevicePortStruct()
                port4.portId = portId4
                port4.portName = "10GE1/0/4"
                port4.portType = "IFMIPHYTYPE10GE"

                def port5 = new HuaweiIMasterNceFabricCommands.SwitchDevicePortStruct()
                port5.portId = portId5
                port5.portName = "10GE1/0/5"
                port5.portType = "IFMIPHYTYPE10GE"
                port5.ethTrunkName = "Eth-Trunk1"

                def port6 = new HuaweiIMasterNceFabricCommands.SwitchDevicePortStruct()
                port6.portId = portId6
                port6.portName = "10GE1/0/6"
                port6.portType = "IFMIPHYTYPE10GE"
                port6.ethTrunkName = "Eth-Trunk1"

                def port7 = new HuaweiIMasterNceFabricCommands.SwitchDevicePortStruct()
                port7.portId = portId7
                port7.portName = "10GE1/0/7"
                port7.portType = "IFMIPHYTYPE10GE"

                def port8 = new HuaweiIMasterNceFabricCommands.SwitchDevicePortStruct()
                port8.portId = portId8
                port8.portName = "10GE1/0/8"
                port8.portType = "IFMIPHYTYPE10GE"

                switchDevicePort.portList = [port1, port2, port3, port4, port5, port6, port7, port8]
                switchDevicePort.deviceId = switchDeviceId
                rsp.devicePortList = [switchDevicePort]

                return rsp
            }

            simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_VPC_PATH) { HttpEntity<String> e ->
                def rsp = new HuaweiIMasterNceFabricCommands.GetVpcRsp()
                def vpc = new HuaweiIMasterNceFabricCommands.VpcStruct()

                vpc.id = vpcId
                vpc.name = "vpc1"
                vpc.description = "test vpc"
                vpc.tenantId = tenantId
                vpc.fabricId = [fabricId]

                rsp.network = [vpc]

                return rsp
            }

            simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_LOGICAL_SWITCHES_PATH) { HttpEntity<String> e ->
                def rsp = new HuaweiIMasterNceFabricCommands.CreateHuaweiLogicalSwitchRsp()
                return rsp
            }

            simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_LOGICAL_SWITCH_PATH) { HttpEntity<String> e ->
                def rsp = new HuaweiIMasterNceFabricCommands.GetHuaweiLogicalSwitchRsp()
                HuaweiIMasterNceFabricCommands.HuaweiLogicalSwitchStruct lsStruct = new HuaweiIMasterNceFabricCommands.HuaweiLogicalSwitchStruct();
                lsStruct.id = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(Platform.uuid)
                lsStruct.vni = 10000
                rsp.logicalSwitch = [lsStruct]
                return rsp
            }

            simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_LOGICAL_PORTS_PATH) { HttpEntity<String> e ->
                def rsp = new HuaweiIMasterNceFabricCommands.CreateHuaweiLogicalSwitchPortRsp()
                return rsp
            }

            simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_LOGICAL_PORT_PATH) { HttpEntity<String> e ->
                def rsp = new HuaweiIMasterNceFabricCommands.GetHuaweiLogicalSwitchPortRsp()
                //HuaweiIMasterNceFabricCommands.HuaweiLogicalSwitchPortStruct struct = new HuaweiIMasterNceFabricCommands.HuaweiLogicalSwitchPortStruct()
                //struct.id = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(Platform.uuid)
                //rsp.port = [struct]
                return rsp
            }

            simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_LOGICAL_ROUTERS_PATH) { HttpEntity<String> e ->
                def rsp = new HuaweiIMasterNceFabricCommands.CreateHuaweiLogicalRouterRsp()
                return rsp
            }

            simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_LOGICAL_ROUTER_PATH) { HttpEntity<String> e ->
                def rsp = new HuaweiIMasterNceFabricCommands.GetHuaweiLogicalRouterRsp()
                HuaweiIMasterNceFabricCommands.HuaweiLogicalRouterStruct struct = new HuaweiIMasterNceFabricCommands.HuaweiLogicalRouterStruct()
                struct.id = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(Platform.uuid)
                rsp.router = [struct]
                return rsp
            }

            simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_LOGICAL_LINKS_PATH) { HttpEntity<String> e ->
                def rsp = new HuaweiIMasterNceFabricCommands.CreateHuaweiLogicalLinkRsp()
                return rsp
            }

            simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_LOGICAL_LINK_PATH) { HttpEntity<String> e ->
                def rsp = new HuaweiIMasterNceFabricCommands.DeleteHuaweiLogicalLinkRsp()
                return rsp
            }

            simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_SUBNETS_PATH) { HttpEntity<String> e ->
                def rsp = new HuaweiIMasterNceFabricCommands.CreateHuaweiSubnetRsp()
                return rsp
            }

            simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_SUBNET_PATH) { HttpEntity<String> e ->
                def rsp = new HuaweiIMasterNceFabricCommands.DeleteHuaweiSubnetRsp()
                return rsp
            }

            simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_SYSTEM_INFO_PATH) { HttpEntity<String> e ->
                def rsp = new HuaweiIMasterNceFabricCommands.GetSystemInfoRsp()
                rsp.productVersion = "V100R023C00SPC201"
                return rsp
            }
        }
    }

    PrimaryStorageSpec sbgPrimaryStorage(@DelegatesTo(strategy = Closure.DELEGATE_FIRST, value = SharedBlockGroupPrimaryStorageSpec.class) Closure c) {
        def nspec = new SharedBlockGroupPrimaryStorageSpec(envSpec)
        c.delegate = nspec
        c.resolveStrategy = Closure.DELEGATE_FIRST
        c()
        addChild(nspec)
        primaryStorage.add(nspec)
        return nspec
    }

    PrimaryStorageSpec miniStorage(@DelegatesTo(strategy = Closure.DELEGATE_FIRST, value = MiniStorageSpec.class) Closure c) {
        def nspec = new MiniStorageSpec(envSpec)
        c.delegate = nspec
        c.resolveStrategy = Closure.DELEGATE_FIRST
        c()
        addChild(nspec)
        primaryStorage.add(nspec)
        return nspec
    }

    PrimaryStorageSpec blockPrimaryStorage(@DelegatesTo(strategy =  Closure.DELEGATE_FIRST, value = BlockPrimaryStorageSpec.class) Closure c) {
        def nspec = new BlockPrimaryStorageSpec(envSpec)
        c.delegate = nspec
        c.resolveStrategy = Closure.DELEGATE_FIRST
        c()
        addChild(nspec)
        primaryStorage.add(nspec)
        return nspec
    }

    BaremetalClusterSpec baremetalCluster(@DelegatesTo(strategy = Closure.DELEGATE_FIRST, value = BaremetalClusterSpec.class) Closure c) {
        def cspec = new BaremetalClusterSpec(envSpec)
        c.delegate = cspec
        c.resolveStrategy = Closure.DELEGATE_FIRST
        c()
        addChild(cspec)
        clusters.add(cspec)
        return cspec
    }

    BaremetalPxeServerSpec baremetalPxeServer(@DelegatesTo(strategy = Closure.DELEGATE_FIRST, value = BaremetalPxeServerSpec.class) Closure c) {
        def pspec = new BaremetalPxeServerSpec(envSpec)
        c.delegate = pspec
        c.resolveStrategy = Closure.DELEGATE_FIRST
        c()
        addChild(pspec)
        pxeServers.add(pspec)
        return pspec
    }

    BareMetal2ClusterSpec bareMetal2Cluster(@DelegatesTo(strategy = Closure.DELEGATE_FIRST, value = BareMetal2ClusterSpec.class) Closure c) {
        def spec = new BareMetal2ClusterSpec(envSpec)
        c.delegate = spec
        c.resolveStrategy = Closure.DELEGATE_FIRST
        c()
        addChild(spec)
        bareMetal2Clusters.add(spec)
        return spec
    }

    BareMetal2ProvisionNetworkSpec bareMetal2ProvisionNetwork(@DelegatesTo(strategy = Closure.DELEGATE_FIRST, value = BareMetal2ProvisionNetworkSpec.class) Closure c) {
        def spec = new BareMetal2ProvisionNetworkSpec(envSpec)
        c.delegate = spec
        c.resolveStrategy = Closure.DELEGATE_FIRST
        c()
        addChild(spec)
        bareMetal2ProvisionNetworks.add(spec)
        return spec
    }

    VpcHaGroupSpec vpcHaGroup(@DelegatesTo(strategy = Closure.DELEGATE_FIRST, value = VpcHaGroupSpec.class) Closure c) {
        def pspec = new VpcHaGroupSpec(envSpec)
        c.delegate = pspec
        c.resolveStrategy = Closure.DELEGATE_FIRST
        c()
        addChild(pspec)
        vpcHaGroups.add(pspec)
        return pspec
    }

    VpcVRouterSpec vpcVRouter(@DelegatesTo(strategy = Closure.DELEGATE_FIRST, value = VpcVRouterSpec.class) Closure c) {
        def pspec = new VpcVRouterSpec(envSpec)
        c.delegate = pspec
        c.resolveStrategy = Closure.DELEGATE_FIRST
        c()
        addChild(pspec)
        vpcVRouters.add(pspec)
        return pspec
    }

    @SpecMethod
    void attachBuildSystem(String...names) {
        names.each { String bsName ->
            preCreate {
                addDependency(bsName, AppBuildSystemSpec.class)
            }

            buildSystemToAttach.add(bsName)
        }
    }

    InfoSecSecretResourcePoolSpec infoSecSecretResourcePool(@DelegatesTo(strategy = Closure.DELEGATE_FIRST, value = InfoSecSecretResourcePoolSpec.class) Closure c) {
        def spec = new InfoSecSecretResourcePoolSpec(envSpec)
        c.delegate = spec
        c.resolveStrategy = Closure.DELEGATE_FIRST
        c()
        addChild(spec)
        return spec
    }
}
