package org.zstack.test.integration.premium.sdncontroller

import org.springframework.http.HttpEntity
import org.zstack.compute.bonding.HostNetworkBondingConstant
import org.zstack.compute.host.MevocoKVMAgentCommands
import org.zstack.compute.host.MevocoKVMConstant
import org.zstack.core.Platform
import org.zstack.core.db.Q
import org.zstack.core.db.SQL
import org.zstack.header.host.NetworkInterfaceType
import org.zstack.header.network.l2.L2NetworkConstant
import org.zstack.network.hostNetworkInterface.HostNetworkInterfaceVO
import org.zstack.network.hostNetworkInterface.HostNetworkInterfaceVO_
import org.zstack.network.hostNetworkInterface.PhysicalSwitchPortVO
import org.zstack.network.hostNetworkInterface.PhysicalSwitchPortVO_
import org.zstack.network.hostNetworkInterface.PhysicalSwitchVO
import org.zstack.network.huawei.imaster.HuaweiIMasterNceFabricCommands
import org.zstack.network.huawei.imaster.HuaweiIMasterSystemTags
import org.zstack.network.huawei.imaster.HuaweiIMasterConstant
import org.zstack.network.huawei.imaster.HuaweiIMasterTenantVO
import org.zstack.network.ovn.OvnControllerCommands
import org.zstack.sdk.*
import org.zstack.sdnController.SdnControllerSystemTags
import org.zstack.sdnController.header.SdnControllerConstant
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.gson.JSONObjectUtil

/**
 * <AUTHOR>
 * @date 2025/07/14
 */
class TestHuaweiSdnVxlanPoolCase extends PremiumSubCase {
    EnvSpec env
    KVMHostInventory kvm

    @Override
    void setup() {
        useSpring(SdnControllerTest.springSpec)
    }

    @Override
    void environment() {
        env = SdnControllerTestEnv.BasicVpc()
    }

    @Override
    void test() {
        env.create {
            TestAddHuaweiSdnController()
            TestHuaweiSdnVxlanPoolLifeCycle()
            TestHuaweiSdnReturnEmpty()
            cleanEnv()
        }
    }

    void TestAddHuaweiSdnController() {
        HostInventory h1 = env.inventoryByName("kvm-1")
        HostInventory h2 = env.inventoryByName("kvm-2")

        env.simulator(MevocoKVMConstant.GET_HOST_NETWORK_FACTS) { HttpEntity<String> e, EnvSpec spec ->

            OvnControllerCommands.LogicalSwitchPortCmd cmd =
                    JSONObjectUtil.toObject(e.getBody(), OvnControllerCommands.LogicalSwitchPortCmd.class)

            def reply = new MevocoKVMAgentCommands.GetHostNetworkBondingResponse()

            HostNetworkInterfaceInventory inv1 = new HostNetworkInterfaceInventory()
            inv1.setInterfaceName("eth1")
            inv1.setSpeed(10000L)
            inv1.setCarrierActive(true)
            inv1.setMac("ac:1f:6b:93:6c:8c")
            inv1.setPciDeviceAddress("0e:00.0")
            inv1.setInterfaceType(NetworkInterfaceType.noMaster.toString());

            HostNetworkInterfaceInventory inv2 = new HostNetworkInterfaceInventory()
            inv2.setInterfaceName("eth2")
            inv2.setSpeed(10000L)
            inv2.setCarrierActive(true)
            inv2.setMac("ac:1f:6b:93:6c:8d")
            inv2.setPciDeviceAddress("0e:00.1")
            inv2.setInterfaceType(NetworkInterfaceType.bondingSlave.toString())

            HostNetworkInterfaceInventory inv3 = new HostNetworkInterfaceInventory()
            inv3.setInterfaceName("eth3")
            inv3.setSpeed(10000L)
            inv3.setCarrierActive(true)
            inv3.setMac("ac:1f:6b:93:6c:8e")
            inv3.setPciDeviceAddress("0e:01.0")
            inv3.setInterfaceType(NetworkInterfaceType.bondingSlave.toString());

            reply.nics = [inv1, inv2, inv3] as List<HostNetworkInterfaceInventory>

            HostNetworkBondingInventory bond0 = new HostNetworkBondingInventory();
            bond0.setUuid(Platform.getUuid());
            bond0.setBondingName("bond0");
            bond0.setBondingType("noBridge")
            bond0.setType(HostNetworkBondingConstant.LINUX_BONDING_TYPE.toString());
            bond0.setXmitHashPolicy("layer3+4");
            bond0.setMiiStatus('up')
            bond0.setMode(L2NetworkConstant.BONDING_MODE_AB);
            bond0.setSlaves([inv2, inv3] as List<HostNetworkInterfaceInventory>);

            reply.bondings = [bond0]

            return reply;
        }

        reconnectHost {
            uuid = h1.uuid
        }

        env.simulator(MevocoKVMConstant.GET_HOST_NETWORK_FACTS) { HttpEntity<String> e, EnvSpec spec ->

            OvnControllerCommands.LogicalSwitchPortCmd cmd =
                    JSONObjectUtil.toObject(e.getBody(), OvnControllerCommands.LogicalSwitchPortCmd.class)

            def reply = new MevocoKVMAgentCommands.GetHostNetworkBondingResponse()

            HostNetworkInterfaceInventory inv1 = new HostNetworkInterfaceInventory()
            inv1.setInterfaceName("eth1")
            inv1.setSpeed(10000L)
            inv1.setCarrierActive(true)
            inv1.setMac("ac:1f:6b:93:6c:9c")
            inv1.setPciDeviceAddress("0e:00.0")
            inv1.setInterfaceType(NetworkInterfaceType.noMaster.toString());

            HostNetworkInterfaceInventory inv2 = new HostNetworkInterfaceInventory()
            inv2.setInterfaceName("eth2")
            inv2.setSpeed(10000L)
            inv2.setCarrierActive(true)
            inv2.setMac("ac:1f:6b:93:6c:9d")
            inv2.setPciDeviceAddress("0e:00.1")
            inv2.setInterfaceType(NetworkInterfaceType.bondingSlave.toString())

            HostNetworkInterfaceInventory inv3 = new HostNetworkInterfaceInventory()
            inv3.setInterfaceName("eth3")
            inv3.setSpeed(10000L)
            inv3.setCarrierActive(true)
            inv3.setMac("ac:1f:6b:93:6c:9e")
            inv3.setPciDeviceAddress("0e:01.0")
            inv3.setInterfaceType(NetworkInterfaceType.noMaster.toString());

            reply.nics = [inv1, inv2, inv3] as List<HostNetworkInterfaceInventory>

            HostNetworkBondingInventory bond0 = new HostNetworkBondingInventory();
            bond0.setBondingName("bond0");
            bond0.setType(HostNetworkBondingConstant.LINUX_BONDING_TYPE);
            bond0.setMode(L2NetworkConstant.BONDING_MODE_LACP);
            bond0.setUuid(Platform.getUuid());
            bond0.setBondingType("noBridge")
            bond0.setXmitHashPolicy("layer3+4");
            bond0.setMiiStatus('up')
            bond0.setSlaves([inv2, inv3] as List<HostNetworkInterfaceInventory>);

            reply.bondings = [bond0]

            return reply;
        }
        reconnectHost {
            uuid = h2.uuid
        }

        expect(AssertionError.class) {
            addSdnController {
                name = "huawei-imaster"
                vendorType = HuaweiIMasterConstant.HUAWEI_IMASTER_CONTROLLER_TYPE
                description = "huawei imaster"
                ip = "*************"
                userName = "admin"
                password = "password"
            }
        }

        SdnControllerInventory sdn = addSdnController {
            name = "huawei-imaster"
            vendorType = HuaweiIMasterConstant.HUAWEI_IMASTER_CONTROLLER_TYPE
            description = "huawei imaster"
            ip = "*************"
            userName = "admin"
            password = "password"
            systemTags = [
                    SdnControllerSystemTags.VLAN_RANGE.instantiateTag(
                            [(SdnControllerSystemTags.START_VLAN_TOKEN): 1000,
                             (SdnControllerSystemTags.END_VLAN_TOKEN): 2000]),
                    SdnControllerSystemTags.VLAN_RANGE.instantiateTag(
                            [(SdnControllerSystemTags.START_VLAN_TOKEN): 3000,
                             (SdnControllerSystemTags.END_VLAN_TOKEN): 3100])
                    ]
        }

        List<Map<String, String>> ranges = SdnControllerSystemTags.VLAN_RANGE.getTokensOfTagsByResourceUuid(sdn.uuid)
        assert ranges.size() == 2

        List<PhysicalSwitchInventory> phySwitches = queryPhysicalSwitch {}
        assert phySwitches.size() == 1

        changeSdnController {
            uuid = sdn.uuid
            password = "password1"
            vlanRanges = ["100-200"]
        }

        ranges = SdnControllerSystemTags.VLAN_RANGE.getTokensOfTagsByResourceUuid(sdn.uuid)
        assert ranges.size() == 1
        assert ranges.get(0).get(SdnControllerSystemTags.START_VLAN_TOKEN) == "100"
        assert ranges.get(0).get(SdnControllerSystemTags.END_VLAN_TOKEN) == "200"

        // result depend on the simulate in ZonePremiumSpec
        List<HuaweiIMasterTenantInventory> tenants = queryHuaweiIMasterTenant {}
        assert tenants.size() == 1
        List<HuaweiIMasterFabricInventory> fabrics = queryHuaweiIMasterFabric {}
        assert fabrics.size() == 1
        List<HuaweiIMasterVRouterInventory> vpcs = queryHuaweiIMasterVpc {}
        assert vpcs.size() == 1

        // link host1: eth1, eth2, eth3 ---> 10GE1/0/1, 10GE1/0/2, 10GE1/0/3
        // link host2: eth1, eth2, eth3 ---> 10GE1/0/4, 10GE1/0/5, 10GE1/0/6
        String host1Eth1Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h1.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth1").findValue()
        String host1Eth2Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h1.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth2").findValue()
        String host1Eth3Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h1.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth3").findValue()
        String host2Eth1Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h2.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth1").findValue()
        String host2Eth2Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h2.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth2").findValue()
        String host2Eth3Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h2.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth3").findValue()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/1")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host1Eth1Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/2")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host1Eth2Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/3")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host1Eth3Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/4")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host2Eth1Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/5")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host2Eth2Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/6")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host2Eth3Uuid).update()

        pullHuaweiIMasterController {
            uuid = sdn.uuid
        }

        pullHuaweiIMasterController {
            uuid = sdn.uuid
            pullSwitch = true
        }

        // delete vpc, tenant, fabric to test pullHuaweiIMasterController
        for (HuaweiIMasterVpcInventory vpc : vpcs) {
            deleteHuaweiIMasterVpc {
                uuid = vpc.uuid
            }
        }

        for (HuaweiIMasterTenantInventory tenant : tenants) {
            deleteHuaweiIMasterTenant {
                uuid = tenant.uuid
            }
        }

        for (HuaweiIMasterFabricInventory fabric : fabrics) {
            deleteHuaweiIMasterFabric {
                uuid = fabric.uuid
            }
        }

        pullHuaweiIMasterController {
            uuid = sdn.uuid
        }

        tenants = queryHuaweiIMasterTenant {}
        assert tenants.size() == 1
        assert tenants.fabricIds.size() == 1

        fabrics = queryHuaweiIMasterFabric {}
        assert fabrics.size() == 1
        vpcs = queryHuaweiIMasterVpc {}
        assert vpcs.size() == 1
    }

    void TestHuaweiSdnVxlanPoolLifeCycle() {
        ZoneInventory zone = env.inventoryByName("zone")
        ClusterInventory cluster = env.inventoryByName("cluster")
        SdnControllerInventory sdn = querySdnController {conditions = ["name=huawei-imaster"]} [0]

        L2VxlanNetworkPoolInventory hardPool = createL2HardwareVxlanNetworkPool {
            name = "hardwareVxlanPool1"
            type = SdnControllerConstant.HARDWARE_VXLAN_NETWORK_POOL_TYPE
            sdnControllerUuid = sdn.uuid
            physicalInterface = "eth0"
            zoneUuid = zone.uuid
        }

        expect(AssertionError.class) {
            attachL2NetworkToCluster {
                l2NetworkUuid = hardPool.getUuid()
                clusterUuid = cluster.uuid
            }
        }

        deleteL2Network {
            uuid = hardPool.uuid
        }

        L2VxlanNetworkPoolInventory hardPool2 = createL2HardwareVxlanNetworkPool {
            name = "hardwareVxlanPool2"
            type = SdnControllerConstant.HARDWARE_VXLAN_NETWORK_POOL_TYPE
            sdnControllerUuid = sdn.uuid
            physicalInterface = "eth1"
            zoneUuid = zone.uuid
        }

        attachL2NetworkToCluster {
            l2NetworkUuid = hardPool2.getUuid()
            clusterUuid = cluster.uuid
        }

        detachL2NetworkFromCluster {
            l2NetworkUuid = hardPool2.getUuid()
            clusterUuid = cluster.uuid
        }

        deleteL2Network {
            uuid = hardPool2.uuid
        }

        L2VxlanNetworkPoolInventory hardPool3 = createL2HardwareVxlanNetworkPool {
            name = "hardwareVxlanPool3"
            type = SdnControllerConstant.HARDWARE_VXLAN_NETWORK_POOL_TYPE
            sdnControllerUuid = sdn.uuid
            physicalInterface = "bond0"
            zoneUuid = zone.uuid
        }

        attachL2NetworkToCluster {
            l2NetworkUuid = hardPool3.getUuid()
            clusterUuid = cluster.uuid
        }
    }

    void TestHuaweiSdnReturnEmpty() {
        SdnControllerInventory sdn = querySdnController {conditions = ["name=huawei-imaster"]} [0]

        env.simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_SWITCH_PORT_PATH) { HttpEntity<String> e, EnvSpec spec ->
            def rsp = new HuaweiIMasterNceFabricCommands.GetSwitchPortRsp()
            return rsp
        }
        pullHuaweiIMasterController {
            uuid = sdn.uuid
        }

        env.simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_SWITCH_PATH) { HttpEntity<String> e, EnvSpec spec ->
            def rsp = new HuaweiIMasterNceFabricCommands.GetSwitchRsp()
            return rsp
        }
        pullHuaweiIMasterController {
            pullSwitch = true
            uuid = sdn.uuid
        }

        env.simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_VPC_PATH) { HttpEntity<String> e, EnvSpec spec ->
            def rsp = new HuaweiIMasterNceFabricCommands.GetVpcRsp()
            return rsp
        }
        pullHuaweiIMasterController {
            pullSwitch = true
            uuid = sdn.uuid
        }

        env.simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_TENANT_PATH) { HttpEntity<String> e, EnvSpec spec ->
            def rsp = new HuaweiIMasterNceFabricCommands.GetTenantRsp()
            return rsp
        }
        pullHuaweiIMasterController {
            uuid = sdn.uuid
        }

        env.simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_FABRIC_PATH) { HttpEntity<String> e, EnvSpec spec ->
            def rsp = new HuaweiIMasterNceFabricCommands.GetFabricRsp()
            return rsp
        }
        pullHuaweiIMasterController {
            uuid = sdn.uuid
        }
    }

    void cleanEnv() {
        List<SdnControllerInventory> invs = querySdnController {}
        for (SdnControllerInventory inv : invs) {
            removeSdnController {
                uuid = inv.uuid
            }
        }
        SQL.New(PhysicalSwitchPortVO.class).delete()
        SQL.New(PhysicalSwitchVO.class).delete()
    }

    @Override
    void clean() {
        env.delete()
    }
}
