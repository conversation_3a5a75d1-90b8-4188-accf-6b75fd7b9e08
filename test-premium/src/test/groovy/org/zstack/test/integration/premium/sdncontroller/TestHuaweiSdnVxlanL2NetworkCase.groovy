package org.zstack.test.integration.premium.sdncontroller

import org.apache.commons.collections.list.SynchronizedList
import org.springframework.http.HttpEntity
import org.zstack.compute.bonding.HostNetworkBondingConstant
import org.zstack.compute.host.MevocoKVMAgentCommands
import org.zstack.compute.host.MevocoKVMConstant
import org.zstack.core.Platform
import org.zstack.core.db.Q
import org.zstack.core.db.SQL
import org.zstack.header.host.NetworkInterfaceType
import org.zstack.header.network.l2.L2NetworkConstant
import org.zstack.kvm.KVMAgentCommands
import org.zstack.kvm.KVMConstant
import org.zstack.network.hostNetworkInterface.HostNetworkInterfaceVO
import org.zstack.network.hostNetworkInterface.HostNetworkInterfaceVO_
import org.zstack.network.hostNetworkInterface.PhysicalSwitchPortVO
import org.zstack.network.hostNetworkInterface.PhysicalSwitchPortVO_
import org.zstack.network.hostNetworkInterface.PhysicalSwitchVO
import org.zstack.network.huawei.imaster.HuaweiIMasterHelper
import org.zstack.network.huawei.imaster.HuaweiIMasterNceFabricCommands
import org.zstack.network.huawei.imaster.HuaweiIMasterSystemTags
import org.zstack.network.huawei.imaster.HuaweiIMasterConstant
import org.zstack.sdk.*
import org.zstack.sdk.huawei.imaster.HuaweiIMasterTenantInventory
import org.zstack.sdk.huawei.imaster.HuaweiIMasterVpcInventory
import org.zstack.sdnController.SdnControllerSystemTags
import org.zstack.sdnController.header.SdnControllerConstant
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.HttpError
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.gson.JSONObjectUtil

/**
 * <AUTHOR>
 * @date 2025/07/15
 */
class TestHuaweiSdnVxlanL2NetworkCase extends PremiumSubCase {
    EnvSpec env
    KVMHostInventory kvm
    PhysicalSwitchPortVO p1
    PhysicalSwitchPortVO p2
    PhysicalSwitchPortVO p3
    PhysicalSwitchPortVO p4
    PhysicalSwitchPortVO p5
    PhysicalSwitchPortVO p6
    PhysicalSwitchPortVO p7
    PhysicalSwitchPortVO p8

    @Override
    void setup() {
        useSpring(SdnControllerTest.springSpec)
    }

    @Override
    void environment() {
        env = SdnControllerTestEnv.BasicVpc()
    }

    @Override
    void test() {
        env.create {
            TestAddHuaweiSdnController()
            TestHuaweiSdnVxlanNetworkLifeCycle()
            TestHuaweiSdnVxlanNetworkRollBack()
            cleanEnv()
        }
    }

    void simulateLinkHostInterfaceToPhysicalSwitch() {
        HostInventory h1 = env.inventoryByName("kvm-1")
        HostInventory h2 = env.inventoryByName("kvm-2")

        // link host1: eth1, eth2, eth3 ---> 10GE1/0/1, 10GE1/0/2, 10GE1/0/3
        // link host2: eth1, eth2, eth3 ---> 10GE1/0/4, 10GE1/0/5, 10GE1/0/6
        String host1Eth1Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h1.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth1").findValue()
        String host1Eth2Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h1.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth2").findValue()
        String host1Eth3Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h1.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth3").findValue()
        String host2Eth1Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h2.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth1").findValue()
        String host2Eth2Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h2.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth2").findValue()
        String host2Eth3Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h2.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth3").findValue()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/1")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host1Eth1Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/2")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host1Eth2Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/3")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host1Eth3Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/4")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host2Eth1Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/5")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host2Eth2Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/6")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host2Eth3Uuid).update()

        p1 = Q.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/1").find()
        p2 = Q.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/2").find()
        p3 = Q.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/3").find()
        p4 = Q.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/4").find()
        p5 = Q.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/5").find()
        p6 = Q.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/6").find()
        p7 = Q.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/7").find()
        p8 = Q.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/8").find()
        
    }

    void TestAddHuaweiSdnController() {
        ZoneInventory zone = env.inventoryByName("zone")
        ClusterInventory cluster = env.inventoryByName("cluster")
        HostInventory h1 = env.inventoryByName("kvm-1")
        HostInventory h2 = env.inventoryByName("kvm-2")

        env.simulator(MevocoKVMConstant.GET_HOST_NETWORK_FACTS) { HttpEntity<String> e, EnvSpec spec ->
            def reply = new MevocoKVMAgentCommands.GetHostNetworkBondingResponse()

            HostNetworkInterfaceInventory inv1 = new HostNetworkInterfaceInventory()
            inv1.setInterfaceName("eth1")
            inv1.setSpeed(10000L)
            inv1.setCarrierActive(true)
            inv1.setMac("ac:1f:6b:93:6c:8c")
            inv1.setPciDeviceAddress("0e:00.0")
            inv1.setInterfaceType(NetworkInterfaceType.noMaster.toString());

            HostNetworkInterfaceInventory inv2 = new HostNetworkInterfaceInventory()
            inv2.setInterfaceName("eth2")
            inv2.setSpeed(10000L)
            inv2.setCarrierActive(true)
            inv2.setMac("ac:1f:6b:93:6c:8d")
            inv2.setPciDeviceAddress("0e:00.1")
            inv2.setInterfaceType(NetworkInterfaceType.bondingSlave.toString())

            HostNetworkInterfaceInventory inv3 = new HostNetworkInterfaceInventory()
            inv3.setInterfaceName("eth3")
            inv3.setSpeed(10000L)
            inv3.setCarrierActive(true)
            inv3.setMac("ac:1f:6b:93:6c:8e")
            inv3.setPciDeviceAddress("0e:01.0")
            inv3.setInterfaceType(NetworkInterfaceType.bondingSlave.toString());

            reply.nics = [inv1, inv2, inv3] as List<HostNetworkInterfaceInventory>

            HostNetworkBondingInventory bond0 = new HostNetworkBondingInventory();
            bond0.setUuid(Platform.getUuid());
            bond0.setBondingName("bond0");
            bond0.setBondingType("noBridge")
            bond0.setType(HostNetworkBondingConstant.LINUX_BONDING_TYPE.toString());
            bond0.setXmitHashPolicy("layer3+4");
            bond0.setMiiStatus('up')
            bond0.setMode(L2NetworkConstant.BONDING_MODE_AB);
            bond0.setSlaves([inv2, inv3] as List<HostNetworkInterfaceInventory>);

            reply.bondings = [bond0]

            return reply;
        }

        reconnectHost {
            uuid = h1.uuid
        }

        env.simulator(MevocoKVMConstant.GET_HOST_NETWORK_FACTS) { HttpEntity<String> e, EnvSpec spec ->
            def reply = new MevocoKVMAgentCommands.GetHostNetworkBondingResponse()

            HostNetworkInterfaceInventory inv1 = new HostNetworkInterfaceInventory()
            inv1.setInterfaceName("eth1")
            inv1.setSpeed(10000L)
            inv1.setCarrierActive(true)
            inv1.setMac("ac:1f:6b:93:6c:9c")
            inv1.setPciDeviceAddress("0e:00.0")
            inv1.setInterfaceType(NetworkInterfaceType.noMaster.toString());

            HostNetworkInterfaceInventory inv2 = new HostNetworkInterfaceInventory()
            inv2.setInterfaceName("eth2")
            inv2.setSpeed(10000L)
            inv2.setCarrierActive(true)
            inv2.setMac("ac:1f:6b:93:6c:9d")
            inv2.setPciDeviceAddress("0e:00.1")
            inv2.setInterfaceType(NetworkInterfaceType.bondingSlave.toString())

            HostNetworkInterfaceInventory inv3 = new HostNetworkInterfaceInventory()
            inv3.setInterfaceName("eth3")
            inv3.setSpeed(10000L)
            inv3.setCarrierActive(true)
            inv3.setMac("ac:1f:6b:93:6c:9e")
            inv3.setPciDeviceAddress("0e:01.0")
            inv3.setInterfaceType(NetworkInterfaceType.noMaster.toString());

            reply.nics = [inv1, inv2, inv3] as List<HostNetworkInterfaceInventory>

            HostNetworkBondingInventory bond0 = new HostNetworkBondingInventory();
            bond0.setBondingName("bond0");
            bond0.setType(HostNetworkBondingConstant.LINUX_BONDING_TYPE);
            bond0.setMode(L2NetworkConstant.BONDING_MODE_LACP);
            bond0.setUuid(Platform.getUuid());
            bond0.setBondingType("noBridge")
            bond0.setXmitHashPolicy("layer3+4");
            bond0.setMiiStatus('up')
            bond0.setSlaves([inv2, inv3] as List<HostNetworkInterfaceInventory>);

            reply.bondings = [bond0]

            return reply;
        }
        reconnectHost {
            uuid = h2.uuid
        }

        expect(AssertionError.class) {
            addSdnController {
                name = "huawei-imaster"
                vendorType = HuaweiIMasterConstant.HUAWEI_IMASTER_CONTROLLER_TYPE
                description = "huawei imaster"
                ip = "*************"
                userName = "admin"
                password = "password"
            }
        }

        addSdnController {
            name = "huawei-imaster"
            vendorType = HuaweiIMasterConstant.HUAWEI_IMASTER_CONTROLLER_TYPE
            description = "huawei imaster"
            ip = "*************"
            userName = "admin"
            password = "password"
            systemTags = [
                    SdnControllerSystemTags.VLAN_RANGE.instantiateTag(
                            [(SdnControllerSystemTags.START_VLAN_TOKEN): 1000,
                             (SdnControllerSystemTags.END_VLAN_TOKEN): 2000]),
                    SdnControllerSystemTags.VNI_RANGE.instantiateTag(
                            [(SdnControllerSystemTags.START_VNI_TOKEN): 1000,
                             (SdnControllerSystemTags.END_VNI_TOKEN): 2000])
                    ]
        }
        simulateLinkHostInterfaceToPhysicalSwitch()
    }

    void TestHuaweiSdnVxlanNetworkLifeCycle() {
        ZoneInventory zone = env.inventoryByName("zone")
        ClusterInventory cluster = env.inventoryByName("cluster")
        SdnControllerInventory sdn = querySdnController {conditions = ["name=huawei-imaster"]} [0]

        HuaweiIMasterNceFabricCommands.CreateHuaweiLogicalSwitchCmd lsCmd = null
        env.afterSimulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_LOGICAL_SWITCHES_PATH) {
            rsp, HttpEntity<String> e ->
                lsCmd = JSONObjectUtil.toObject(e.body, HuaweiIMasterNceFabricCommands.CreateHuaweiLogicalSwitchCmd.class)
                return rsp
        }

        HuaweiIMasterNceFabricCommands.CreateHuaweiLogicalSwitchPortCmd lspCmd = null
        env.afterSimulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_LOGICAL_PORTS_PATH) {
            rsp, HttpEntity<String> e ->
                lspCmd = JSONObjectUtil.toObject(e.body, HuaweiIMasterNceFabricCommands.CreateHuaweiLogicalSwitchPortCmd.class)
                return rsp
        }

        HuaweiIMasterNceFabricCommands.UpdateHuaweiLogicalSwitchPortCmd lspUpdateCmd = null
        env.simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_LOGICAL_PORT_PATH) { HttpEntity<String> e, EnvSpec spec ->
            String method = e.headers.get("method")
            if (method == "GET") {
                def rsp = new HuaweiIMasterNceFabricCommands.GetHuaweiLogicalSwitchPortRsp()
                rsp.port = []
                return rsp
            } else if (method == "DELETE") {
                def rsp = new HuaweiIMasterNceFabricCommands.DeleteHuaweiLogicalSwitchPortRsp()
                return rsp
            } else if (method == "PUT") {
                def rsp = new HuaweiIMasterNceFabricCommands.UpdateHuaweiLogicalSwitchPortRsp()
                lspUpdateCmd = JSONObjectUtil.toObject(e.body, HuaweiIMasterNceFabricCommands.UpdateHuaweiLogicalSwitchPortCmd.class)
                return rsp
            } else {
                throw new HttpError(405, "method not allowed")
            }
        }

        List<KVMAgentCommands.CreateVlanBridgeCmd> vlanCmds = new ArrayList<>()
        env.afterSimulator(KVMConstant.KVM_REALIZE_L2VLAN_NETWORK_PATH) { rsp, HttpEntity<String> e ->
            KVMAgentCommands.CreateVlanBridgeCmd cmd = json(e.body, KVMAgentCommands.CreateVlanBridgeCmd.class)
            vlanCmds.add(cmd)
            return rsp
        }

        def cmds = [] as SynchronizedList<KVMAgentCommands.DeleteVlanBridgeCmd>
        env.afterSimulator(KVMConstant.KVM_DELETE_L2VLAN_NETWORK_PATH) { rsp, HttpEntity<String> e ->
            def deleteVlanBridgeCmd = JSONObjectUtil.toObject(e.body, KVMAgentCommands.DeleteVlanBridgeCmd.class)
            cmds.add(deleteVlanBridgeCmd)
            return rsp
        }

        HuaweiIMasterTenantInventory tenant = queryHuaweiIMasterTenant {}[0]
        HuaweiIMasterVpcInventory vpc = queryHuaweiIMasterVpc {} [0]

        L2VxlanNetworkPoolInventory hardPool = createL2HardwareVxlanNetworkPool {
            name = "hardwareVxlanPool1"
            type = SdnControllerConstant.HARDWARE_VXLAN_NETWORK_POOL_TYPE
            sdnControllerUuid = sdn.uuid
            physicalInterface = "eth1"
            zoneUuid = zone.uuid
        }

        expect(AssertionError.class) {
            createL2HardwareVxlanNetwork {
                poolUuid = hardPool.uuid
                name = "TestVxlan1"
                zoneUuid = zone.uuid
                vni = 1001
                vlan = 1000
            }
        }

        L2VxlanNetworkInventory vx1 = createL2HardwareVxlanNetwork {
            poolUuid = hardPool.uuid
            name = "TestVxlan1"
            zoneUuid = zone.uuid
            vni = 1001
            vlan = 1000
            systemTags = [
                    HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID.instantiateTag(
                            [(HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID_TOKEN): vpc.uuid]),
                    HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_TENANT_UUID.instantiateTag(
                            [(HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_TENANT_UUID_TOKEN): tenant.uuid]),
            ]
        }
        assert lsCmd != null
        assert lsCmd.logicalSwitch.size() == 1
        HuaweiIMasterNceFabricCommands.HuaweiLogicalSwitchStruct lsStruct =  lsCmd.logicalSwitch.get(0)
        assert lsStruct.logicNetworkId == HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(vpc.uuid)
        assert lsStruct.tenantId == HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(tenant.uuid)
        assert lspCmd == null
        assert vlanCmds.size() == 0

        lsCmd = null as HuaweiIMasterNceFabricCommands.CreateHuaweiLogicalSwitchCmd
        attachL2NetworkToCluster {
            l2NetworkUuid = hardPool.getUuid()
            clusterUuid = cluster.uuid
        }
        assert lsCmd == null
        assert lspCmd != null
        assert lspCmd.port.size() == 1
        for (HuaweiIMasterNceFabricCommands.HuaweiLogicalSwitchPortStruct lspStruct : lspCmd.port) {
            assert lspStruct.logicSwitchId == HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(vx1.uuid)
            assert lspStruct.tenantId == HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(tenant.uuid)
            assert lspStruct.fabricId == HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(tenant.fabricIds.get(0))
            assert lspStruct.accessInfo.location.size() == 2
            assert lspStruct.accessInfo.vlan == 1000
        }
        assert vlanCmds.size() == 2
        for (KVMAgentCommands.CreateVlanBridgeCmd vlanCmd : vlanCmds) {
            assert vlanCmd.vlan == 1000
        }

        cmds = [] as SynchronizedList<KVMAgentCommands.DeleteVlanBridgeCmd>
        detachL2NetworkFromCluster {
            l2NetworkUuid = hardPool.getUuid()
            clusterUuid = cluster.uuid
        }
        assert cmds.size() == 2
        for (KVMAgentCommands.DeleteVlanBridgeCmd cmd : cmds) {
            assert cmd.vlan == 1000
            assert cmd.physicalInterfaceName == "eth1"
        }

        attachL2NetworkToCluster {
            l2NetworkUuid = hardPool.getUuid()
            clusterUuid = cluster.uuid
        }

        lsCmd = null as HuaweiIMasterNceFabricCommands.CreateHuaweiLogicalSwitchCmd
        lspCmd = null as HuaweiIMasterNceFabricCommands.CreateHuaweiLogicalSwitchPortCmd
        vlanCmds = new ArrayList<>() as List<KVMAgentCommands.CreateVlanBridgeCmd>
        L2VxlanNetworkInventory vx2 = createL2HardwareVxlanNetwork {
            poolUuid = hardPool.uuid
            name = "TestVxlan2"
            zoneUuid = zone.uuid
            vni = 1002
            vlan = 1003
            systemTags = [
                    HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID.instantiateTag(
                            [(HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID_TOKEN): vpc.uuid]),
                    HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_TENANT_UUID.instantiateTag(
                            [(HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_TENANT_UUID_TOKEN): tenant.uuid]),
            ]
        }
        assert lsCmd != null
        assert lsCmd.logicalSwitch.size() == 1
        lsStruct =  lsCmd.logicalSwitch.get(0)
        assert lsStruct.logicNetworkId == HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(vpc.uuid)
        assert lsStruct.tenantId == HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(tenant.uuid)
        assert lspCmd != null
        assert lspCmd.port.size() == 1
        for (HuaweiIMasterNceFabricCommands.HuaweiLogicalSwitchPortStruct lspStruct : lspCmd.port) {
            assert lspStruct.logicSwitchId == HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(vx2.uuid)
            assert lspStruct.tenantId == HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(tenant.uuid)
            assert lspStruct.fabricId == HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(tenant.fabricIds.get(0))
            assert lspStruct.accessInfo.location.size() == 2
            assert lspStruct.accessInfo.vlan == 1003
        }
        assert vlanCmds.size() == 2
        for (KVMAgentCommands.CreateVlanBridgeCmd vlanCmd : vlanCmds) {
            assert vlanCmd.vlan == 1003
        }

        lsCmd = null as HuaweiIMasterNceFabricCommands.CreateHuaweiLogicalSwitchCmd
        lspCmd = null as HuaweiIMasterNceFabricCommands.CreateHuaweiLogicalSwitchPortCmd
        vlanCmds = new ArrayList<>() as List<KVMAgentCommands.CreateVlanBridgeCmd>

        addKVMHost {
            username = "root"
            password = "password"
            name = "kvm4"
            managementIp = "*********"
            clusterUuid = cluster.uuid
        }
        assert vlanCmds.size() == 2
        for (KVMAgentCommands.CreateVlanBridgeCmd vlanCmd : vlanCmds) {
            assert vlanCmd.vlan == 1003 || vlanCmd.vlan == 1000
        }

        cmds = [] as SynchronizedList<KVMAgentCommands.DeleteVlanBridgeCmd>
        deleteL2Network {
            uuid = vx1.uuid
        }
        assert cmds.size() == 3
        for (KVMAgentCommands.DeleteVlanBridgeCmd cmd : cmds) {
            assert cmd.vlan == 1000
            assert cmd.physicalInterfaceName == "eth1"
        }
    }

    void TestHuaweiSdnVxlanNetworkRollBack() {
        ZoneInventory zone = env.inventoryByName("zone")
        ClusterInventory cluster = env.inventoryByName("cluster")
        SdnControllerInventory sdn = querySdnController {conditions = ["name=huawei-imaster"]} [0]
        L2VxlanNetworkPoolInventory hardPool = queryL2VxlanNetworkPool {conditions = ["name=hardwareVxlanPool1"]} [0]
        HuaweiIMasterVpcInventory vpc = queryHuaweiIMasterVpc {} [0]

        env.simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_LOGICAL_SWITCHES_PATH) {
            HttpEntity<String> e ->
                throw new Exception("mock create logical switch fail")
        }

        expect(AssertionError.class) {
            createL2HardwareVxlanNetwork {
                poolUuid = hardPool.uuid
                name = "TestVxlan1-failed"
                zoneUuid = zone.uuid
                vlan = 1009
                systemTags = [
                        HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID.instantiateTag(
                                [(HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID_TOKEN): vpc.uuid]),
                ]
            }
        }

        List<L2NetworkInventory> res = queryL2Network {conditions=["name=TestVxlan1-failed"]}
        assert res.size() == 0
    }

    void cleanEnv() {
        List<SdnControllerInventory> invs = querySdnController {}
        for (SdnControllerInventory inv : invs) {
            removeSdnController {
                uuid = inv.uuid
            }
        }
        SQL.New(PhysicalSwitchPortVO.class).delete()
        SQL.New(PhysicalSwitchVO.class).delete()
    }

    @Override
    void clean() {
        env.delete()
    }
}
