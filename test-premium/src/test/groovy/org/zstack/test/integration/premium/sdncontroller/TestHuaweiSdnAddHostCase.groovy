package org.zstack.test.integration.premium.sdncontroller

import org.springframework.http.HttpEntity
import org.zstack.compute.bonding.HostNetworkBondingConstant
import org.zstack.compute.host.MevocoKVMAgentCommands
import org.zstack.compute.host.MevocoKVMConstant
import org.zstack.core.Platform
import org.zstack.core.db.Q
import org.zstack.core.db.SQL
import org.zstack.header.host.NetworkInterfaceType
import org.zstack.header.network.l2.L2NetworkConstant
import org.zstack.header.vpc.VpcConstants
import org.zstack.kvm.KVMAgentCommands
import org.zstack.kvm.KVMConstant
import org.zstack.network.hostNetworkInterface.*
import org.zstack.network.hostNetworkInterface.lldp.LldpConstant
import org.zstack.network.hostNetworkInterface.lldp.LldpInfoStruct
import org.zstack.network.hostNetworkInterface.lldp.LldpKvmAgentCommands
import org.zstack.network.huawei.imaster.HuaweiIMasterConstant
import org.zstack.network.huawei.imaster.HuaweiIMasterHelper
import org.zstack.network.huawei.imaster.HuaweiIMasterNceFabricCommands
import org.zstack.network.huawei.imaster.HuaweiIMasterSystemTags
import org.zstack.network.l3.L3NetworkSystemTags
import org.zstack.network.ovn.OvnControllerCommands
import org.zstack.sdk.*
import org.zstack.sdnController.SdnControllerSystemTags
import org.zstack.sdnController.header.SdnControllerConstant
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase
import org.zstack.utils.gson.JSONObjectUtil

/**
 * <AUTHOR>
 * @date 2025/07/16
 */
class TestHuaweiSdnAddHostCase extends PremiumSubCase {
    EnvSpec env
    KVMHostInventory kvm

    @Override
    void setup() {
        useSpring(SdnControllerTest.springSpec)
    }

    @Override
    void environment() {
        env = SdnControllerTestEnv.BasicVpc()
    }

    @Override
    void test() {
        env.create {
            TestAddHuaweiSdnController()
            TestHuaweiSdnL3NetworkLifeCycle()
            cleanEnv()
        }
    }

    void TestAddHuaweiSdnController() {
        ZoneInventory zone = env.inventoryByName("zone")
        ClusterInventory cluster = env.inventoryByName("cluster")
        HostInventory h1 = env.inventoryByName("kvm-1")
        HostInventory h2 = env.inventoryByName("kvm-2")

        env.simulator(MevocoKVMConstant.GET_HOST_NETWORK_FACTS) { HttpEntity<String> e, EnvSpec spec ->

            OvnControllerCommands.LogicalSwitchPortCmd cmd =
                    JSONObjectUtil.toObject(e.getBody(), OvnControllerCommands.LogicalSwitchPortCmd.class)

            def reply = new MevocoKVMAgentCommands.GetHostNetworkBondingResponse()

            HostNetworkInterfaceInventory inv1 = new HostNetworkInterfaceInventory()
            inv1.setInterfaceName("eth1")
            inv1.setSpeed(10000L)
            inv1.setCarrierActive(true)
            inv1.setMac("ac:1f:6b:93:6c:8c")
            inv1.setPciDeviceAddress("0e:00.0")
            inv1.setInterfaceType(NetworkInterfaceType.noMaster.toString());

            HostNetworkInterfaceInventory inv2 = new HostNetworkInterfaceInventory()
            inv2.setInterfaceName("eth2")
            inv2.setSpeed(10000L)
            inv2.setCarrierActive(true)
            inv2.setMac("ac:1f:6b:93:6c:8d")
            inv2.setPciDeviceAddress("0e:00.1")
            inv2.setInterfaceType(NetworkInterfaceType.bondingSlave.toString())

            HostNetworkInterfaceInventory inv3 = new HostNetworkInterfaceInventory()
            inv3.setInterfaceName("eth3")
            inv3.setSpeed(10000L)
            inv3.setCarrierActive(true)
            inv3.setMac("ac:1f:6b:93:6c:8e")
            inv3.setPciDeviceAddress("0e:01.0")
            inv3.setInterfaceType(NetworkInterfaceType.bondingSlave.toString());

            reply.nics = [inv1, inv2, inv3] as List<HostNetworkInterfaceInventory>

            HostNetworkBondingInventory bond0 = new HostNetworkBondingInventory();
            bond0.setUuid(Platform.getUuid());
            bond0.setBondingName("bond0");
            bond0.setBondingType("noBridge")
            bond0.setType(HostNetworkBondingConstant.LINUX_BONDING_TYPE.toString());
            bond0.setXmitHashPolicy("layer3+4");
            bond0.setMiiStatus('up')
            bond0.setMode(L2NetworkConstant.BONDING_MODE_AB);
            bond0.setSlaves([inv2, inv3] as List<HostNetworkInterfaceInventory>);

            reply.bondings = [bond0]

            return reply;
        }

        reconnectHost {
            uuid = h1.uuid
        }

        env.simulator(MevocoKVMConstant.GET_HOST_NETWORK_FACTS) { HttpEntity<String> e, EnvSpec spec ->

            OvnControllerCommands.LogicalSwitchPortCmd cmd =
                    JSONObjectUtil.toObject(e.getBody(), OvnControllerCommands.LogicalSwitchPortCmd.class)

            def reply = new MevocoKVMAgentCommands.GetHostNetworkBondingResponse()

            HostNetworkInterfaceInventory inv1 = new HostNetworkInterfaceInventory()
            inv1.setInterfaceName("eth1")
            inv1.setSpeed(10000L)
            inv1.setCarrierActive(true)
            inv1.setMac("ac:1f:6b:93:6c:9c")
            inv1.setPciDeviceAddress("0e:00.0")
            inv1.setInterfaceType(NetworkInterfaceType.noMaster.toString());

            HostNetworkInterfaceInventory inv2 = new HostNetworkInterfaceInventory()
            inv2.setInterfaceName("eth2")
            inv2.setSpeed(10000L)
            inv2.setCarrierActive(true)
            inv2.setMac("ac:1f:6b:93:6c:9d")
            inv2.setPciDeviceAddress("0e:00.1")
            inv2.setInterfaceType(NetworkInterfaceType.bondingSlave.toString())

            HostNetworkInterfaceInventory inv3 = new HostNetworkInterfaceInventory()
            inv3.setInterfaceName("eth3")
            inv3.setSpeed(10000L)
            inv3.setCarrierActive(true)
            inv3.setMac("ac:1f:6b:93:6c:9e")
            inv3.setPciDeviceAddress("0e:01.0")
            inv3.setInterfaceType(NetworkInterfaceType.noMaster.toString());

            reply.nics = [inv1, inv2, inv3] as List<HostNetworkInterfaceInventory>

            HostNetworkBondingInventory bond0 = new HostNetworkBondingInventory();
            bond0.setBondingName("bond0");
            bond0.setType(HostNetworkBondingConstant.LINUX_BONDING_TYPE);
            bond0.setMode(L2NetworkConstant.BONDING_MODE_LACP);
            bond0.setUuid(Platform.getUuid());
            bond0.setBondingType("noBridge")
            bond0.setXmitHashPolicy("layer3+4");
            bond0.setMiiStatus('up')
            bond0.setSlaves([inv2, inv3] as List<HostNetworkInterfaceInventory>);

            reply.bondings = [bond0]

            return reply;
        }
        reconnectHost {
            uuid = h2.uuid
        }

        SdnControllerInventory sdn = addSdnController {
            name = "huawei-imaster"
            vendorType = HuaweiIMasterConstant.HUAWEI_IMASTER_CONTROLLER_TYPE
            description = "huawei imaster"
            ip = "*************"
            userName = "admin"
            password = "password"
            systemTags = [
                    SdnControllerSystemTags.VLAN_RANGE.instantiateTag(
                            [(SdnControllerSystemTags.START_VLAN_TOKEN): 1000,
                             (SdnControllerSystemTags.END_VLAN_TOKEN): 2000]),
                    SdnControllerSystemTags.VNI_RANGE.instantiateTag(
                            [(SdnControllerSystemTags.START_VNI_TOKEN): 1000,
                             (SdnControllerSystemTags.END_VNI_TOKEN): 2000]),
                    ]
        }

        HuaweiIMasterNceFabricCommands.GetTokenCmd tokenCmd = null
        env.afterSimulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_TOKEN_PATH) {
            rsp, HttpEntity<String> e ->
                tokenCmd = JSONObjectUtil.toObject(e.body, HuaweiIMasterNceFabricCommands.GetTokenCmd.class)
                return rsp
        }

        reconnectSdnController {
            sdnControllerUuid = sdn.uuid
        }
        assert tokenCmd != null


        // link host1: eth1, eth2, eth3 ---> 10GE1/0/1, 10GE1/0/2, 10GE1/0/3
        // link host2: eth1, eth2, eth3 ---> 10GE1/0/4, 10GE1/0/5, 10GE1/0/6
        String host1Eth1Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h1.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth1").findValue()
        String host1Eth2Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h1.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth2").findValue()
        String host1Eth3Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h1.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth3").findValue()
        String host2Eth1Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h2.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth1").findValue()
        String host2Eth2Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h2.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth2").findValue()
        String host2Eth3Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h2.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth3").findValue()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/1")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host1Eth1Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/2")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host1Eth2Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/3")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host1Eth3Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/4")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host2Eth1Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/5")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host2Eth2Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/6")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host2Eth3Uuid).update()

        L2VxlanNetworkPoolInventory hardPool = createL2HardwareVxlanNetworkPool {
            name = "hardwareVxlanPool1"
            type = SdnControllerConstant.HARDWARE_VXLAN_NETWORK_POOL_TYPE
            sdnControllerUuid = sdn.uuid
            physicalInterface = "eth1"
            zoneUuid = zone.uuid
        }

        attachL2NetworkToCluster {
            l2NetworkUuid = hardPool.getUuid()
            clusterUuid = cluster.uuid
        }
        HuaweiIMasterTenantInventory tenant = queryHuaweiIMasterTenant {}[0]
        HuaweiIMasterVpcInventory vpc = queryHuaweiIMasterVpc {} [0]
        L2VxlanNetworkInventory vxlan = createL2HardwareVxlanNetwork {
            poolUuid = hardPool.uuid
            name = "huawei-l2-1"
            zoneUuid = zone.uuid
            vni = 1002
            vlan = 1001
            systemTags = [
                    HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID.instantiateTag(
                            [(HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID_TOKEN): vpc.uuid])
            ]
        }
        HuaweiIMasterVRouterInventory vr = createHuaweiIMasterVRouter {
            name = "R1"
            huaweiVpcUuid = vpc.uuid
        }
        L3NetworkInventory l3 = createL3Network {
            name = "huaweiL3"
            l2NetworkUuid = vxlan.uuid
            type = VpcConstants.VPC_L3_NETWORK_TYPE
            systemTags = [L3NetworkSystemTags.L3_NETWORK_HUAWEI_LOGICAL_ROUTER.instantiateTag(
                    [(L3NetworkSystemTags.L3_NETWORK_HUAWEI_LOGICAL_ROUTER_TOKEN): vr.uuid])]
        }
        attachNetworkServiceToL3Network {
            l3NetworkUuid = l3.uuid
            networkServices = ['Flat':['DHCP', 'Userdata']]
        }
        addIpRange {
            name = "TestIpRange"
            l3NetworkUuid = l3.getUuid()
            startIp = "*************0"
            endIp = "***************"
            gateway = "*************"
            netmask = "*************"
        }

        L2VxlanNetworkInventory vxlan2 = createL2HardwareVxlanNetwork {
            poolUuid = hardPool.uuid
            name = "huawei-l2-2"
            zoneUuid = zone.uuid
            vlan = 1002
            systemTags = [
                    HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID.instantiateTag(
                            [(HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID_TOKEN): vpc.uuid])
            ]
        }
    }

    void TestHuaweiSdnL3NetworkLifeCycle() {
        ImageInventory image = env.inventoryByName("image1")
        ClusterInventory cluster = env.inventoryByName("cluster")
        InstanceOfferingInventory instanceOffering = env.inventoryByName("instanceOffering")
        L2NetworkInventory l2 = queryL2Network { conditions=["name=huawei-l2-1"] }[0]
        L3NetworkInventory l3 = queryL3Network { conditions=["name=huaweiL3"] }[0]

        createVmInstance {
            name = "vm-pub"
            instanceOfferingUuid = instanceOffering.uuid
            imageUuid = image.uuid
            l3NetworkUuids = [l3.uuid]
        }

        GetCandidateZonesClustersHostsForCreatingVmResult result = getCandidateZonesClustersHostsForCreatingVm {
            instanceOfferingUuid = instanceOffering.uuid
            imageUuid = image.uuid
            l3NetworkUuids = [l3.uuid]
        }
        assert result.hosts.size() == 2

        List<KVMAgentCommands.CreateVlanBridgeCmd> createVlanBridgeCmds = new ArrayList<>()
        env.afterSimulator(KVMConstant.KVM_REALIZE_L2VLAN_NETWORK_PATH) { rsp, HttpEntity<String> e ->
            KVMAgentCommands.CreateVlanBridgeCmd createVlanBridgeCmd = json(e.body, KVMAgentCommands.CreateVlanBridgeCmd.class)
            createVlanBridgeCmds.add(createVlanBridgeCmd)
            return rsp
        }

        //add a new host to cluster1
        HostInventory h3 = addKVMHost {
            username = "root"
            password = "password"
            name = "kvm1-3"
            managementIp = "*********"
            clusterUuid = cluster.uuid
        }
        assert createVlanBridgeCmds.size() == 2
        for (KVMAgentCommands.CreateVlanBridgeCmd cmd : createVlanBridgeCmds) {
            assert cmd.vlan == 1001 || cmd.vlan == 1002
        }

        result = getCandidateZonesClustersHostsForCreatingVm {
            instanceOfferingUuid = instanceOffering.uuid
            imageUuid = image.uuid
            l3NetworkUuids = [l3.uuid]
        }
        assert result.hosts.size() == 2
        assert !result.hosts.find { it.uuid == h3.uuid }

        env.simulator(LldpConstant.GET_LLDP_INFO_PATH) { HttpEntity<String> entity, EnvSpec spec ->
            def reply = new LldpKvmAgentCommands.GetLldpInfoResponse()
            reply.lldpInfo = new LldpInfoStruct()
            reply.lldpInfo.chassisId = "mac 00:1e:08:1d:05:ba"
            reply.lldpInfo.timeToLive = 120
            reply.lldpInfo.managementAddress = "**********"
            reply.lldpInfo.systemName = "huawei_152"
            reply.lldpInfo.systemDescription = "huawei CE12800"
            reply.lldpInfo.systemCapabilities = "Bridge, on  Router, on"
            reply.lldpInfo.portId = "10GE1/0/7"
            reply.lldpInfo.portDescription = "10GE1/0/7"
            reply.lldpInfo.vlanId = 3999
            reply.lldpInfo.aggregationPortId = 4294965248L
            reply.lldpInfo.mtu = 9600
            return reply
        }

        PhysicalSwitchPortVO p1 = Q.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/1").find()
        PhysicalSwitchPortVO p2 = Q.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/4").find()
        PhysicalSwitchPortVO p3 = Q.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/7").find()
        List<HuaweiIMasterNceFabricCommands.HuaweiLogicalSwitchPortLocationStruct> locations = new ArrayList<>()
        locations.add(new HuaweiIMasterNceFabricCommands.HuaweiLogicalSwitchPortLocationStruct(
                org.zstack.network.hostNetworkInterface.PhysicalSwitchPortInventory.valueOf(p1)))
        locations.add(new HuaweiIMasterNceFabricCommands.HuaweiLogicalSwitchPortLocationStruct(
                org.zstack.network.hostNetworkInterface.PhysicalSwitchPortInventory.valueOf(p2)))
        HuaweiIMasterNceFabricCommands.HuaweiLogicalSwitchPortStruct struct = new HuaweiIMasterNceFabricCommands.HuaweiLogicalSwitchPortStruct()
        struct.id = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(l2.uuid)
        struct.name = l2.name
        struct.logicSwitchId = HuaweiIMasterHelper.zStackUuidToHuaweiIMasterId(l2.uuid)
        struct.accessInfo = new HuaweiIMasterNceFabricCommands.HuaweiLogicalSwitchPortAccessInfoStruct()
        struct.accessInfo.mode = "Uni"
        struct.accessInfo.type = "Dot1q"
        struct.accessInfo.vlan = 1002
        struct.accessInfo.location = locations

        env.simulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_LOGICAL_PORT_PATH) { HttpEntity<String> e ->
            def portRsp = new HuaweiIMasterNceFabricCommands.GetHuaweiLogicalSwitchPortRsp()
            portRsp.port = [struct]
            return portRsp
        }

        HuaweiIMasterNceFabricCommands.CreateHuaweiLogicalSwitchPortCmd logicalPortCmd = null
        env.afterSimulator(HuaweiIMasterNceFabricCommands.HUAWEI_IMASTER_LOGICAL_PORT_PATH) { rsp, HttpEntity<String> e ->
            logicalPortCmd = json(e.body, HuaweiIMasterNceFabricCommands.CreateHuaweiLogicalSwitchPortCmd .class)
            return rsp
        }

        org.zstack.sdk.HostNetworkInterfaceInventory hintf = queryHostNetworkInterface { conditions =
                ["interfaceName=eth1", "hostUuid=${h3.uuid}"] }[0]
        getHostNetworkInterfaceLldp {
            interfaceUuid = hintf.uuid
        }
        retryInSecs {
            assert logicalPortCmd != null
        }
        assert logicalPortCmd.port.size() == 1
        assert logicalPortCmd.port.get(0).accessInfo.location.size() == 3
        assert logicalPortCmd.port.get(0).accessInfo.location.find { it.portName == "10GE1/0/7" }

        env.simulator(LldpConstant.GET_LLDP_INFO_PATH) { HttpEntity<String> entity, EnvSpec spec ->
            def reply = new LldpKvmAgentCommands.GetLldpInfoResponse()
            reply.lldpInfo = new LldpInfoStruct()
            reply.lldpInfo.chassisId = "mac 00:1e:08:1d:05:ba"
            reply.lldpInfo.timeToLive = 120
            reply.lldpInfo.managementAddress = "**********"
            reply.lldpInfo.systemName = "huawei_152"
            reply.lldpInfo.systemDescription = "huawei CE12800"
            reply.lldpInfo.systemCapabilities = "Bridge, on  Router, on"
            reply.lldpInfo.portId = "10GE1/0/8"
            reply.lldpInfo.portDescription = "10GE1/0/8"
            reply.lldpInfo.vlanId = 3999
            reply.lldpInfo.mtu = 9600
            return reply
        }

        logicalPortCmd = null as HuaweiIMasterNceFabricCommands.CreateHuaweiLogicalSwitchPortCmd
        hintf = queryHostNetworkInterface { conditions =
                ["interfaceName=eth1", "hostUuid=${h3.uuid}"] }[0]
        getHostNetworkInterfaceLldp {
            interfaceUuid = hintf.uuid
        }
        retryInSecs(10,1) {
            assert logicalPortCmd != null
        }
        assert logicalPortCmd.port.size() == 1
        assert logicalPortCmd.port.get(0).accessInfo.location.size() == 3
        assert !logicalPortCmd.port.get(0).accessInfo.location.find { it.portName == "10GE1/0/7" }
        assert logicalPortCmd.port.get(0).accessInfo.location.find { it.portName == "10GE1/0/8" }

        result = getCandidateZonesClustersHostsForCreatingVm {
            instanceOfferingUuid = instanceOffering.uuid
            imageUuid = image.uuid
            l3NetworkUuids = [l3.uuid]
        }
        assert result.hosts.size() == 3
        assert result.hosts.find { it.uuid == h3.uuid }
    }

    void cleanEnv() {
        List<SdnControllerInventory> invs = querySdnController {}
        for (SdnControllerInventory inv : invs) {
            removeSdnController {
                uuid = inv.uuid
            }
        }

    }

    @Override
    void clean() {
        env.delete()
    }
}
