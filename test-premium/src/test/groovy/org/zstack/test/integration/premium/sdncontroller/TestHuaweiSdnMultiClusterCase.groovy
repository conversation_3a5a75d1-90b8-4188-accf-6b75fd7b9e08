package org.zstack.test.integration.premium.sdncontroller

import org.springframework.http.HttpEntity
import org.zstack.compute.bonding.HostNetworkBondingConstant
import org.zstack.compute.host.MevocoKVMAgentCommands
import org.zstack.compute.host.MevocoKVMConstant
import org.zstack.core.Platform
import org.zstack.core.db.Q
import org.zstack.core.db.SQL
import org.zstack.header.host.NetworkInterfaceType
import org.zstack.header.network.l2.L2NetworkConstant
import org.zstack.network.hostNetworkInterface.*
import org.zstack.network.huawei.imaster.HuaweiIMasterConstant
import org.zstack.network.huawei.imaster.HuaweiIMasterSystemTags
import org.zstack.sdk.*
import org.zstack.sdk.huawei.imaster.HuaweiIMasterTenantInventory
import org.zstack.sdk.huawei.imaster.HuaweiIMasterVpcInventory
import org.zstack.sdnController.SdnControllerSystemTags
import org.zstack.sdnController.header.SdnControllerConstant
import org.zstack.testlib.EnvSpec
import org.zstack.testlib.premium.PremiumSubCase

/**
 * <AUTHOR>
 * @date 2025/07/15
 */
class TestHuaweiSdnMultiClusterCase extends PremiumSubCase {
    EnvSpec env
    KVMHostInventory kvm

    @Override
    void setup() {
        useSpring(SdnControllerTest.springSpec)
    }

    @Override
    void environment() {
        env = SdnControllerTestEnv.BasicVpc()
    }

    @Override
    void test() {
        env.create {
            TestAddHuaweiSdnController()
            TestHuaweiSdnVxlanNetworkLifeCycle()
            cleanEnv()
        }
    }

    void simulateLinkHostInterfaceToPhysicalSwitch() {
        HostInventory h1 = env.inventoryByName("kvm-1")
        HostInventory h2 = env.inventoryByName("kvm-2")
        HostInventory h3 = env.inventoryByName("kvm-3")

        // link host1: eth1, eth2, eth3 ---> 10GE1/0/1, 10GE1/0/2, 10GE1/0/3
        // link host2: eth1, eth2, eth3 ---> 10GE1/0/4, 10GE1/0/5, 10GE1/0/6
        // link host3: eth1, eth2,       ---> 10GE1/0/7, 10GE1/0/8
        String host1Eth1Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h1.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth1").findValue()
        String host1Eth2Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h1.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth2").findValue()
        String host1Eth3Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h1.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth3").findValue()
        String host2Eth1Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h2.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth1").findValue()
        String host2Eth2Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h2.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth2").findValue()
        String host2Eth3Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h2.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth3").findValue()
        String host3Eth1Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h3.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth1").findValue()
        String host3Eth2Uuid = Q.New(HostNetworkInterfaceVO.class).select(HostNetworkInterfaceVO_.uuid)
                .eq(HostNetworkInterfaceVO_.hostUuid, h3.uuid)
                .eq(HostNetworkInterfaceVO_.interfaceName, "eth2").findValue()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/1")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host1Eth1Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/2")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host1Eth2Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/3")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host1Eth3Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/4")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host2Eth1Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/5")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host2Eth2Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/6")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host2Eth3Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/7")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host3Eth1Uuid).update()
        SQL.New(PhysicalSwitchPortVO.class).eq(PhysicalSwitchPortVO_.name, "10GE1/0/8")
                .set(PhysicalSwitchPortVO_.peerInterfaceUuid, host3Eth2Uuid).update()
    }

    void TestAddHuaweiSdnController() {
        HostInventory h1 = env.inventoryByName("kvm-1")
        HostInventory h2 = env.inventoryByName("kvm-2")
        HostInventory h3 = env.inventoryByName("kvm-3")

        env.simulator(MevocoKVMConstant.GET_HOST_NETWORK_FACTS) { HttpEntity<String> e, EnvSpec spec ->
            def reply = new MevocoKVMAgentCommands.GetHostNetworkBondingResponse()

            HostNetworkInterfaceInventory inv1 = new HostNetworkInterfaceInventory()
            inv1.setInterfaceName("eth1")
            inv1.setSpeed(10000L)
            inv1.setCarrierActive(true)
            inv1.setMac("ac:1f:6b:93:6c:8c")
            inv1.setPciDeviceAddress("0e:00.0")
            inv1.setInterfaceType(NetworkInterfaceType.noMaster.toString());

            HostNetworkInterfaceInventory inv2 = new HostNetworkInterfaceInventory()
            inv2.setInterfaceName("eth2")
            inv2.setSpeed(10000L)
            inv2.setCarrierActive(true)
            inv2.setMac("ac:1f:6b:93:6c:8d")
            inv2.setPciDeviceAddress("0e:00.1")
            inv2.setInterfaceType(NetworkInterfaceType.bondingSlave.toString())

            HostNetworkInterfaceInventory inv3 = new HostNetworkInterfaceInventory()
            inv3.setInterfaceName("eth3")
            inv3.setSpeed(10000L)
            inv3.setCarrierActive(true)
            inv3.setMac("ac:1f:6b:93:6c:8e")
            inv3.setPciDeviceAddress("0e:01.0")
            inv3.setInterfaceType(NetworkInterfaceType.bondingSlave.toString());

            reply.nics = [inv1, inv2, inv3] as List<HostNetworkInterfaceInventory>

            HostNetworkBondingInventory bond0 = new HostNetworkBondingInventory();
            bond0.setUuid(Platform.getUuid());
            bond0.setBondingName("bond0");
            bond0.setBondingType("noBridge")
            bond0.setType(HostNetworkBondingConstant.LINUX_BONDING_TYPE.toString());
            bond0.setXmitHashPolicy("layer3+4");
            bond0.setMiiStatus('up')
            bond0.setMode(L2NetworkConstant.BONDING_MODE_AB);
            bond0.setSlaves([inv2, inv3] as List<HostNetworkInterfaceInventory>);

            reply.bondings = [bond0]

            return reply;
        }

        reconnectHost {
            uuid = h1.uuid
        }
        reconnectHost {
            uuid = h2.uuid
        }
        reconnectHost {
            uuid = h3.uuid
        }

        addSdnController {
            name = "huawei-imaster"
            vendorType = HuaweiIMasterConstant.HUAWEI_IMASTER_CONTROLLER_TYPE
            description = "huawei imaster"
            ip = "*************"
            userName = "admin"
            password = "password"
            systemTags = [
                    SdnControllerSystemTags.VLAN_RANGE.instantiateTag(
                            [(SdnControllerSystemTags.START_VLAN_TOKEN): 1000,
                             (SdnControllerSystemTags.END_VLAN_TOKEN): 2000])
                    ]
        }
        simulateLinkHostInterfaceToPhysicalSwitch()
    }

    void TestHuaweiSdnVxlanNetworkLifeCycle() {
        ZoneInventory zone = env.inventoryByName("zone")
        ClusterInventory cluster = env.inventoryByName("cluster")
        ClusterInventory cluster2 = env.inventoryByName("cluster-2")
        SdnControllerInventory sdn = querySdnController {conditions = ["name=huawei-imaster"]} [0]

        HuaweiIMasterTenantInventory tenant = queryHuaweiIMasterTenant {}[0]
        HuaweiIMasterVpcInventory vpc = queryHuaweiIMasterVpc {} [0]

        L2VxlanNetworkPoolInventory hardPool = createL2HardwareVxlanNetworkPool {
            name = "hardwareVxlanPool1"
            type = SdnControllerConstant.HARDWARE_VXLAN_NETWORK_POOL_TYPE
            sdnControllerUuid = sdn.uuid
            physicalInterface = "eth1"
            zoneUuid = zone.uuid
        }

        L2VxlanNetworkInventory vx1 = createL2HardwareVxlanNetwork {
            poolUuid = hardPool.uuid
            name = "TestVxlan1"
            zoneUuid = zone.uuid
            vni = 1001
            vlan = 1000
            systemTags = [
                    HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID.instantiateTag(
                            [(HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID_TOKEN): vpc.uuid]),
            ]
        }

        attachL2NetworkToCluster {
            l2NetworkUuid = hardPool.getUuid()
            clusterUuid = cluster.uuid
        }

        attachL2NetworkToCluster {
            l2NetworkUuid = hardPool.getUuid()
            clusterUuid = cluster2.uuid
        }

        detachL2NetworkFromCluster {
            l2NetworkUuid = hardPool.getUuid()
            clusterUuid = cluster.uuid
        }

        detachL2NetworkFromCluster {
            l2NetworkUuid = hardPool.getUuid()
            clusterUuid = cluster2.uuid
        }

        attachL2NetworkToCluster {
            l2NetworkUuid = hardPool.getUuid()
            clusterUuid = cluster.uuid
        }

        attachL2NetworkToCluster {
            l2NetworkUuid = hardPool.getUuid()
            clusterUuid = cluster2.uuid
        }

        deleteL2Network {
            uuid = vx1.uuid
        }

        vx1 = createL2HardwareVxlanNetwork {
            poolUuid = hardPool.uuid
            name = "TestVxlan1"
            zoneUuid = zone.uuid
            vni = 1001
            vlan = 1000
            systemTags = [
                    HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID.instantiateTag(
                            [(HuaweiIMasterSystemTags.L2_NETWORK_HUAWEI_VPC_UUID_TOKEN): vpc.uuid]),
            ]
        }

        deleteL2Network {
            uuid = hardPool.uuid
        }
    }

    void cleanEnv() {
        List<SdnControllerInventory> invs = querySdnController {}
        for (SdnControllerInventory inv : invs) {
            removeSdnController {
                uuid = inv.uuid
            }
        }
        SQL.New(PhysicalSwitchPortVO.class).delete()
        SQL.New(PhysicalSwitchVO.class).delete()
    }

    @Override
    void clean() {
        env.delete()
    }
}
