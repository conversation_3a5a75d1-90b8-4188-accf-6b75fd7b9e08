package org.zstack.sdnController.header;

import org.zstack.header.message.APIEvent;
import org.zstack.header.network.sdncontroller.SdnControllerInventory;
import org.zstack.header.rest.RestResponse;

@RestResponse(allTo = "inventory")
public class APISdnControllerChangeHostEvent extends APIEvent {
    /**
     * @desc see :ref:`SdnControllerInventory`
     */
    private SdnControllerInventory inventory;

    public APISdnControllerChangeHostEvent(String apiId) {
        super(apiId);
    }

    public SdnControllerInventory getInventory() {
        return inventory;
    }

    public APISdnControllerChangeHostEvent() {
        super(null);
    }

    public void setInventory(SdnControllerInventory inventory) {
        this.inventory = inventory;
    }

    public static APISdnControllerChangeHostEvent __example__() {
        APISdnControllerChangeHostEvent event = new APISdnControllerChangeHostEvent();
        SdnControllerInventory inventory = new SdnControllerInventory();

        inventory.setUuid(uuid());
        inventory.setVendorType(SdnControllerConstant.H3C_VCFC_CONTROLLER);
        inventory.setName("sdn-1");
        inventory.setDescription("sdn controller from vendor");
        inventory.setIp("***********");
        inventory.setUsername("admin");
        inventory.setPassword("password");

        event.setInventory(inventory);
        return event;
    }
}
