<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>plugin</artifactId>
        <groupId>org.zstack</groupId>
        <version>5.3.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>mediator</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>eip</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>portForwarding</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>loadBalancer</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>applianceVm</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.zstack</groupId>
            <artifactId>acl</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${project.compiler.version}</version>
                <configuration>
                    <compilerId>groovy-eclipse-compiler</compilerId>
                    <source>${project.java.version}</source>
                    <target>${project.java.version}</target>
                    <debuglevel>lines,vars,source</debuglevel>
                    <debug>true</debug>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy-eclipse-compiler</artifactId>
                        <version>${groovy.eclipse.compiler}</version>
                    </dependency>
                    <dependency>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy-eclipse-batch</artifactId>
                        <version>${groovy.eclipse.batch}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>aspectj-maven-plugin</artifactId>
                <version>${aspectj.plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>test-compile</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <source>${project.java.version}</source>
                    <target>${project.java.version}</target>
                    <complianceLevel>${project.java.version}</complianceLevel>
                    <XnoInline>true</XnoInline>
                    <aspectLibraries>
                        <aspectLibrary>
                            <groupId>org.springframework</groupId>
                            <artifactId>spring-aspects</artifactId>
                        </aspectLibrary>
                        <aspectLibrary>
                            <groupId>org.zstack</groupId>
                            <artifactId>core</artifactId>
                        </aspectLibrary>
                        <aspectLibrary>
                            <groupId>org.zstack</groupId>
                            <artifactId>header</artifactId>
                        </aspectLibrary>
                    </aspectLibraries>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
